image: atlassian/default-image:3

pipelines:
  branches:
    dev:
      - step:
          name: 'Deployment to Staging'
          deployment: staging
          script:
            - echo "BEGIN deployment to staging..."
            - echo $user
            - pipe: atlassian/ssh-run:0.2.2
              variables:
                SSH_USER: $user
                SERVER: $host
                COMMAND: '~/deploy_scripts/nengine.sh'