# Documentation Generation System

A comprehensive documentation system that can merge multiple markdown files into a single unified document or generate an interconnected documentation site with navigation.

## Features

- **📄 Merged Documentation**: Combine all markdown files into a single cohesive document
- **🌐 Documentation Site**: Generate a static site with navigation, search, and responsive design
- **⚙️ Configurable**: JSON-based configuration for structure, styling, and output options
- **🔍 Search**: Client-side search functionality for the documentation site
- **📱 Responsive**: Mobile-friendly design that works on all devices
- **🎨 Customizable**: Configurable themes and styling options
- **👀 Watch Mode**: Automatic regeneration when files change
- **🚀 CI/CD Ready**: GitHub Actions workflow for automated generation and deployment

## Quick Start

### 1. Generate Documentation

```bash
# Generate both merged docs and site
npm run docs

# Or use the CLI directly
tsx scripts/docs-cli.ts both
```

### 2. Development Mode

```bash
# Watch for changes and regenerate automatically
npm run docs:watch

# Development mode with verbose output
npm run docs:dev
```

### 3. Serve Locally

```bash
# Using Make (recommended)
make docs-serve

# Or manually with Python
cd docs-site && python3 -m http.server 8000

# Or with Node.js
npx serve docs-site -p 3000
```

## Configuration

The system uses `docs-config.json` to define the documentation structure:

```json
{
  "title": "Notification Engine Documentation",
  "description": "Comprehensive documentation for the Notification Engine System",
  "version": "1.0.0",
  "structure": {
    "sections": [
      {
        "id": "overview",
        "title": "Overview",
        "description": "Introduction and getting started guide",
        "order": 1,
        "files": [
          {
            "id": "readme",
            "title": "Introduction",
            "file": "README.md",
            "description": "System overview and architecture",
            "order": 1
          }
        ]
      }
    ]
  }
}
```

## Available Commands

### NPM Scripts

```bash
npm run docs              # Generate both merged docs and site
npm run docs:merge        # Generate merged markdown only
npm run docs:site         # Generate documentation site only
npm run docs:watch        # Watch for changes and regenerate
npm run docs:dev          # Development mode with verbose output
npm run docs:build        # Build for production
npm run docs:clean        # Clean generated files
```

### Make Commands

```bash
make help                 # Show all available commands
make docs                 # Generate complete documentation
make docs-serve           # Generate and serve locally
make docs-watch           # Watch mode
make docs-validate        # Validate generated files
make docs-stats           # Show documentation statistics
```

### CLI Usage

```bash
# Basic usage
tsx scripts/docs-cli.ts [command] [config-file] [options]

# Commands
tsx scripts/docs-cli.ts merge     # Merged docs only
tsx scripts/docs-cli.ts site      # Site only
tsx scripts/docs-cli.ts both      # Both formats (default)

# Options
--output, -o <file>       # Output file for merged docs
--output-dir, -d <dir>    # Output directory for site
--watch, -w               # Watch for changes
--verbose, -v             # Verbose output
--no-toc                  # Disable table of contents
--no-search               # Disable search functionality
```

## File Structure

```
engine-system/
├── docs-config.json              # Configuration file
├── scripts/
│   ├── docs-cli.ts              # Main CLI interface
│   ├── docs-generator.ts        # Core generator (legacy)
│   ├── merge-docs.ts            # Markdown merger
│   └── generate-site.ts         # Site generator
├── docs/                        # Source documentation files
│   ├── API.md
│   ├── DATABASE.md
│   ├── DEPLOYMENT.md
│   ├── QUEUES.md
│   ├── TEMPLATES.md
│   ├── TESTING.md
│   └── TROUBLESHOOTING.md
├── DOCUMENTATION.md             # Generated merged documentation
├── docs-site/                   # Generated documentation site
│   ├── index.html
│   ├── assets/
│   │   ├── style.css
│   │   ├── highlight.css
│   │   └── script.js
│   ├── overview/
│   ├── api/
│   ├── architecture/
│   ├── development/
│   └── troubleshooting/
└── .github/workflows/docs.yml   # GitHub Actions workflow
```

## Generated Output

### Merged Documentation (`DOCUMENTATION.md`)

- Single markdown file with all content
- Automatic table of contents
- Section and file numbering
- Source file links
- Proper heading hierarchy

### Documentation Site (`docs-site/`)

- Responsive HTML site
- Interactive navigation
- Client-side search
- Code syntax highlighting
- Previous/next page navigation
- Mobile-friendly design

## Customization

### Styling

Modify the CSS generation in `scripts/generate-site.ts`:

```typescript
private generateCSS(): string {
  return `/* Your custom styles */`;
}
```

### Navigation

Update the navigation structure in `docs-config.json`:

```json
{
  "navigation": {
    "showSectionNumbers": true,
    "showFileNumbers": true,
    "generateToc": true,
    "maxTocDepth": 3
  }
}
```

### Search

Configure search options:

```json
{
  "output": {
    "includeSearch": true
  }
}
```

## Deployment

### GitHub Pages

The included GitHub Actions workflow automatically deploys to GitHub Pages on pushes to the main branch.

### Manual Deployment

```bash
# Build for production
npm run docs:build

# Deploy the docs-site/ directory to your hosting provider
```

### CI/CD Integration

```yaml
# In your CI/CD pipeline
- name: Generate Documentation
  run: |
    cd engine-system
    npm ci
    npm run docs:build
    
- name: Deploy Documentation
  # Deploy docs-site/ directory
```

## Troubleshooting

### Common Issues

1. **Files not found**: Check file paths in `docs-config.json`
2. **Build failures**: Ensure all dependencies are installed with `npm ci`
3. **Broken links**: Run `make docs-validate` to check for issues
4. **Watch mode not working**: Check file permissions and paths

### Debug Mode

```bash
# Enable verbose output
tsx scripts/docs-cli.ts both --verbose

# Check configuration
make docs-check-config

# Validate generated files
make docs-validate
```

## Contributing

1. Update documentation files in the `docs/` directory
2. Modify `docs-config.json` if adding new sections
3. Test changes with `npm run docs:dev`
4. Commit changes - documentation will auto-generate via GitHub Actions

## License

This documentation system is part of the Notification Engine project and follows the same license terms.
