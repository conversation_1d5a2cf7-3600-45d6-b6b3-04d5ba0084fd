import fs from 'fs/promises'
import path from 'path'
import Handlebars from 'handlebars'
import { templateRegistry } from './template-registry'
import { ZodSchema } from 'zod'
import { TRIGGER_TYPE } from './enums/triggers'
import { NotificationService } from './connectors/notification-service'

type TemplateName = keyof typeof templateRegistry

export class TemplateEngine {
    registry: typeof templateRegistry
    templates: Record<string, string> = {}
    layouts: Record<string, string> = {}

    constructor(
        private readonly templatesPath: string,
        private readonly layoutsPath?: string,
        registry = templateRegistry
    ) {
        this.registry = registry
    }

    async init() {
        // Register custom helpers that mimic handlebars-layouts
        this.registerLayoutHelpers()

        // Load layouts first (if path provided)
        if (this.layoutsPath) {
            await this.loadLayouts()
        }

        // Then load regular templates
        await this.loadTemplates()
        await this.checkTemplateRegistryCompleteness()
    }

    /**
     * Register helpers that mimic handlebars-layouts functionality
     */
    private registerLayoutHelpers() {
        // Store for blocks of content
        const blocks: Record<string, Record<string, string>> = {}

        // Helper to register a block of content
        Handlebars.registerHelper(
            'content',
            function (this: any, name, options) {
                // Store the block content
                const data = Handlebars.createFrame(options.data)
                const blockName = name.trim()

                if (!blocks[data.layoutName]) {
                    blocks[data.layoutName] = {}
                }

                // Store the block content
                blocks[data.layoutName][blockName] = options.fn(this)
                return null
            }
        )

        // Helper to retrieve a block of content
        Handlebars.registerHelper('block', function (this: any, name, options) {
            const data = options.data
            const layoutName = data.layoutName

            // Get the block content or use the default
            let content = ''
            if (blocks[layoutName]?.[name]) {
                content = blocks[layoutName][name]
            } else {
                // Use the default content if no block is defined
                content = options.fn(this)
            }

            return new Handlebars.SafeString(content)
        })

        // Helper to extend a layout
        Handlebars.registerHelper(
            'extend',
            function (this: any, name, options) {
                // Extract layout name and create a unique key for this template
                const layoutName = name.trim()
                const templateKey = Math.random().toString(36).substring(2, 15)

                // Add layout name to the data context
                const data = Handlebars.createFrame(options.data)
                data.layoutName = templateKey

                // Process child template content first
                options.fn(this, { data })

                // Get the layout partial
                const layout = Handlebars.partials[layoutName]
                if (!layout) {
                    throw new Error(
                        `Layout '${layoutName}' not found. Available layouts: ${Object.keys(Handlebars.partials).join(', ')}`
                    )
                }

                // Compile and execute the layout with the current context
                const compiledLayout =
                    typeof layout === 'function'
                        ? layout
                        : Handlebars.compile(layout)
                return new Handlebars.SafeString(compiledLayout(this, { data }))
            }
        )

        // Additional common helpers
        Handlebars.registerHelper('eq', function (a, b) {
            return a === b
        })

        Handlebars.registerHelper('formatDate', function (date, format) {
            if (!date) return ''
            const d = new Date(date)
            return d.toLocaleDateString()
        })
        Handlebars.registerHelper('year', function () {
            return new Date().getFullYear()
        })
    }

    /**
     * Load layout templates from the layouts directory
     */
    async loadLayouts(): Promise<void> {
        try {
            const files = await fs.readdir(this.layoutsPath!)
            for (const file of files) {
                if (file.endsWith('.hbs')) {
                    const name = path.basename(file, '.hbs')
                    const content = await fs.readFile(
                        path.join(this.layoutsPath!, file),
                        'utf8'
                    )
                    this.layouts[name] = content
                    // Register each layout as a partial
                    Handlebars.registerPartial(name, content)
                    console.log(`Registered layout: ${name}`)
                }
            }
            console.log(
                `✅ Loaded ${Object.keys(this.layouts).length} layout templates`
            )
        } catch (error) {
            console.error('Error loading layouts:', error)
            throw error
        }
    }

    /**
     * Load regular templates from the templates directory
     */
    async loadTemplates(): Promise<void> {
        try {
            const files = await fs.readdir(this.templatesPath)
            for (const file of files) {
                if (file.endsWith('.hbs')) {
                    const name = path.basename(file, '.hbs')
                    const content = await fs.readFile(
                        path.join(this.templatesPath, file),
                        'utf8'
                    )
                    this.templates[name] = content

                    // Register the template as a partial so it can be used by other templates
                    Handlebars.registerPartial(name, content)
                    console.log(`Registered template: ${name}`)
                }
            }
            console.log(
                `✅ Loaded ${Object.keys(this.templates).length} templates`
            )
        } catch (error) {
            console.error('Error loading templates:', error)
            throw error
        }
    }

    /**
     * Check that all templates in the registry have corresponding files
     */
    async checkTemplateRegistryCompleteness() {
        const registryKeys = Object.keys(this.registry)
        const missing = registryKeys.filter((key) => !this.templates[key])
        if (missing.length > 0) {
            console.warn(
                `⚠️ Missing template files for the following registered trigger types:\n- ${missing.join('\n- ')}`
            )
        } else {
            console.log('✅ All registered templates have matching .hbs files.')
        }
    }

    /**
     * Get schema for validation from registry
     */
    getSchema(templateName: TemplateName, channel: string): ZodSchema {
        const entry = this.registry[templateName]
        if (!entry) {
            throw new Error(`Cannot find template ${templateName}`)
        }
        const schema = entry.schema
        if (!schema) {
            throw new Error(`Cannot find schema for template ${templateName}`)
        }
        return schema
    }

    /**
     * Validate data against template schema
     */
    validate(templateName: TemplateName, channel: string, data: unknown): any {
        const schema = this.getSchema(templateName, channel)

        const result = schema.safeParse(data)
        if (!result.success) {
            throw new Error(
                `Validation failed for template ${templateName}: ${JSON.stringify(result.error.issues)}`
            )
        }
        return result.data
    }

    /**
     * Render a template with provided data
     */
    render(templateName: TemplateName, channel: string, data: unknown): string {
        // Get and check template
        const rawTemplate = this.templates[templateName]
        if (!rawTemplate) {
            throw new Error(`Cannot find template ${templateName}`)
        }

        // Validate data
        const validData = this.validate(templateName, channel, data)

        // Prepare template context
        const templateContext = {
            ...validData,
            title: this.registry[templateName].subject,
        }

        // Compile and execute the template
        try {
            const compiled = Handlebars.compile(rawTemplate)
            const result = compiled(templateContext)
            return result
        } catch (error) {
            console.error(`Error rendering template ${templateName}:`, error)
            throw error
        }
    }
}
