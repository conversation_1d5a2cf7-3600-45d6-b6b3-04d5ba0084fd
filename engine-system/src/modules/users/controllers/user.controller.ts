import { Request, Response } from 'express';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { IUserService } from '../interfaces/user-service.interface';
import { DeleteUserDto } from '../dto/delete-user.dto';
import { UserDeletionResponseDto } from '../dto/user-deletion-response.dto';
import { 
  BadRequestError, 
  createErrorResponse, 
  NotFoundError,
  InternalServerError 
} from '../../../api/utils/custom.errors';
import Logger from '../../../utils/logger';

const logger = Logger.getInstance();

export class UserController {
  constructor(private readonly userService: IUserService) {}

  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      logger.info('User deletion request received', { body: req.body });

      // Validate request body
      const deleteUserDto = plainToClass(DeleteUserDto, req.body);
      const validationErrors = await validate(deleteUserDto);

      if (validationErrors.length > 0) {
        const errorMessages = validationErrors
          .map(error => Object.values(error.constraints || {}).join(', '))
          .join('; ');
        throw new BadRequestError(`Validation failed: ${errorMessages}`);
      }

      const { userId, adminUserId } = deleteUserDto;

      // Perform user deletion with reassignment
      const deletionResult = await this.userService.deleteUserWithReassignment(userId, adminUserId);

      // Map to response DTO
      const response: UserDeletionResponseDto = {
        success: deletionResult.success,
        userId: deletionResult.userId,
        deletedDirectly: deletionResult.deletedDirectly,
        reassignmentSummary: deletionResult.reassignmentSummary ? {
          userId: deletionResult.reassignmentSummary.userId,
          adminUserId: deletionResult.reassignmentSummary.adminUserId,
          totalReassigned: deletionResult.reassignmentSummary.totalReassigned,
          results: deletionResult.reassignmentSummary.results.map(result => ({
            success: result.success,
            reassignedCount: result.reassignedCount,
            entityType: result.entityType,
            error: result.error
          })),
          timestamp: deletionResult.reassignmentSummary.timestamp
        } : undefined,
        error: deletionResult.error,
        timestamp: deletionResult.timestamp,
        message: this.generateResponseMessage(deletionResult)
      };

      if (!deletionResult.success) {
        res.status(400).json(response);
        return;
      }

      res.status(200).json(response);
      
    } catch (error) {
      logger.error('Error in user deletion controller:', error);
      return createErrorResponse(error, res);
    }
  }

  async checkUserConstraints(req: Request, res: Response): Promise<void> {
    try {
      const { userId } = req.params;

      if (!userId) {
        throw new BadRequestError('User ID is required');
      }

      const hasConstraints = await this.userService.hasConstrainingContent(userId);

      res.status(200).json({
        userId,
        hasConstrainingContent: hasConstraints,
        message: hasConstraints 
          ? 'User has content that would prevent direct deletion'
          : 'User can be deleted directly without constraints'
      });

    } catch (error) {
      logger.error('Error checking user constraints:', error);
      return createErrorResponse(error, res);
    }
  }

  private generateResponseMessage(deletionResult: any): string {
    if (!deletionResult.success) {
      return `User deletion failed: ${deletionResult.error}`;
    }

    if (deletionResult.deletedDirectly) {
      return `User ${deletionResult.userId} can be deleted directly without constraints`;
    }

    const totalReassigned = deletionResult.reassignmentSummary?.totalReassigned || 0;
    return `User ${deletionResult.userId} content successfully reassigned to admin. ${totalReassigned} items reassigned. User can now be safely deleted.`;
  }
}
