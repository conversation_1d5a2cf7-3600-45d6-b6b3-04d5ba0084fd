import {
    IUserService,
    UserDeletionResult,
} from '../interfaces/user-service.interface'
import { IUserRepository } from '../interfaces/user-repository.interface'
import { IUserReassignmentService } from '../interfaces/user-reassignment.interface'
import prisma from '../../../db'
import Logger from '../../../utils/logger'
import { UserOperationLogger } from '../utils/user-operation-logger'
import {
    UserOperationErrorHandler,
    UserOperationError,
    UserOperationErrorType,
} from '../utils/error-handler'

const logger = Logger.getInstance()

export class UserService implements IUserService {
    constructor(
        private readonly userRepository: IUserRepository,
        private readonly reassignmentService: IUserReassignmentService
    ) {}

    async deleteUserWithReassignment(
        userId: string,
        adminUserId: string
    ): Promise<UserDeletionResult> {
        const timestamp = new Date()

        try {
            UserOperationLogger.logUserDeletionStart(userId, adminUserId)

            // Validate inputs
            if (!userId || !adminUserId) {
                throw new UserOperationError(
                    UserOperationErrorType.VALIDATION_ERROR,
                    'Both userId and adminUserId are required',
                    userId,
                    adminUserId
                )
            }

            if (userId === adminUserId) {
                UserOperationLogger.logSecurityEvent(
                    'self_reassignment_attempt',
                    userId,
                    adminUserId
                )
                throw new UserOperationError(
                    UserOperationErrorType.SECURITY_ERROR,
                    'Cannot reassign user content to themselves',
                    userId,
                    adminUserId
                )
            }

            // Validate that the user exists
            const userExists = await this.userRepository.userExists(userId)
            if (!userExists) {
                return {
                    success: false,
                    userId,
                    deletedDirectly: false,
                    error: 'User not found or has no content in the system',
                    timestamp,
                }
            }

            // Validate admin user
            const adminIsValid =
                await this.userRepository.validateAdminUser(adminUserId)
            if (!adminIsValid) {
                return {
                    success: false,
                    userId,
                    deletedDirectly: false,
                    error: 'Invalid admin user ID provided',
                    timestamp,
                }
            }

            // Check if user has constraining content
            const hasConstraints = await this.hasConstrainingContent(userId)

            if (!hasConstraints) {
                // User has no constraining content, can be deleted directly
                logger.info(
                    `User ${userId} has no constraining content, deletion would succeed directly`
                )
                return {
                    success: true,
                    userId,
                    deletedDirectly: true,
                    timestamp,
                }
            }

            // User has constraining content, perform reassignment
            logger.info(
                `User ${userId} has constraining content, performing reassignment to admin ${adminUserId}`
            )

            const reassignmentSummary =
                await this.reassignmentService.reassignUserContent(
                    userId,
                    adminUserId
                )

            // Check if all reassignments were successful
            const allSuccessful = reassignmentSummary.results.every(
                (result) => result.success
            )

            if (!allSuccessful) {
                const failedOperations = reassignmentSummary.results
                    .filter((result) => !result.success)
                    .map((result) => `${result.entityType}: ${result.error}`)
                    .join(', ')

                return {
                    success: false,
                    userId,
                    deletedDirectly: false,
                    reassignmentSummary,
                    error: `Some reassignment operations failed: ${failedOperations}`,
                    timestamp,
                }
            }

            logger.info(
                `Successfully reassigned ${reassignmentSummary.totalReassigned} items from user ${userId} to admin ${adminUserId}`
            )

            return {
                success: true,
                userId,
                deletedDirectly: false,
                reassignmentSummary,
                timestamp,
            }
        } catch (error) {
            const userError = UserOperationErrorHandler.handleError(
                error,
                'user_deletion',
                userId,
                adminUserId
            )

            const result: UserDeletionResult = {
                success: false,
                userId,
                deletedDirectly: false,
                error: userError.message,
                timestamp,
            }

            UserOperationLogger.logUserDeletionResult(result)
            return result
        }
    }

    async hasConstrainingContent(userId: string): Promise<boolean> {
        try {
            const [templateCount, triggerCount, auditLogCount] =
                await Promise.all([
                    prisma.template.count({
                        where: {
                            OR: [{ createdBy: userId }, { updatedBy: userId }],
                            deletedAt: null,
                        },
                    }),
                    prisma.trigger.count({
                        where: {
                            OR: [{ createdBy: userId }, { updatedBy: userId }],
                            deletedAt: null,
                        },
                    }),
                    prisma.templateAuditLog.count({
                        where: {
                            performedBy: userId,
                        },
                    }),
                ])

            const hasConstraints =
                templateCount > 0 || triggerCount > 0 || auditLogCount > 0

            const details = {
                templateCount,
                triggerCount,
                auditLogCount,
            }

            UserOperationLogger.logConstraintCheck(
                userId,
                hasConstraints,
                details
            )

            return hasConstraints
        } catch (error) {
            logger.error(
                `Error checking constraining content for user ${userId}:`,
                error
            )
            return true // Assume constraints exist if we can't check
        }
    }
}
