import {
    IUserReassignmentService,
    ReassignmentResult,
    UserReassignmentSummary,
} from '../interfaces/user-reassignment.interface'
import prisma from '../../../db'
import Logger from '../../../utils/logger'
import { UserOperationLogger } from '../utils/user-operation-logger'

const logger = Logger.getInstance()

export class UserReassignmentService implements IUserReassignmentService {
    async reassignUserContent(
        userId: string,
        adminUserId: string
    ): Promise<UserReassignmentSummary> {
        UserOperationLogger.logReassignmentStart(userId, adminUserId)

        const results: ReassignmentResult[] = []
        let totalReassigned = 0

        try {
            // Reassign templates
            const templateResult = await this.reassignTemplates(
                userId,
                adminUserId
            )
            UserOperationLogger.logReassignmentResult(templateResult)
            results.push(templateResult)
            totalReassigned += templateResult.reassignedCount

            // Reassign triggers
            const triggerResult = await this.reassignTriggers(
                userId,
                adminUserId
            )
            UserOperationLogger.logReassignmentResult(triggerResult)
            results.push(triggerResult)
            totalReassigned += triggerResult.reassignedCount

            // Reassign template audit logs
            const auditLogResult = await this.reassignTemplateAuditLogs(
                userId,
                adminUserId
            )
            UserOperationLogger.logReassignmentResult(auditLogResult)
            results.push(auditLogResult)
            totalReassigned += auditLogResult.reassignedCount

            const summary: UserReassignmentSummary = {
                userId,
                adminUserId,
                totalReassigned,
                results,
                timestamp: new Date(),
            }

            UserOperationLogger.logReassignmentSummary(summary)
            return summary
        } catch (error) {
            logger.error(`Error during content reassignment:`, error)
            throw error
        }
    }

    async reassignTemplates(
        userId: string,
        adminUserId: string
    ): Promise<ReassignmentResult> {
        try {
            logger.info(
                `Reassigning templates from user ${userId} to admin ${adminUserId}`
            )

            // Update templates where user is creator
            const createdByUpdate = await prisma.template.updateMany({
                where: {
                    createdBy: userId,
                    deletedAt: null, // Only update non-deleted templates
                },
                data: {
                    createdBy: adminUserId,
                    updatedBy: adminUserId,
                    updatedAt: new Date(),
                },
            })

            // Update templates where user is last updater (but not creator)
            const updatedByUpdate = await prisma.template.updateMany({
                where: {
                    updatedBy: userId,
                    createdBy: { not: userId }, // Don't double-count
                    deletedAt: null,
                },
                data: {
                    updatedBy: adminUserId,
                    updatedAt: new Date(),
                },
            })

            const totalCount = createdByUpdate.count + updatedByUpdate.count

            logger.info(
                `Reassigned ${totalCount} templates (${createdByUpdate.count} created, ${updatedByUpdate.count} updated)`
            )

            return {
                success: true,
                reassignedCount: totalCount,
                entityType: 'templates',
            }
        } catch (error) {
            logger.error(`Error reassigning templates:`, error)
            return {
                success: false,
                reassignedCount: 0,
                entityType: 'templates',
                error: error instanceof Error ? error.message : 'Unknown error',
            }
        }
    }

    async reassignTriggers(
        userId: string,
        adminUserId: string
    ): Promise<ReassignmentResult> {
        try {
            logger.info(
                `Reassigning triggers from user ${userId} to admin ${adminUserId}`
            )

            // Update triggers where user is creator
            const createdByUpdate = await prisma.trigger.updateMany({
                where: {
                    createdBy: userId,
                    deletedAt: null, // Only update non-deleted triggers
                },
                data: {
                    createdBy: adminUserId,
                    updatedBy: adminUserId,
                    updatedAt: new Date(),
                },
            })

            // Update triggers where user is last updater (but not creator)
            const updatedByUpdate = await prisma.trigger.updateMany({
                where: {
                    updatedBy: userId,
                    createdBy: { not: userId }, // Don't double-count
                    deletedAt: null,
                },
                data: {
                    updatedBy: adminUserId,
                    updatedAt: new Date(),
                },
            })

            const totalCount = createdByUpdate.count + updatedByUpdate.count

            logger.info(
                `Reassigned ${totalCount} triggers (${createdByUpdate.count} created, ${updatedByUpdate.count} updated)`
            )

            return {
                success: true,
                reassignedCount: totalCount,
                entityType: 'triggers',
            }
        } catch (error) {
            logger.error(`Error reassigning triggers:`, error)
            return {
                success: false,
                reassignedCount: 0,
                entityType: 'triggers',
                error: error instanceof Error ? error.message : 'Unknown error',
            }
        }
    }

    async reassignTemplateAuditLogs(
        userId: string,
        adminUserId: string
    ): Promise<ReassignmentResult> {
        try {
            logger.info(
                `Reassigning template audit logs from user ${userId} to admin ${adminUserId}`
            )

            const updateResult = await prisma.templateAuditLog.updateMany({
                where: {
                    performedBy: userId,
                },
                data: {
                    performedBy: adminUserId,
                },
            })

            logger.info(`Reassigned ${updateResult.count} template audit logs`)

            return {
                success: true,
                reassignedCount: updateResult.count,
                entityType: 'template_audit_logs',
            }
        } catch (error) {
            logger.error(`Error reassigning template audit logs:`, error)
            return {
                success: false,
                reassignedCount: 0,
                entityType: 'template_audit_logs',
                error: error instanceof Error ? error.message : 'Unknown error',
            }
        }
    }
}
