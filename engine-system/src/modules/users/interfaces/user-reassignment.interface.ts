export interface ReassignmentResult {
  success: boolean;
  reassignedCount: number;
  entityType: string;
  error?: string;
}

export interface UserReassignmentSummary {
  userId: string;
  adminUserId: string;
  totalReassigned: number;
  results: ReassignmentResult[];
  timestamp: Date;
}

export interface IUserReassignmentService {
  /**
   * Reassign all user-created content to an admin user
   * @param userId - The user whose content should be reassigned
   * @param adminUserId - The admin user to receive the content
   * @returns Promise<UserReassignmentSummary> - Summary of reassignment operations
   */
  reassignUserContent(userId: string, adminUserId: string): Promise<UserReassignmentSummary>;

  /**
   * Reassign templates created by a user to an admin user
   * @param userId - The user whose templates should be reassigned
   * @param adminUserId - The admin user to receive the templates
   * @returns Promise<ReassignmentResult> - Result of the reassignment operation
   */
  reassignTemplates(userId: string, adminUserId: string): Promise<ReassignmentResult>;

  /**
   * Reassign triggers created by a user to an admin user
   * @param userId - The user whose triggers should be reassigned
   * @param adminUserId - The admin user to receive the triggers
   * @returns Promise<ReassignmentResult> - Result of the reassignment operation
   */
  reassignTriggers(userId: string, adminUserId: string): Promise<ReassignmentResult>;

  /**
   * Reassign template audit logs performed by a user to an admin user
   * @param userId - The user whose audit logs should be reassigned
   * @param adminUserId - The admin user to receive the audit logs
   * @returns Promise<ReassignmentResult> - Result of the reassignment operation
   */
  reassignTemplateAuditLogs(userId: string, adminUserId: string): Promise<ReassignmentResult>;
}
