import { UserReassignmentSummary } from './user-reassignment.interface';

export interface UserDeletionResult {
  success: boolean;
  userId: string;
  deletedDirectly: boolean;
  reassignmentSummary?: UserReassignmentSummary;
  error?: string;
  timestamp: Date;
}

export interface IUserService {
  /**
   * Delete a user, reassigning their content to an admin user if foreign key constraints prevent direct deletion
   * @param userId - The user ID to delete
   * @param adminUserId - The admin user ID to receive reassigned content
   * @returns Promise<UserDeletionResult> - Result of the deletion operation
   */
  deleteUserWithReassignment(userId: string, adminUserId: string): Promise<UserDeletionResult>;

  /**
   * Check if a user has any content that would prevent deletion
   * @param userId - The user ID to check
   * @returns Promise<boolean> - True if user has content that would cause foreign key constraints
   */
  hasConstrainingContent(userId: string): Promise<boolean>;
}
