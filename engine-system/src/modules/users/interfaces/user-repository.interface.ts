export interface IUserRepository {
  /**
   * Check if a user exists in the external system
   * @param userId - The user ID to check
   * @returns Promise<boolean> - True if user exists
   */
  userExists(userId: string): Promise<boolean>;

  /**
   * Validate that an admin user exists and has proper permissions
   * @param adminUserId - The admin user ID to validate
   * @returns Promise<boolean> - True if admin user is valid
   */
  validateAdminUser(adminUserId: string): Promise<boolean>;
}
