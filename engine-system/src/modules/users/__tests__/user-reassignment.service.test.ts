import { UserReassignmentService } from '../services/user-reassignment.service';

// Mock Prisma
jest.mock('../../../db', () => ({
  template: {
    updateMany: jest.fn(),
  },
  trigger: {
    updateMany: jest.fn(),
  },
  templateAuditLog: {
    updateMany: jest.fn(),
  },
}));

describe('UserReassignmentService', () => {
  let service: UserReassignmentService;

  beforeEach(() => {
    service = new UserReassignmentService();
    jest.clearAllMocks();
  });

  describe('reassignUserContent', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';
    const adminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should successfully reassign all user content', async () => {
      const prisma = require('../../../db');
      
      // Mock successful updates
      prisma.template.updateMany
        .mockResolvedValueOnce({ count: 3 }) // createdBy update
        .mockResolvedValueOnce({ count: 2 }); // updatedBy update
      
      prisma.trigger.updateMany
        .mockResolvedValueOnce({ count: 1 }) // createdBy update
        .mockResolvedValueOnce({ count: 1 }); // updatedBy update
      
      prisma.templateAuditLog.updateMany
        .mockResolvedValueOnce({ count: 4 }); // performedBy update

      const result = await service.reassignUserContent(userId, adminUserId);

      expect(result.userId).toBe(userId);
      expect(result.adminUserId).toBe(adminUserId);
      expect(result.totalReassigned).toBe(11); // 3+2+1+1+4
      expect(result.results).toHaveLength(3);
      
      // Check templates result
      expect(result.results[0].success).toBe(true);
      expect(result.results[0].entityType).toBe('templates');
      expect(result.results[0].reassignedCount).toBe(5); // 3+2
      
      // Check triggers result
      expect(result.results[1].success).toBe(true);
      expect(result.results[1].entityType).toBe('triggers');
      expect(result.results[1].reassignedCount).toBe(2); // 1+1
      
      // Check audit logs result
      expect(result.results[2].success).toBe(true);
      expect(result.results[2].entityType).toBe('template_audit_logs');
      expect(result.results[2].reassignedCount).toBe(4);
    });

    it('should handle partial failures gracefully', async () => {
      const prisma = require('../../../db');
      
      // Mock template success
      prisma.template.updateMany
        .mockResolvedValueOnce({ count: 3 })
        .mockResolvedValueOnce({ count: 2 });
      
      // Mock trigger failure
      prisma.trigger.updateMany
        .mockRejectedValueOnce(new Error('Database error'));
      
      // Mock audit log success
      prisma.templateAuditLog.updateMany
        .mockResolvedValueOnce({ count: 4 });

      const result = await service.reassignUserContent(userId, adminUserId);

      expect(result.totalReassigned).toBe(9); // 5+0+4
      expect(result.results).toHaveLength(3);
      
      // Templates should succeed
      expect(result.results[0].success).toBe(true);
      expect(result.results[0].reassignedCount).toBe(5);
      
      // Triggers should fail
      expect(result.results[1].success).toBe(false);
      expect(result.results[1].reassignedCount).toBe(0);
      expect(result.results[1].error).toBe('Database error');
      
      // Audit logs should succeed
      expect(result.results[2].success).toBe(true);
      expect(result.results[2].reassignedCount).toBe(4);
    });
  });

  describe('reassignTemplates', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';
    const adminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should successfully reassign templates', async () => {
      const prisma = require('../../../db');
      
      prisma.template.updateMany
        .mockResolvedValueOnce({ count: 3 }) // createdBy update
        .mockResolvedValueOnce({ count: 2 }); // updatedBy update

      const result = await service.reassignTemplates(userId, adminUserId);

      expect(result.success).toBe(true);
      expect(result.reassignedCount).toBe(5);
      expect(result.entityType).toBe('templates');
      expect(result.error).toBeUndefined();

      // Verify the correct queries were made
      expect(prisma.template.updateMany).toHaveBeenCalledTimes(2);
      
      // Check createdBy update
      expect(prisma.template.updateMany).toHaveBeenNthCalledWith(1, {
        where: {
          createdBy: userId,
          deletedAt: null
        },
        data: {
          createdBy: adminUserId,
          updatedBy: adminUserId,
          updatedAt: expect.any(Date)
        }
      });
      
      // Check updatedBy update
      expect(prisma.template.updateMany).toHaveBeenNthCalledWith(2, {
        where: {
          updatedBy: userId,
          createdBy: { not: userId },
          deletedAt: null
        },
        data: {
          updatedBy: adminUserId,
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should handle database errors', async () => {
      const prisma = require('../../../db');
      
      prisma.template.updateMany.mockRejectedValueOnce(new Error('Database connection failed'));

      const result = await service.reassignTemplates(userId, adminUserId);

      expect(result.success).toBe(false);
      expect(result.reassignedCount).toBe(0);
      expect(result.entityType).toBe('templates');
      expect(result.error).toBe('Database connection failed');
    });
  });

  describe('reassignTriggers', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';
    const adminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should successfully reassign triggers', async () => {
      const prisma = require('../../../db');
      
      prisma.trigger.updateMany
        .mockResolvedValueOnce({ count: 1 }) // createdBy update
        .mockResolvedValueOnce({ count: 2 }); // updatedBy update

      const result = await service.reassignTriggers(userId, adminUserId);

      expect(result.success).toBe(true);
      expect(result.reassignedCount).toBe(3);
      expect(result.entityType).toBe('triggers');

      // Verify the correct queries were made
      expect(prisma.trigger.updateMany).toHaveBeenCalledTimes(2);
    });
  });

  describe('reassignTemplateAuditLogs', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';
    const adminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should successfully reassign template audit logs', async () => {
      const prisma = require('../../../db');
      
      prisma.templateAuditLog.updateMany.mockResolvedValueOnce({ count: 7 });

      const result = await service.reassignTemplateAuditLogs(userId, adminUserId);

      expect(result.success).toBe(true);
      expect(result.reassignedCount).toBe(7);
      expect(result.entityType).toBe('template_audit_logs');

      // Verify the correct query was made
      expect(prisma.templateAuditLog.updateMany).toHaveBeenCalledWith({
        where: {
          performedBy: userId
        },
        data: {
          performedBy: adminUserId
        }
      });
    });
  });
});
