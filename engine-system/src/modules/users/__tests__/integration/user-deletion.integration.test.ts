import request from 'supertest';
import { Express } from 'express';
import prisma from '../../../../db';

// This would be your actual app setup
// import { createApp } from '../../../../app';

describe('User Deletion Integration Tests', () => {
  let app: Express;
  
  beforeAll(async () => {
    // app = createApp();
    // Set up test database connection
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.templateAuditLog.deleteMany({});
    await prisma.template.deleteMany({});
    await prisma.trigger.deleteMany({});
  });

  describe('DELETE /users', () => {
    const testUserId = '123e4567-e89b-12d3-a456-************';
    const testAdminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should successfully reassign user content and allow deletion', async () => {
      // Create test data
      const trigger = await prisma.trigger.create({
        data: {
          name: 'test-trigger',
          triggerTypeId: 1,
          createdBy: testUserId,
          updatedBy: testUserId,
        }
      });

      const template = await prisma.template.create({
        data: {
          name: 'test-template',
          body: 'Test template body',
          channelId: 1,
          contentTypeId: 1,
          createdBy: testUserId,
          updatedBy: testUserId,
        }
      });

      await prisma.templateAuditLog.create({
        data: {
          templateId: template.id,
          action: 'created',
          performedBy: testUserId,
        }
      });

      // Make the deletion request
      const response = await request(app)
        .delete('/users')
        .send({
          userId: testUserId,
          adminUserId: testAdminUserId
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.deletedDirectly).toBe(false);
      expect(response.body.reassignmentSummary).toBeDefined();
      expect(response.body.reassignmentSummary.totalReassigned).toBeGreaterThan(0);

      // Verify that content was reassigned
      const updatedTrigger = await prisma.trigger.findUnique({
        where: { id: trigger.id }
      });
      expect(updatedTrigger?.createdBy).toBe(testAdminUserId);
      expect(updatedTrigger?.updatedBy).toBe(testAdminUserId);

      const updatedTemplate = await prisma.template.findUnique({
        where: { id: template.id }
      });
      expect(updatedTemplate?.createdBy).toBe(testAdminUserId);
      expect(updatedTemplate?.updatedBy).toBe(testAdminUserId);

      const auditLogs = await prisma.templateAuditLog.findMany({
        where: { templateId: template.id }
      });
      expect(auditLogs[0].performedBy).toBe(testAdminUserId);
    });

    it('should handle direct deletion when no constraints exist', async () => {
      const response = await request(app)
        .delete('/users')
        .send({
          userId: testUserId,
          adminUserId: testAdminUserId
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.deletedDirectly).toBe(true);
      expect(response.body.reassignmentSummary).toBeUndefined();
    });

    it('should return 400 for invalid user ID format', async () => {
      const response = await request(app)
        .delete('/users')
        .send({
          userId: 'invalid-uuid',
          adminUserId: testAdminUserId
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Validation failed');
    });

    it('should return 400 when trying to reassign to self', async () => {
      const response = await request(app)
        .delete('/users')
        .send({
          userId: testUserId,
          adminUserId: testUserId
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Cannot reassign user content to themselves');
    });
  });

  describe('GET /users/:userId/constraints', () => {
    const testUserId = '123e4567-e89b-12d3-a456-************';

    it('should return true when user has constraining content', async () => {
      // Create test data
      await prisma.trigger.create({
        data: {
          name: 'test-trigger-constraint',
          triggerTypeId: 1,
          createdBy: testUserId,
        }
      });

      const response = await request(app)
        .get(`/users/${testUserId}/constraints`)
        .expect(200);

      expect(response.body.hasConstrainingContent).toBe(true);
      expect(response.body.message).toContain('prevent direct deletion');
    });

    it('should return false when user has no constraining content', async () => {
      const response = await request(app)
        .get(`/users/${testUserId}/constraints`)
        .expect(200);

      expect(response.body.hasConstrainingContent).toBe(false);
      expect(response.body.message).toContain('can be deleted directly');
    });

    it('should return 400 for missing user ID', async () => {
      const response = await request(app)
        .get('/users//constraints')
        .expect(400);

      expect(response.body.error).toContain('User ID is required');
    });
  });
});
