import { UserService } from '../services/user.service';
import { UserRepository } from '../repositories/user.repository';
import { UserReassignmentService } from '../services/user-reassignment.service';
import { UserOperationErrorType } from '../utils/error-handler';

// Mock dependencies
jest.mock('../repositories/user.repository');
jest.mock('../services/user-reassignment.service');
jest.mock('../../../db', () => ({
  template: {
    count: jest.fn(),
  },
  trigger: {
    count: jest.fn(),
  },
  templateAuditLog: {
    count: jest.fn(),
  },
}));

describe('UserService', () => {
  let userService: UserService;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockReassignmentService: jest.Mocked<UserReassignmentService>;

  beforeEach(() => {
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;
    mockReassignmentService = new UserReassignmentService() as jest.Mocked<UserReassignmentService>;
    userService = new UserService(mockUserRepository, mockReassignmentService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('deleteUserWithReassignment', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';
    const adminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should throw validation error when userId is missing', async () => {
      const result = await userService.deleteUserWithReassignment('', adminUserId);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Both userId and adminUserId are required');
    });

    it('should throw validation error when adminUserId is missing', async () => {
      const result = await userService.deleteUserWithReassignment(userId, '');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Both userId and adminUserId are required');
    });

    it('should throw security error when trying to reassign to self', async () => {
      const result = await userService.deleteUserWithReassignment(userId, userId);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Cannot reassign user content to themselves');
    });

    it('should return error when user does not exist', async () => {
      mockUserRepository.userExists.mockResolvedValue(false);
      
      const result = await userService.deleteUserWithReassignment(userId, adminUserId);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('User not found or has no content in the system');
    });

    it('should return error when admin user is invalid', async () => {
      mockUserRepository.userExists.mockResolvedValue(true);
      mockUserRepository.validateAdminUser.mockResolvedValue(false);
      
      const result = await userService.deleteUserWithReassignment(userId, adminUserId);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid admin user ID provided');
    });

    it('should return success for direct deletion when no constraints exist', async () => {
      mockUserRepository.userExists.mockResolvedValue(true);
      mockUserRepository.validateAdminUser.mockResolvedValue(true);
      
      // Mock hasConstrainingContent to return false
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(0);
      prisma.trigger.count.mockResolvedValue(0);
      prisma.templateAuditLog.count.mockResolvedValue(0);
      
      const result = await userService.deleteUserWithReassignment(userId, adminUserId);
      
      expect(result.success).toBe(true);
      expect(result.deletedDirectly).toBe(true);
      expect(result.reassignmentSummary).toBeUndefined();
    });

    it('should perform reassignment when constraints exist', async () => {
      mockUserRepository.userExists.mockResolvedValue(true);
      mockUserRepository.validateAdminUser.mockResolvedValue(true);
      
      // Mock hasConstrainingContent to return true
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(5);
      prisma.trigger.count.mockResolvedValue(3);
      prisma.templateAuditLog.count.mockResolvedValue(2);
      
      const mockReassignmentSummary = {
        userId,
        adminUserId,
        totalReassigned: 10,
        results: [
          { success: true, reassignedCount: 5, entityType: 'templates' },
          { success: true, reassignedCount: 3, entityType: 'triggers' },
          { success: true, reassignedCount: 2, entityType: 'template_audit_logs' }
        ],
        timestamp: new Date()
      };
      
      mockReassignmentService.reassignUserContent.mockResolvedValue(mockReassignmentSummary);
      
      const result = await userService.deleteUserWithReassignment(userId, adminUserId);
      
      expect(result.success).toBe(true);
      expect(result.deletedDirectly).toBe(false);
      expect(result.reassignmentSummary).toBeDefined();
      expect(result.reassignmentSummary?.totalReassigned).toBe(10);
      expect(mockReassignmentService.reassignUserContent).toHaveBeenCalledWith(userId, adminUserId);
    });

    it('should return error when some reassignments fail', async () => {
      mockUserRepository.userExists.mockResolvedValue(true);
      mockUserRepository.validateAdminUser.mockResolvedValue(true);
      
      // Mock hasConstrainingContent to return true
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(5);
      prisma.trigger.count.mockResolvedValue(3);
      prisma.templateAuditLog.count.mockResolvedValue(2);
      
      const mockReassignmentSummary = {
        userId,
        adminUserId,
        totalReassigned: 5,
        results: [
          { success: true, reassignedCount: 5, entityType: 'templates' },
          { success: false, reassignedCount: 0, entityType: 'triggers', error: 'Database error' },
          { success: true, reassignedCount: 2, entityType: 'template_audit_logs' }
        ],
        timestamp: new Date()
      };
      
      mockReassignmentService.reassignUserContent.mockResolvedValue(mockReassignmentSummary);
      
      const result = await userService.deleteUserWithReassignment(userId, adminUserId);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Some reassignment operations failed');
      expect(result.error).toContain('triggers: Database error');
    });
  });

  describe('hasConstrainingContent', () => {
    const userId = '123e4567-e89b-12d3-a456-426614174000';

    it('should return false when user has no content', async () => {
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(0);
      prisma.trigger.count.mockResolvedValue(0);
      prisma.templateAuditLog.count.mockResolvedValue(0);
      
      const result = await userService.hasConstrainingContent(userId);
      
      expect(result).toBe(false);
    });

    it('should return true when user has templates', async () => {
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(1);
      prisma.trigger.count.mockResolvedValue(0);
      prisma.templateAuditLog.count.mockResolvedValue(0);
      
      const result = await userService.hasConstrainingContent(userId);
      
      expect(result).toBe(true);
    });

    it('should return true when user has triggers', async () => {
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(0);
      prisma.trigger.count.mockResolvedValue(1);
      prisma.templateAuditLog.count.mockResolvedValue(0);
      
      const result = await userService.hasConstrainingContent(userId);
      
      expect(result).toBe(true);
    });

    it('should return true when user has audit logs', async () => {
      const prisma = require('../../../db');
      prisma.template.count.mockResolvedValue(0);
      prisma.trigger.count.mockResolvedValue(0);
      prisma.templateAuditLog.count.mockResolvedValue(1);
      
      const result = await userService.hasConstrainingContent(userId);
      
      expect(result).toBe(true);
    });

    it('should return true when database query fails', async () => {
      const prisma = require('../../../db');
      prisma.template.count.mockRejectedValue(new Error('Database error'));
      
      const result = await userService.hasConstrainingContent(userId);
      
      expect(result).toBe(true); // Should assume constraints exist on error
    });
  });
});
