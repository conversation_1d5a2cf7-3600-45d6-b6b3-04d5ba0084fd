import { Request, Response } from 'express';
import { UserController } from '../controllers/user.controller';
import { UserService } from '../services/user.service';

// Mock dependencies
jest.mock('../services/user.service');

describe('UserController', () => {
  let controller: UserController;
  let mockUserService: jest.Mocked<UserService>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    mockUserService = new UserService({} as any, {} as any) as jest.Mocked<UserService>;
    controller = new UserController(mockUserService);

    mockRequest = {
      body: {},
      params: {}
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe('deleteUser', () => {
    const validUserId = '123e4567-e89b-12d3-a456-426614174000';
    const validAdminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

    it('should successfully delete user with reassignment', async () => {
      mockRequest.body = {
        userId: validUserId,
        adminUserId: validAdminUserId
      };

      const mockDeletionResult = {
        success: true,
        userId: validUserId,
        deletedDirectly: false,
        reassignmentSummary: {
          userId: validUserId,
          adminUserId: validAdminUserId,
          totalReassigned: 10,
          results: [
            { success: true, reassignedCount: 5, entityType: 'templates' },
            { success: true, reassignedCount: 3, entityType: 'triggers' },
            { success: true, reassignedCount: 2, entityType: 'template_audit_logs' }
          ],
          timestamp: new Date()
        },
        timestamp: new Date()
      };

      mockUserService.deleteUserWithReassignment.mockResolvedValue(mockDeletionResult);

      await controller.deleteUser(mockRequest as Request, mockResponse as Response);

      expect(mockUserService.deleteUserWithReassignment).toHaveBeenCalledWith(validUserId, validAdminUserId);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          userId: validUserId,
          deletedDirectly: false,
          reassignmentSummary: expect.objectContaining({
            totalReassigned: 10
          }),
          message: expect.stringContaining('content successfully reassigned')
        })
      );
    });

    it('should handle direct deletion without reassignment', async () => {
      mockRequest.body = {
        userId: validUserId,
        adminUserId: validAdminUserId
      };

      const mockDeletionResult = {
        success: true,
        userId: validUserId,
        deletedDirectly: true,
        timestamp: new Date()
      };

      mockUserService.deleteUserWithReassignment.mockResolvedValue(mockDeletionResult);

      await controller.deleteUser(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: true,
          deletedDirectly: true,
          message: expect.stringContaining('can be deleted directly without constraints')
        })
      );
    });

    it('should return 400 for validation errors', async () => {
      mockRequest.body = {
        userId: 'invalid-uuid',
        adminUserId: validAdminUserId
      };

      await controller.deleteUser(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.stringContaining('Validation failed')
        })
      );
    });

    it('should return 400 for deletion failures', async () => {
      mockRequest.body = {
        userId: validUserId,
        adminUserId: validAdminUserId
      };

      const mockDeletionResult = {
        success: false,
        userId: validUserId,
        deletedDirectly: false,
        error: 'User not found',
        timestamp: new Date()
      };

      mockUserService.deleteUserWithReassignment.mockResolvedValue(mockDeletionResult);

      await controller.deleteUser(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'User not found',
          message: expect.stringContaining('User deletion failed')
        })
      );
    });

    it('should handle missing request body', async () => {
      mockRequest.body = {};

      await controller.deleteUser(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });

    it('should handle service errors', async () => {
      mockRequest.body = {
        userId: validUserId,
        adminUserId: validAdminUserId
      };

      mockUserService.deleteUserWithReassignment.mockRejectedValue(new Error('Database connection failed'));

      await controller.deleteUser(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });
  });

  describe('checkUserConstraints', () => {
    const validUserId = '123e4567-e89b-12d3-a456-426614174000';

    it('should return constraint information for user with constraints', async () => {
      mockRequest.params = { userId: validUserId };
      mockUserService.hasConstrainingContent.mockResolvedValue(true);

      await controller.checkUserConstraints(mockRequest as Request, mockResponse as Response);

      expect(mockUserService.hasConstrainingContent).toHaveBeenCalledWith(validUserId);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        userId: validUserId,
        hasConstrainingContent: true,
        message: 'User has content that would prevent direct deletion'
      });
    });

    it('should return constraint information for user without constraints', async () => {
      mockRequest.params = { userId: validUserId };
      mockUserService.hasConstrainingContent.mockResolvedValue(false);

      await controller.checkUserConstraints(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        userId: validUserId,
        hasConstrainingContent: false,
        message: 'User can be deleted directly without constraints'
      });
    });

    it('should return 400 when userId is missing', async () => {
      mockRequest.params = {};

      await controller.checkUserConstraints(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });

    it('should handle service errors', async () => {
      mockRequest.params = { userId: validUserId };
      mockUserService.hasConstrainingContent.mockRejectedValue(new Error('Database error'));

      await controller.checkUserConstraints(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });
  });
});
