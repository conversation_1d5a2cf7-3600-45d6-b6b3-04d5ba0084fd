import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { UserService } from '../services/user.service';
import { UserRepository } from '../repositories/user.repository';
import { UserReassignmentService } from '../services/user-reassignment.service';

const userRouter = Router();

// Initialize dependencies
const userRepository = new UserRepository();
const reassignmentService = new UserReassignmentService();
const userService = new UserService(userRepository, reassignmentService);
const userController = new UserController(userService);

/**
 * @route DELETE /users
 * @description Delete a user with automatic content reassignment to admin user
 * @body {userId: string, adminUserId: string}
 * @returns {UserDeletionResponseDto}
 */
userRouter.delete('/', (req, res) => userController.deleteUser(req, res));

/**
 * @route GET /users/:userId/constraints
 * @description Check if a user has content that would prevent direct deletion
 * @param userId - The user ID to check
 * @returns {hasConstrainingContent: boolean, message: string}
 */
userRouter.get('/:userId/constraints', (req, res) => userController.checkUserConstraints(req, res));

export { userRouter };
