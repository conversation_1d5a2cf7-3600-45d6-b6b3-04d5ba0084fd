# User Management Module

This module provides graceful user deletion functionality that automatically handles foreign key constraints by reassigning user-created content to an admin user before deletion.

## Overview

When attempting to delete a user who has created content (templates, triggers, audit logs) that would cause foreign key constraint violations, the system automatically:

1. Validates the user and admin user
2. Checks for constraining content
3. Reassigns all user-created content to the specified admin user
4. Returns a detailed summary of the reassignment operation

## Architecture

The module follows SOLID principles with clear separation of concerns:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic for user deletion and content reassignment
- **Repositories**: Handle data access and external system integration
- **Interfaces**: Define contracts for dependency injection
- **DTOs**: Data transfer objects for request/response validation
- **Utils**: Logging and error handling utilities

## API Endpoints

### Delete User with Reassignment

```http
DELETE /users
Content-Type: application/json

{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "adminUserId": "987fcdeb-51a2-43d1-b789-123456789abc"
}
```

**Response (Success - Direct Deletion):**
```json
{
  "success": true,
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "deletedDirectly": true,
  "timestamp": "2025-06-18T10:30:00.000Z",
  "message": "User can be deleted directly without constraints"
}
```

**Response (Success - With Reassignment):**
```json
{
  "success": true,
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "deletedDirectly": false,
  "reassignmentSummary": {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "adminUserId": "987fcdeb-51a2-43d1-b789-123456789abc",
    "totalReassigned": 15,
    "results": [
      {
        "success": true,
        "reassignedCount": 8,
        "entityType": "templates"
      },
      {
        "success": true,
        "reassignedCount": 5,
        "entityType": "triggers"
      },
      {
        "success": true,
        "reassignedCount": 2,
        "entityType": "template_audit_logs"
      }
    ],
    "timestamp": "2025-06-18T10:30:00.000Z"
  },
  "timestamp": "2025-06-18T10:30:00.000Z",
  "message": "User content successfully reassigned to admin. 15 items reassigned. User can now be safely deleted."
}
```

### Check User Constraints

```http
GET /users/{userId}/constraints
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "hasConstrainingContent": true,
  "message": "User has content that would prevent direct deletion"
}
```

## Content Reassignment

The system reassigns the following user-created content:

### Templates
- `createdBy` field → admin user
- `updatedBy` field → admin user (when user was last updater)
- `updatedAt` field → current timestamp

### Triggers
- `createdBy` field → admin user
- `updatedBy` field → admin user (when user was last updater)
- `updatedAt` field → current timestamp

### Template Audit Logs
- `performedBy` field → admin user

## Error Handling

The system provides comprehensive error handling with specific error types:

- **VALIDATION_ERROR**: Invalid input data
- **USER_NOT_FOUND**: User doesn't exist or has no content
- **ADMIN_INVALID**: Invalid admin user ID
- **SECURITY_ERROR**: Security violations (e.g., self-reassignment)
- **CONSTRAINT_ERROR**: Database constraint violations
- **DATABASE_ERROR**: Database operation failures

## Logging

Comprehensive logging is provided for:

- User deletion process start/completion
- Content reassignment operations
- Constraint checks
- Security events
- Database errors
- Validation failures

## Usage Examples

### Basic Usage

```typescript
import { UserService } from './services/user.service';
import { UserRepository } from './repositories/user.repository';
import { UserReassignmentService } from './services/user-reassignment.service';

// Initialize services
const userRepository = new UserRepository();
const reassignmentService = new UserReassignmentService();
const userService = new UserService(userRepository, reassignmentService);

// Delete user with reassignment
const result = await userService.deleteUserWithReassignment(
  '123e4567-e89b-12d3-a456-426614174000',
  '987fcdeb-51a2-43d1-b789-123456789abc'
);

if (result.success) {
  console.log(`User deletion completed. Direct: ${result.deletedDirectly}`);
  if (result.reassignmentSummary) {
    console.log(`Reassigned ${result.reassignmentSummary.totalReassigned} items`);
  }
} else {
  console.error(`User deletion failed: ${result.error}`);
}
```

### Check Constraints Before Deletion

```typescript
const hasConstraints = await userService.hasConstrainingContent(userId);
if (hasConstraints) {
  console.log('User has content that requires reassignment');
} else {
  console.log('User can be deleted directly');
}
```

## Testing

The module includes comprehensive tests:

- **Unit Tests**: Test individual service methods and business logic
- **Integration Tests**: Test complete API workflows with database
- **Controller Tests**: Test HTTP request/response handling

Run tests:
```bash
npm test src/modules/users
```

## Security Considerations

- User IDs must be valid UUIDs
- Admin users are validated before reassignment
- Self-reassignment is prevented
- All operations are logged for audit trails
- Database transactions ensure data consistency

## Configuration

The module uses the existing Prisma database connection and logging infrastructure. No additional configuration is required.

## Future Enhancements

- Support for additional entity types
- Batch user deletion operations
- Configurable reassignment policies
- Integration with external user management systems
- Soft delete support for user records
