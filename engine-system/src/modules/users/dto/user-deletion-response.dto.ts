export class ReassignmentResultDto {
  success: boolean;
  reassignedCount: number;
  entityType: string;
  error?: string;
}

export class UserReassignmentSummaryDto {
  userId: string;
  adminUserId: string;
  totalReassigned: number;
  results: ReassignmentResultDto[];
  timestamp: Date;
}

export class UserDeletionResponseDto {
  success: boolean;
  userId: string;
  deletedDirectly: boolean;
  reassignmentSummary?: UserReassignmentSummaryDto;
  error?: string;
  timestamp: Date;
  message: string;
}
