import Logger from '../../../utils/logger';
import { UserReassignmentSummary, ReassignmentResult } from '../interfaces/user-reassignment.interface';
import { UserDeletionResult } from '../interfaces/user-service.interface';

const logger = Logger.getInstance();

export class UserOperationLogger {
  
  static logReassignmentStart(userId: string, adminUserId: string): void {
    logger.info('=== USER CONTENT REASSIGNMENT STARTED ===', {
      operation: 'user_content_reassignment',
      userId,
      adminUserId,
      timestamp: new Date().toISOString()
    });
  }

  static logReassignmentResult(result: ReassignmentResult): void {
    const logLevel = result.success ? 'info' : 'error';
    logger[logLevel](`Reassignment result for ${result.entityType}`, {
      operation: 'entity_reassignment',
      entityType: result.entityType,
      success: result.success,
      reassignedCount: result.reassignedCount,
      error: result.error,
      timestamp: new Date().toISOString()
    });
  }

  static logReassignmentSummary(summary: UserReassignmentSummary): void {
    const allSuccessful = summary.results.every(r => r.success);
    const logLevel = allSuccessful ? 'info' : 'warn';
    
    logger[logLevel]('=== USER CONTENT REASSIGNMENT COMPLETED ===', {
      operation: 'user_content_reassignment_summary',
      userId: summary.userId,
      adminUserId: summary.adminUserId,
      totalReassigned: summary.totalReassigned,
      allSuccessful,
      results: summary.results.map(r => ({
        entityType: r.entityType,
        success: r.success,
        count: r.reassignedCount,
        error: r.error
      })),
      timestamp: summary.timestamp.toISOString()
    });
  }

  static logUserDeletionStart(userId: string, adminUserId: string): void {
    logger.info('=== USER DELETION PROCESS STARTED ===', {
      operation: 'user_deletion',
      userId,
      adminUserId,
      timestamp: new Date().toISOString()
    });
  }

  static logUserDeletionResult(result: UserDeletionResult): void {
    const logLevel = result.success ? 'info' : 'error';
    
    logger[logLevel]('=== USER DELETION PROCESS COMPLETED ===', {
      operation: 'user_deletion_result',
      userId: result.userId,
      success: result.success,
      deletedDirectly: result.deletedDirectly,
      totalReassigned: result.reassignmentSummary?.totalReassigned || 0,
      error: result.error,
      timestamp: result.timestamp.toISOString()
    });
  }

  static logConstraintCheck(userId: string, hasConstraints: boolean, details: any): void {
    logger.info('User constraint check completed', {
      operation: 'constraint_check',
      userId,
      hasConstraints,
      details,
      timestamp: new Date().toISOString()
    });
  }

  static logValidationError(operation: string, userId: string, error: string): void {
    logger.warn('User operation validation failed', {
      operation,
      userId,
      error,
      timestamp: new Date().toISOString()
    });
  }

  static logDatabaseError(operation: string, userId: string, error: any): void {
    logger.error('Database error during user operation', {
      operation,
      userId,
      error: error instanceof Error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : error,
      timestamp: new Date().toISOString()
    });
  }

  static logSecurityEvent(event: string, userId: string, adminUserId?: string, details?: any): void {
    logger.warn('Security event during user operation', {
      operation: 'security_event',
      event,
      userId,
      adminUserId,
      details,
      timestamp: new Date().toISOString()
    });
  }
}
