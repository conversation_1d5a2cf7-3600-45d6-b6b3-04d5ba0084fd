import { UserOperationLogger } from './user-operation-logger';

export enum UserOperationErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  ADMIN_INVALID = 'ADMIN_INVALID',
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONSTRAINT_ERROR = 'CONSTRAINT_ERROR',
  REASSIGNMENT_FAILED = 'REASSIGNMENT_FAILED',
  SECURITY_ERROR = 'SECURITY_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class UserOperationError extends Error {
  constructor(
    public readonly type: UserOperationErrorType,
    message: string,
    public readonly userId?: string,
    public readonly adminUserId?: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'UserOperationError';
  }
}

export class UserOperationErrorHandler {
  
  static handleError(error: any, operation: string, userId?: string, adminUserId?: string): UserOperationError {
    // If it's already a UserOperationError, just log and return
    if (error instanceof UserOperationError) {
      UserOperationLogger.logDatabaseError(operation, userId || error.userId || 'unknown', error);
      return error;
    }

    // Handle different types of errors
    if (error.message?.includes('validation')) {
      const userError = new UserOperationError(
        UserOperationErrorType.VALIDATION_ERROR,
        `Validation failed: ${error.message}`,
        userId,
        adminUserId,
        error
      );
      UserOperationLogger.logValidationError(operation, userId || 'unknown', error.message);
      return userError;
    }

    if (error.message?.includes('foreign key') || error.message?.includes('constraint')) {
      const userError = new UserOperationError(
        UserOperationErrorType.CONSTRAINT_ERROR,
        `Database constraint violation: ${error.message}`,
        userId,
        adminUserId,
        error
      );
      UserOperationLogger.logDatabaseError(operation, userId || 'unknown', error);
      return userError;
    }

    if (error.message?.includes('not found')) {
      const userError = new UserOperationError(
        UserOperationErrorType.USER_NOT_FOUND,
        `User not found: ${error.message}`,
        userId,
        adminUserId,
        error
      );
      UserOperationLogger.logValidationError(operation, userId || 'unknown', error.message);
      return userError;
    }

    if (error.message?.includes('admin') || error.message?.includes('permission')) {
      const userError = new UserOperationError(
        UserOperationErrorType.ADMIN_INVALID,
        `Admin validation failed: ${error.message}`,
        userId,
        adminUserId,
        error
      );
      UserOperationLogger.logSecurityEvent('admin_validation_failed', userId || 'unknown', adminUserId, error.message);
      return userError;
    }

    if (error.code === 'ER_ROW_IS_REFERENCED_2' || error.code === 'ER_NO_REFERENCED_ROW_2') {
      const userError = new UserOperationError(
        UserOperationErrorType.CONSTRAINT_ERROR,
        'Database foreign key constraint prevents operation',
        userId,
        adminUserId,
        error
      );
      UserOperationLogger.logDatabaseError(operation, userId || 'unknown', error);
      return userError;
    }

    // Generic database errors
    if (error.code && error.code.startsWith('ER_')) {
      const userError = new UserOperationError(
        UserOperationErrorType.DATABASE_ERROR,
        `Database error: ${error.message}`,
        userId,
        adminUserId,
        error
      );
      UserOperationLogger.logDatabaseError(operation, userId || 'unknown', error);
      return userError;
    }

    // Unknown error
    const userError = new UserOperationError(
      UserOperationErrorType.UNKNOWN_ERROR,
      `Unexpected error during ${operation}: ${error.message || 'Unknown error'}`,
      userId,
      adminUserId,
      error
    );
    UserOperationLogger.logDatabaseError(operation, userId || 'unknown', error);
    return userError;
  }

  static isRetryableError(error: UserOperationError): boolean {
    return [
      UserOperationErrorType.DATABASE_ERROR,
      UserOperationErrorType.UNKNOWN_ERROR
    ].includes(error.type);
  }

  static getHttpStatusCode(error: UserOperationError): number {
    switch (error.type) {
      case UserOperationErrorType.VALIDATION_ERROR:
        return 400;
      case UserOperationErrorType.USER_NOT_FOUND:
        return 404;
      case UserOperationErrorType.ADMIN_INVALID:
      case UserOperationErrorType.SECURITY_ERROR:
        return 403;
      case UserOperationErrorType.CONSTRAINT_ERROR:
        return 409;
      case UserOperationErrorType.REASSIGNMENT_FAILED:
        return 422;
      case UserOperationErrorType.DATABASE_ERROR:
      case UserOperationErrorType.UNKNOWN_ERROR:
      default:
        return 500;
    }
  }
}
