/**
 * Example script demonstrating user deletion with content reassignment
 * 
 * This script shows how to use the user management module to safely delete
 * users while reassigning their content to an admin user.
 */

import { UserService } from '../services/user.service';
import { UserRepository } from '../repositories/user.repository';
import { UserReassignmentService } from '../services/user-reassignment.service';
import Logger from '../../../utils/logger';

const logger = Logger.getInstance();

async function demonstrateUserDeletion() {
  // Initialize the user management services
  const userRepository = new UserRepository();
  const reassignmentService = new UserReassignmentService();
  const userService = new UserService(userRepository, reassignmentService);

  // Example user IDs (in a real scenario, these would come from your user system)
  const userToDelete = '123e4567-e89b-12d3-a456-************';
  const adminUser = '987fcdeb-51a2-43d1-b789-123456789abc';

  try {
    console.log('=== User Deletion Example ===\n');

    // Step 1: Check if user has constraining content
    console.log('1. Checking if user has constraining content...');
    const hasConstraints = await userService.hasConstrainingContent(userToDelete);
    console.log(`   User has constraining content: ${hasConstraints}\n`);

    // Step 2: Perform user deletion with reassignment
    console.log('2. Attempting user deletion with reassignment...');
    const deletionResult = await userService.deleteUserWithReassignment(userToDelete, adminUser);

    if (deletionResult.success) {
      console.log('   ✅ User deletion completed successfully!');
      console.log(`   Direct deletion: ${deletionResult.deletedDirectly}`);
      
      if (deletionResult.reassignmentSummary) {
        console.log(`   Total items reassigned: ${deletionResult.reassignmentSummary.totalReassigned}`);
        console.log('   Reassignment details:');
        
        deletionResult.reassignmentSummary.results.forEach(result => {
          const status = result.success ? '✅' : '❌';
          console.log(`     ${status} ${result.entityType}: ${result.reassignedCount} items`);
          if (result.error) {
            console.log(`       Error: ${result.error}`);
          }
        });
      }
    } else {
      console.log('   ❌ User deletion failed!');
      console.log(`   Error: ${deletionResult.error}`);
    }

    console.log('\n=== Example completed ===');

  } catch (error) {
    logger.error('Error in user deletion example:', error);
    console.error('❌ Example failed with error:', error);
  }
}

async function demonstrateConstraintCheck() {
  const userService = new UserService(
    new UserRepository(),
    new UserReassignmentService()
  );

  const testUserId = '456e7890-e89b-12d3-a456-426614174001';

  console.log('\n=== Constraint Check Example ===\n');
  
  try {
    const hasConstraints = await userService.hasConstrainingContent(testUserId);
    
    if (hasConstraints) {
      console.log('🔒 User has content that would prevent direct deletion');
      console.log('   Recommendation: Use reassignment before deletion');
    } else {
      console.log('🟢 User can be deleted directly without constraints');
      console.log('   Recommendation: Safe to delete immediately');
    }
  } catch (error) {
    console.error('❌ Constraint check failed:', error);
  }
}

async function demonstrateErrorHandling() {
  const userService = new UserService(
    new UserRepository(),
    new UserReassignmentService()
  );

  console.log('\n=== Error Handling Examples ===\n');

  // Example 1: Invalid user ID format
  console.log('1. Testing invalid user ID format...');
  try {
    const result = await userService.deleteUserWithReassignment(
      'invalid-uuid',
      '987fcdeb-51a2-43d1-b789-123456789abc'
    );
    console.log(`   Result: ${result.success ? 'Success' : 'Failed'}`);
    if (!result.success) {
      console.log(`   Error: ${result.error}`);
    }
  } catch (error) {
    console.log(`   Caught error: ${error}`);
  }

  // Example 2: Self-reassignment attempt
  console.log('\n2. Testing self-reassignment prevention...');
  const sameUserId = '123e4567-e89b-12d3-a456-************';
  try {
    const result = await userService.deleteUserWithReassignment(sameUserId, sameUserId);
    console.log(`   Result: ${result.success ? 'Success' : 'Failed'}`);
    if (!result.success) {
      console.log(`   Error: ${result.error}`);
    }
  } catch (error) {
    console.log(`   Caught error: ${error}`);
  }

  // Example 3: Missing parameters
  console.log('\n3. Testing missing parameters...');
  try {
    const result = await userService.deleteUserWithReassignment('', '');
    console.log(`   Result: ${result.success ? 'Success' : 'Failed'}`);
    if (!result.success) {
      console.log(`   Error: ${result.error}`);
    }
  } catch (error) {
    console.log(`   Caught error: ${error}`);
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting User Management Examples\n');
  
  await demonstrateConstraintCheck();
  await demonstrateUserDeletion();
  await demonstrateErrorHandling();
  
  console.log('\n✨ All examples completed!');
}

// Run the examples if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error in examples:', error);
    process.exit(1);
  });
}

export {
  demonstrateUserDeletion,
  demonstrateConstraintCheck,
  demonstrateErrorHandling
};
