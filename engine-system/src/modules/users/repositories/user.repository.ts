import { IUserRepository } from '../interfaces/user-repository.interface';
import Logger from '../../../utils/logger';

const logger = Logger.getInstance();

export class UserRepository implements IUserRepository {
  /**
   * Check if a user exists in the external system
   * Since this notification engine doesn't manage users directly,
   * we'll assume the user exists if they have created content in our system
   */
  async userExists(userId: string): Promise<boolean> {
    try {
      // In a real implementation, this would check an external user service
      // For now, we'll check if the user has any content in our system
      const prisma = (await import('../../../db')).default;
      
      const [templateCount, triggerCount, auditLogCount] = await Promise.all([
        prisma.template.count({
          where: {
            OR: [
              { createdBy: userId },
              { updatedBy: userId }
            ]
          }
        }),
        prisma.trigger.count({
          where: {
            OR: [
              { createdBy: userId },
              { updatedBy: userId }
            ]
          }
        }),
        prisma.templateAuditLog.count({
          where: {
            performedBy: userId
          }
        })
      ]);

      const hasContent = templateCount > 0 || triggerCount > 0 || auditLogCount > 0;
      
      logger.info(`User existence check for ${userId}: hasContent=${hasContent}`);
      return hasContent;
    } catch (error) {
      logger.error(`Error checking user existence for ${userId}:`, error);
      return false;
    }
  }

  /**
   * Validate that an admin user exists and has proper permissions
   * In a real implementation, this would check user roles/permissions
   */
  async validateAdminUser(adminUserId: string): Promise<boolean> {
    try {
      // For this implementation, we'll assume any user ID that exists is valid as admin
      // In a real system, you'd check roles/permissions from your user service
      
      // Basic validation: ensure it's a valid UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      const isValidUuid = uuidRegex.test(adminUserId);
      
      if (!isValidUuid) {
        logger.warn(`Invalid admin user ID format: ${adminUserId}`);
        return false;
      }

      logger.info(`Admin user validation for ${adminUserId}: valid`);
      return true;
    } catch (error) {
      logger.error(`Error validating admin user ${adminUserId}:`, error);
      return false;
    }
  }
}
