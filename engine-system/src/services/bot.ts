import axios from 'axios'
import { QueueService } from '../messaging/queue-service'
import { fetchTrialSites } from '../utils/fakes/allsites'
import Logger from '../utils/logger'

const logger = Logger.getInstance()

export interface TrialSite {
    idSite: string
    siteName: string
    email: string
    trialStartDate: string
    status: string
}

export async function runDailyBot() {
    const queueService = QueueService.getInstance()
    await queueService.initialize()

    const exchange = 'site_evaluation'

    const routingKey = 'site_evaluation.all'

    try {
        const sites = await fetchTrialSites()
        logger.info(
            `Publishing ${sites.length} sites to site_evaluation exchange`
        )

        for (const site of sites) {
            await queueService.publishToExchange(
                exchange,
                routingKey,
                Buffer.from(JSON.stringify(site)),
                'direct'
            )
            await new Promise((resolve) => setTimeout(resolve, 200)) // simulating delay
        }
    } catch (e) {
        logger.error(`Daily Bot Error: ${e}`)
    }
    logger.info('Daily bot completed publishing site Ids')
}

// async function fetchTrialSites(): Promise<Array<TrialSite>>{
//     const trialSites :TrialSite[] = []
//
//     for ( let i = 0; i < 1000; i++ ) {
//         const siteNameBase=     faker.company.name().replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
//         const  siteName  =`${siteNameBase}.com`
//
//         trialSites.push({
//             idSite: `${i+1}`,
//             siteName : siteName,
//             email: `${siteNameBase}@example.com`,
//             trialStartDate: faker.date.recent({ days: 14}).toISOString(),
//             status: "trial"
//         })
//
//     }
//     return trialSites;
// }
//

runDailyBot()
