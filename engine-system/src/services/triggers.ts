import prisma from '../db'
import RedisClient from './redis'
import { RedisKeys } from '../constants/redis-keys'

const TRIGGER_CACHE_TTL = 60 * 60 * 24
const redis = RedisClient.getInstance()
async function getTriggerId(
    triggerValue: string
): Promise<{ id: number } | null> {
    try {
        return await prisma.trigger.findUnique({
            where: {
                name: triggerValue,
            },
            select: {
                id: true,
                cooldownSeconds: true,
                fireOnce: true,
            },
        })
    } finally {
        await prisma.$disconnect()
    }
}

export async function getCachedTriggerId(
    triggerValue: string
): Promise<{ id: number } | null> {
    const cacheKey = RedisKeys.TRIGGER_ID_KEY(triggerValue)

    const cache = await redis.get(cacheKey)
    if (cache) {
        return JSON.parse(cache) as { id: number }
    }

    const trigger = await getTriggerId(triggerValue)
    if (trigger) {
        await redis.set(
            cacheKey,
            JSON.stringify(trigger),
            'EX',
            TRIGGER_CACHE_TTL,
            'NX'
        )
    }
    return trigger
}
