import Redis, { RedisOptions } from 'ioredis'
import { envConfig } from '../env.constant'
import Logger from '../utils/logger'

const logger = Logger.getInstance()

interface RedisConfig {
    host: string
    port: number
    password?: string
    db?: number
    maxRetriesPerRequest?: number
    retryStrategy?: (times: number) => number | null
}

class RedisClient {
    private static instance: Redis | null = null
    private static reconnectAttempts = 0
    private static readonly MAX_RECONNECT_ATTEMPTS = 10
    private static readonly RECONNECT_DELAY_MS = 2000
    private static isShuttingDown = false

    private constructor() {}

    public static getInstance(
        config: RedisConfig = RedisClient.getDefaultConfig()
    ): Redis {
        if (!RedisClient.instance) {
            const options: RedisOptions = {
                host: config.host,
                port: config.port,
                ...(config.password != null && { password: config.password }),
                db: config.db ?? 0,
                maxRetriesPerRequest: config.maxRetriesPerRequest ?? 3,
                retryStrategy:
                    config.retryStrategy ?? RedisClient.defaultRetryStrategy,
                enableOfflineQueue: true,
                enableReadyCheck: true,
                // reconnectOnError(error) {
                //     const targetErrors = [/ENOTFOUND/];
                //     logger.warn(`Redis connection error: ${error.message}`, error);
                //     return !targetErrors.some((targetError) => targetError.test(error.message));
                // }
            }

            RedisClient.instance = new Redis(options)

            RedisClient.setupEventListeners()
        }
        return RedisClient.instance
    }

    private static getDefaultConfig(): RedisConfig {
        return {
            host: envConfig.redis.host,
            port: envConfig.redis.port,
            maxRetriesPerRequest: 3,
            // retryStrategy: RedisClient.defaultRetryStrategy
            // password: envConfig.redis.password
            // db: envConfig.redis.db
        }
    }

    // Retry strategy for reconnection
    private static defaultRetryStrategy(times: number): number | null {
        if (RedisClient.isShuttingDown) {
            logger.info('Shut down in progress, stopping redis retries')
            return null
        }
        if (times > RedisClient.MAX_RECONNECT_ATTEMPTS) {
            logger.error(
                `Max Redis reconnect attempts (${RedisClient.MAX_RECONNECT_ATTEMPTS}) exceeded`
            )
            return null // Stop retrying
        }
        const delay = Math.min(
            RedisClient.RECONNECT_DELAY_MS * Math.pow(2, times),
            30000
        ) // Exponential backoff, cap at 30s
        logger.warn(
            `Redis reconnect attempt ${times}/${RedisClient.MAX_RECONNECT_ATTEMPTS}, retrying in ${delay}ms`
        )
        return delay
    }
    // Setup event listeners
    private static setupEventListeners() {
        if (!RedisClient.instance) return

        RedisClient.instance.on('connect', () => {
            RedisClient.reconnectAttempts = 0
            logger.info('Connected to Redis')
        })

        RedisClient.instance.on('ready', () => {
            logger.info('Redis client ready')
        })

        RedisClient.instance.on('error', (error) => {
            logger.error('Redis connection error:', error.message)
        })

        RedisClient.instance.on('close', () => {
            logger.warn('Redis connection closed')
        })

        RedisClient.instance.on('reconnecting', () => {
            RedisClient.reconnectAttempts++
            logger.info(
                `Reconnecting to Redis, attempt ${RedisClient.reconnectAttempts}`
            )
        })

        RedisClient.instance.on('end', () => {
            logger.error(
                'Redis connection ended; no further reconnection attempts'
            )
            if (!RedisClient.isShuttingDown) {
                RedisClient.instance = null // Reset instance for potential reinitialization
            }
        })
    }

    public static async checkConnection(): Promise<void> {
        const client = RedisClient.getInstance()

        try {
            const pong = await client.ping()

            if (pong !== 'PONG')
                throw new Error('Unexpected Redis Ping Response')
            logger.info('Redis Server is running')
        } catch (e) {
            logger.error(
                'Failed to connect to Redis Server: ',
                e instanceof Error ? e.message : e
            )
            throw new Error('Failed to connect to Redis Server')
        }
    }

    public static async close(): Promise<void> {
        if (!RedisClient.instance) return
        RedisClient.isShuttingDown = true
        try {
            // disable retries
            const client = RedisClient.instance
            client.options.retryStrategy = () => null
            client.options.maxRetriesPerRequest = 0

            // remove event listeners
            client.removeAllListeners()

            // attempt graceful quit
            await client.quit()

            logger.info('Redis connection closed gracefully')
        } catch (error) {
            logger.error('Error closing redis connection:', error)
            RedisClient.instance.disconnect()
            logger.warn('Redis connection forcibly disconnected')
        } finally {
            RedisClient.instance = null
            RedisClient.isShuttingDown = false
            RedisClient.reconnectAttempts = 0
        }
    }
    public static async waitUntilReady(
        timeoutMs: number = 10000
    ): Promise<void> {
        const client = RedisClient.getInstance()
        if (client.status === 'ready') return

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error(`Redis not ready after ${timeoutMs}ms`))
            }, timeoutMs)

            client.once('ready', () => {
                clearTimeout(timeout)
                resolve()
            })

            client.once('error', (error) => {
                clearTimeout(timeout)
                reject(error)
            })
        })
    }
}

export default RedisClient
