import Redis from 'ioredis'
import { SiteState } from '../../types/site'
import RedisClient from '../redis'

export type ObservationList =
    | 'snippet-missing'
    | 'incomplete-trial-signup'
    | 'revenue-not-tracking'
    | 'session-data-not-tracking'
    | 'user-not-logged-in-recently'
    | 'ip-blocking-not-implemented'
    | 'teammates-not-invited'

export class ObservationGenerator {
    private site: SiteState
    private today: Date
    private redis: Redis

    constructor(site: SiteState) {
        this.site = site
        this.today = new Date()
        this.redis = RedisClient.getInstance()
    }

    creditCardCheck() {
        return this.site.creditCard.entered
    }
    snippetCardCheck() {
        return this.site.snippet.installed
    }
    sessionTrackingCheck() {
        return this.site.tracking.sessions.active
    }
    revenueTrackingCheck() {
        return this.site.tracking.revenue.active
    }
    ipBlockingCheck() {
        return this.site.ipBlocking.implemented
    }
    hasTeamMembers(): boolean {
        return this.site.hasTeamMembers
    }
    loginCheck() {
        return this.site.login.daysSinceLastLogin >= 5
    }

    generateObservations(): ObservationList[] {
        const observations: ObservationList[] = []

        if (this.creditCardCheck()) {
            observations.push('incomplete-trial-signup')
        }
        if (this.snippetCardCheck()) {
            observations.push('snippet-missing')
        }
        if (this.sessionTrackingCheck()) {
            observations.push('session-data-not-tracking')
        }
        if (this.revenueTrackingCheck()) {
            observations.push('revenue-not-tracking')
        }
        if (this.ipBlockingCheck()) {
            observations.push('ip-blocking-not-implemented')
        }
        if (this.hasTeamMembers()) {
            observations.push('teammates-not-invited')
        }
        if (this.loginCheck()) {
            observations.push('user-not-logged-in-recently')
        }
        return observations
    }
}
