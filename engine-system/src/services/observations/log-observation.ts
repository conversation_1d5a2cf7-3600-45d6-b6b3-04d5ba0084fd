import { ObservationList } from './observer-evaluator'
import axios from 'axios'
import Logger from '../../utils/logger'
import { envConfig } from '../../env.constant'

const logger = Logger.getInstance()

export async function logObservation(
    data: {
        idsite: number
        dateFilterInput: Date[]
        observations: ObservationList[]
    },
    url: string = envConfig.observationUrl
) {
    const source = 'in_context'

    const payload = {
        idsite: data.idsite,
        dateFilterInput: data.dateFilterInput
            .map((date) => date.toISOString().split('T')[0])
            .join(','),
        source,
        observations: data.observations,
    }

    try {
        const response = await axios.post(url, payload)
        logger.info(
            `observation logged successfully for idsite: ${data.idsite}`,
            response.data
        )
        return response.data
    } catch (error) {
        console.error('Failed to log observation', error)
        logger.error(error)
        //throw error
    }
}
