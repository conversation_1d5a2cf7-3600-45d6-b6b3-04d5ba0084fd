import prisma from '../db'
import {
    ChannelType,
    NotificationLog,
    NotificationStatus,
} from '../generated/prisma'

export async function createNotificationLog(data: {
    recipient: string
    idSite: number
    triggerValue: string
    status: NotificationStatus
    channelId: number
    triggerId?: number
    metadata?: Record<string, any>
}): Promise<NotificationLog> {
    try {
        return await prisma.notificationLog.create({
            data: {
                recipient: data.recipient,
                idSite: data.idSite,
                triggerValue: data.triggerValue,
                status: data.status,
                ...(data.metadata != null && { metadata: data.metadata }),
                channelId: data.channelId,
                ...(data.triggerId != null && { triggerId: data.triggerId }),
            },
        })
    } finally {
        await prisma.$disconnect()
    }
}
export async function createManyNotificationLogs(
    data: {
        recipient: string
        idSite: number
        triggerValue: string
        status: NotificationStatus
        channelId: number
        triggerId?: number
        metadata?: Record<string, any>
        createdAt?: Date
        updatedAt?: Date
    }[]
): Promise<{ count: number }> {
    try {
        const records = data.map((item) => ({
            recipient: item.recipient,
            idSite: item.idSite,
            triggerValue: item.triggerValue,
            status: item.status,
            metadata: item.metadata ?? undefined,
            channelId: item.channelId,
            triggerId: item.triggerId ?? undefined,
            createdAt: item.createdAt ?? undefined,
            updatedAt: item.updatedAt ?? undefined,
        }))

        return await prisma.notificationLog.createMany({
            data: records,
            skipDuplicates: false, // set to true if you want to ignore duplicates
        })
    } finally {
        await prisma.$disconnect()
    }
}
