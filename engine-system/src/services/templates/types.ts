import { Parameter } from '../../generated/prisma'

type BaseValidation = {
    required?: boolean
    defaultValue?: any
}
export type ParamType =
    | 'string'
    | 'number'
    | 'boolean'
    | 'object'
    | 'array'
    | 'date'

type StringValidation = BaseValidation & {
    type: 'string'
    minLength?: number
    maxLength?: number
    pattern?: string
    [key: string]: any
}

type NumberValidation = BaseValidation & {
    type: 'number'
    min?: number
    max?: number
    [key: string]: any
}
type BooleanValidation = BaseValidation & {
    type: 'boolean'
    [key: string]: any
}
type DateValidation = BaseValidation & {
    type: 'date'
    [key: string]: any
}
type ObjectValidation = BaseValidation & {
    type: 'object'
    properties: Record<string, ParameterValidation>
    [key: string]: any
}
type ArrayValidation = {
    type: 'array'
    items: ParameterValidation
    [key: string]: any
}
type AnyValidation = {
    type: 'any'
    [key: string]: any
}
export type ParameterValidation =
    | StringValidation
    | NumberValidation
    | BooleanValidation
    | DateValidation
    | ObjectValidation
    | ArrayValidation
    | AnyValidation

export type IParameter = Pick<
    Parameter,
    | 'name'
    | 'exampleValue'
    | 'required'
    | 'description'
    | 'defaultValue'
    | 'validations'
> & { type: ParamType }
