import { z, ZodTypeAny } from 'zod'
import { IParameter, ParameterValidation } from './types'

export class SchemaBuilder {
    buildValidationObject(param: IParameter): ParameterValidation {
        let base: Record<string, any> = {}
        try {
            base =
                typeof param.validations === 'string'
                    ? JSON.parse(param.validations)
                    : param.validations || {}
        } catch (_) {}

        return {
            type: param.type as ParameterValidation['type'],
            ...base,
            required: param.required,
            defaultValue: param.defaultValue,
        } as ParameterValidation
    }

    getSchema(validation: ParameterValidation): ZodTypeAny {
        switch (validation.type) {
            case 'string': {
                let schema = z.string()
                if (validation.minLength !== undefined) {
                    schema.min(validation.minLength)
                }
                if (validation.maxLength !== undefined) {
                    schema.max(validation.maxLength)
                }
                return schema
            }
            case 'number': {
                let schema = z.number()
                if (validation.min !== undefined) {
                    schema.min(validation.min)
                }
                if (validation.max !== undefined) {
                    schema.max(validation.max)
                }
                return schema
            }
            case 'boolean': {
                return z.boolean()
            }
            case 'date': {
                return z.coerce.date()
            }
            case 'object': {
                const shape: Record<string, z.ZodTypeAny> = {}
                Object.entries(validation.properties).forEach(
                    ([key, child]) => {
                        // if (!child.required) {
                        //     childSchema = childSchema.optional()
                        // }
                        shape[key] = this.createSchemaFromValidation(child)
                    }
                )
                return z.object(shape)
            }
            case 'array': {
                const itemSchema = this.createSchemaFromValidation(
                    validation.items
                )
                return z.array(itemSchema)
            }
            case 'any':
            default: {
                return z.any()
            }
        }
    }
    createSchemaFromValidation(validation: ParameterValidation): ZodTypeAny {
        let schema = this.getSchema(validation)
        if (!validation.required) {
            schema = schema.optional()
        }
        if (validation.defaultValue !== undefined) {
            schema = schema.default(validation.defaultValue)
        }
        return schema
    }

    parseParameterValue(value: string, validations: ParameterValidation): any {
        if (!value) return value
        try {
            switch (validations.type) {
                case 'number':
                    return Number(value)
                case 'boolean':
                    return value === 'true'
                case 'date':
                    return new Date(value)
                case 'array':
                case 'object':
                    return JSON.parse(value)
            }
        } catch (error) {
            return value
        }
        return value
    }

    createZodSchemaV1(
        param: IParameter,
        allParams: Record<string, any>
    ): ZodTypeAny {
        const validation = this.buildValidationObject(param)
        return this.createSchemaFromValidation(validation)
    }
    // createZodSchema(
    //     param: IParameter,
    //     allParams: Record<string, any>
    // ): ZodTypeAny {
    //     let schema: z.ZodTypeAny
    //     let validations: ParameterValidation = { type: 'string' }
    //
    //     try {
    //         if (param.validations) {
    //             validations =
    //                 typeof param.validations === 'string'
    //                     ? JSON.parse(param.validations)
    //                     : param.validations
    //         }
    //     } catch (e) {}
    //     schema = this.createSchemaFromValidation(validations)
    //     if (param.defaultValue != null) {
    //         const defaultValue = this.parseParameterValue(
    //             param.defaultValue,
    //             validations
    //         )
    //         schema = schema.default(defaultValue)
    //     }
    //     return schema
    // }

    validateParameters(
        paramDefinitions: IParameter[],
        providedParams: Record<string, any>
    ): {
        isValid: boolean
        errors: string[]
        parameters: Record<string, any>
    } {
        const schemaMap: Record<string, z.ZodTypeAny> = {}
        const errors: string[] = []

        for (const param of paramDefinitions) {
            schemaMap[param.name] = this.createZodSchemaV1(
                param,
                providedParams
            )
        }
        const objectSchema = z.object(schemaMap)

        try {
            const validatedParams = objectSchema.parse(providedParams)
            return { isValid: true, errors: [], parameters: validatedParams }
        } catch (error) {
            if (error instanceof z.ZodError) {
                for (const issue of error.errors) {
                    errors.push(`${issue.path.join('.')}: ${issue.message}`)
                }
            } else {
                errors.push((error as Error)?.message || 'Unknown error')
            }
            return { isValid: false, errors, parameters: {} }
        }
    }
}
