import { Template<PERSON><PERSON><PERSON> } from './renderer'
import { IParameter } from './types'

function runDummy() {
    const renderer = new TemplateRenderer()
    const paramDef: IParameter = {
        name: 'user',
        required: true,
        type: 'object',
        exampleValue: 'more things',
        validations: {
            properties: {
                name: { type: 'string', required: true },
                age: {
                    type: 'number',
                    required: false,
                    min: 0,
                    defaultValue: 23,
                },
                roles: { type: 'array', items: { type: 'string' } },
                verified: {
                    type: 'boolean',
                    defaultValue: true,
                    required: false,
                },
            },
        },
        defaultValue: null,
        description: 'user name',
    }

    const template =
        'Name: {{user.name}}\n ' +
        'Age: {{user.age}} \n' +
        'Roles: {{join user.roles ","}}\n' +
        'Verified: {{user.verified}}\n'
    const output = renderer.renderTemplate(template, [paramDef], {
        user: {
            name: 'Incontext',
            roles: ['admin', 'editor'],
        },
    })
    console.log(output)
    const preview = renderer.previewTemplate(template, {
        user: {
            name: 'Incontext',
            roles: ['admin', 'editor'],
            verified: true,
            age: 23,
        },
    })
    console.log(preview)
}

runDummy()
