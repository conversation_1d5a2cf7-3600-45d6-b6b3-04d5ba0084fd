# Email Service

A robust, extensible email service implementation following SOLID principles for the Notification Engine System.

## Overview

The Email Service provides a unified interface for sending emails through multiple providers with automatic fallback support. It's designed to be easily extensible and maintainable.

## Architecture

### SOLID Principles Implementation

- **Single Responsibility**: Each provider handles only one email service
- **Open/Closed**: Easy to add new providers without modifying existing code
- **Liskov Substitution**: All providers are interchangeable through the EmailProvider interface
- **Interface Segregation**: Focused EmailProvider interface with only necessary methods
- **Dependency Inversion**: Service depends on abstractions, not concrete implementations

### Components

```
src/services/email/
├── interfaces/
│   └── email-provider.interface.ts    # Core interface definition
├── providers/
│   ├── smtp-email-provider.ts         # SMTP implementation
│   ├── logging-email-provider.ts      # Logging implementation
│   └── mailtrap-email-provider.ts     # Mailtrap implementation
├── dtos/
│   └── send-email.dto.ts              # Data transfer objects
├── errors/
│   └── email.errors.ts                # Custom error classes
├── email.service.ts                   # Main service class
├── email-provider.factory.ts          # Provider factory
└── index.ts                           # Public exports
```

## Usage

### Basic Usage

```typescript
import { EmailService } from './services/email';

// Get singleton instance (auto-configured from environment)
const emailService = await EmailService.getInstance();

// Send an email
const result = await emailService.sendEmail({
  to: '<EMAIL>',
  subject: 'Welcome!',
  html: '<h1>Welcome to our service!</h1>',
  text: 'Welcome to our service!'
});

if (result.success) {
  console.log('Email sent:', result.messageId);
} else {
  console.error('Email failed:', result.error);
}
```

### Custom Provider Configuration

```typescript
import { EmailService, SMTPEmailProvider, LoggingEmailProvider } from './services/email';

// Create with specific providers
const primaryProvider = new SMTPEmailProvider();
const fallbackProvider = new LoggingEmailProvider();

const emailService = new EmailService(primaryProvider, fallbackProvider);

// Send email with automatic fallback
const result = await emailService.sendEmail(emailData);
```

### Using the Factory

```typescript
import { EmailProviderFactory } from './services/email';

// Create provider by type
const provider = EmailProviderFactory.createProvider('smtp');
await provider.initialize();

// Create from environment
const envProvider = EmailProviderFactory.createFromEnvironment();
```

## Configuration

### Environment Variables

```bash
# Email Provider Selection
EMAIL_PROVIDER=smtp|logging|mailtrap

# SMTP Configuration (required if EMAIL_PROVIDER=smtp)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
DEFAULT_EMAIL_FROM=<EMAIL>

# Mailtrap Configuration (required if EMAIL_PROVIDER=mailtrap)
MAILTRAP_API_TOKEN=your-api-token
MAILTRAP_INBOX_ID=your-inbox-id
```

### Provider Types

#### 1. SMTP Provider (`smtp`)
- Uses nodemailer for SMTP email sending
- Supports all standard SMTP configurations
- Best for production environments

#### 2. Logging Provider (`logging`)
- Logs emails instead of sending them
- Perfect for development and testing
- No external dependencies required

#### 3. Mailtrap Provider (`mailtrap`)
- Uses Mailtrap API for email testing
- Great for staging environments
- Provides email testing and debugging features

## Error Handling

The service includes comprehensive error handling with custom error types:

```typescript
import { isEmailError, EmailSendError } from './services/email';

try {
  await emailService.sendEmail(emailData);
} catch (error) {
  if (isEmailError(error)) {
    console.error(`Email error [${error.code}]:`, error.message);
    
    if (error instanceof EmailSendError) {
      console.error('Failed to send to:', error.recipient);
      console.error('Provider:', error.provider);
    }
  }
}
```

### Error Types

- `EmailValidationError`: Invalid email data
- `EmailProviderInitializationError`: Provider setup failed
- `EmailSendError`: Email sending failed
- `EmailConfigurationError`: Missing or invalid configuration
- `EmailHealthCheckError`: Provider health check failed
- `EmailRateLimitError`: Rate limit exceeded
- `EmailAttachmentError`: Attachment processing failed

## Health Monitoring

```typescript
// Check provider health
const health = await emailService.checkHealth();

console.log('Primary provider:', health.primary.provider, health.primary.healthy);
if (health.fallback) {
  console.log('Fallback provider:', health.fallback.provider, health.fallback.healthy);
}

// Get provider information
const info = emailService.getProviderInfo();
console.log('Current providers:', info);
```

## Testing

The email service includes comprehensive test coverage:

```bash
# Run email service tests
npm run test:unit -- tests/unit/services/email

# Run with coverage
npm run test:coverage -- tests/unit/services/email
```

### Test Examples

```typescript
import { LoggingEmailProvider } from './services/email';
import { TestDataFactory } from '../../../tests/helpers/test-helpers';

describe('Email Service', () => {
  it('should send email successfully', async () => {
    const provider = new LoggingEmailProvider();
    await provider.initialize();
    
    const emailData = TestDataFactory.createValidEmailData();
    const result = await provider.send(emailData);
    
    expect(result.success).toBe(true);
    expect(result.messageId).toBeDefined();
  });
});
```

## Adding New Providers

To add a new email provider:

1. **Create the provider class**:
```typescript
// src/services/email/providers/new-provider.ts
export class NewEmailProvider implements EmailProvider {
  async send(emailData: EmailData): Promise<EmailSendResult> {
    // Implementation
  }
  
  async initialize(): Promise<boolean> {
    // Implementation
  }
  
  async isHealthy(): Promise<boolean> {
    // Implementation
  }
  
  getProviderName(): string {
    return 'NEW_PROVIDER';
  }
}
```

2. **Update the factory**:
```typescript
// Add to EmailProviderFactory.createProvider()
case 'new-provider':
  return new NewEmailProvider();
```

3. **Add tests**:
```typescript
// tests/unit/services/email/providers/new-provider.test.ts
```

4. **Update documentation and exports**

## Best Practices

1. **Always use the EmailService class** instead of providers directly
2. **Configure fallback providers** for production environments
3. **Use environment variables** for provider configuration
4. **Implement proper error handling** with custom error types
5. **Monitor provider health** in production
6. **Test with different providers** during development
7. **Use DTOs for validation** when accepting external input

## Migration Guide

### From Legacy Email Utils

Replace the old `send-email.ts` utility:

```typescript
// Old way
import { sendEmailUtil } from '../utils/send-email';
await sendEmailUtil(email, subject, body, true);

// New way
import { EmailService } from '../services/email';
const emailService = await EmailService.getInstance();
await emailService.sendEmail({
  to: email,
  subject,
  html: body
});
```

The new service provides:
- Better error handling
- Provider abstraction
- Automatic fallback
- Comprehensive testing
- Type safety
- Configuration flexibility
