import { SiteState, WeeklyMetrics } from '../types/site'
import RedisClient from './redis'
import Redis from 'ioredis'
import { fetchWeeklyMetrics } from '../utils/fakes/weekly-metrics'
import Logger from '../utils/logger'
import { TRIGGER_TYPE } from '../enums/triggers'

const logger = Logger.getInstance()

interface TriggerResult {
    trigger: TRIGGER_TYPE
    priority: number
    category: string
    extraData?: Record<string, any>
    trialDaysElapsed?: number
}

export class TriggerEvaluator {
    private site: SiteState
    private today: Date
    private redis: Redis

    constructor(site: SiteState) {
        this.site = site
        this.today = new Date()
        this.site.trialDaysElapsed = Math.floor(
            (this.today.getTime() -
                new Date(this.site.trialStartDate).getTime()) /
                (1000 * 60 * 60 * 24)
        )
        this.redis = RedisClient.getInstance()
    }

    async evaluateTriggers(event?: {
        type: string
        value?: number
        timestamp?: string
    }): Promise<TriggerResult | null> {
        // Batch triggers (return first match due to early termination)
        const creditCardCheck = this.checkCreditCard()
        if (creditCardCheck) return creditCardCheck

        const snippetCheck = await this.checkSnippet()
        if (snippetCheck) return snippetCheck

        const sessionsCheck = await this.checkSessions()
        if (sessionsCheck) return sessionsCheck

        const revenueCheck = await this.checkRevenue()
        if (revenueCheck) return revenueCheck

        const loginActivityCheck = await this.checkLoginInactivity()
        if (loginActivityCheck) return loginActivityCheck

        const ipBlockingCheck = this.checkIpBlocking()
        if (ipBlockingCheck) return ipBlockingCheck

        const teamMatesInvitedCheck = this.checkTeamMatesNotInvited()
        if (teamMatesInvitedCheck) return teamMatesInvitedCheck

        return (await this.checkWeeklySummaries()) || null
    }

    private checkCreditCard(): TriggerResult | null {
        if (!this.site.creditCard.entered && this.site.trialDaysElapsed >= 1) {
            return {
                trigger: TRIGGER_TYPE.CREDIT_CARD,
                priority: 1,
                category: 'onboarding',
            }
        }
        return null
    }

    private async checkSnippet(): Promise<TriggerResult | null> {
        if (!this.site.snippet.installed && this.site.trialDaysElapsed >= 2) {
            const trigger = this.site.notificationsSent?.[
                TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS
            ]
                ? TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SCHEDULE_CALL
                : TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS
            if (trigger === TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS)
                await this.updateNotificationsSent(
                    TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS
                )
            return {
                trigger,
                priority: 1,
                category: 'onboarding',
            }
        }
        return null
    }

    private async checkSessions(): Promise<TriggerResult | null> {
        if (
            this.site.snippet.installed &&
            !this.site.tracking.sessions.active
        ) {
            const trigger = this.site.notificationsSent?.[
                TRIGGER_TYPE.SESSION_NOT_TRACKING_SHARE_DOCS
            ]
                ? TRIGGER_TYPE.SESSION_NOT_TRACKING_SCHEDULE_CALL
                : TRIGGER_TYPE.SESSION_NOT_TRACKING_SHARE_DOCS
            if (trigger === TRIGGER_TYPE.SESSION_NOT_TRACKING_SHARE_DOCS)
                await this.updateNotificationsSent(
                    TRIGGER_TYPE.SESSION_NOT_TRACKING_SHARE_DOCS
                )
            return {
                trigger,
                priority: 1,
                category: 'onboarding',
            }
        }
        return null
    }

    private async checkRevenue(): Promise<TriggerResult | null> {
        if (this.site.tracking.sessions && !this.site.tracking.revenue.active) {
            const trigger = this.site.notificationsSent?.[
                TRIGGER_TYPE.REVENUE_NOT_TRACKING_SHARE_DOCS
            ]
                ? TRIGGER_TYPE.REVENUE_NOT_TRACKING_SCHEDULE_CALL
                : TRIGGER_TYPE.REVENUE_NOT_TRACKING_SHARE_DOCS
            if (trigger === TRIGGER_TYPE.REVENUE_NOT_TRACKING_SHARE_DOCS)
                await this.updateNotificationsSent(
                    TRIGGER_TYPE.REVENUE_NOT_TRACKING_SHARE_DOCS
                )
            return {
                trigger,
                priority: 1,
                category: 'onboarding',
            }
        }
        return null
    }

    private async checkWeeklySummaries(): Promise<TriggerResult | null> {
        if (
            this.site.trialDaysElapsed >= 14 &&
            !this.site.notificationsSent?.[
                TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL
            ]
        ) {
            await this.updateNotificationsSent(
                TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL
            )
            return {
                trigger: TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL,
                priority: 3,
                category: 'summaries',
                trialDaysElapsed: this.site.trialDaysElapsed,
                // extraData: this._fetchWeeklyMetrics(this.site.trialDaysElapsed, +this.site.idSite)
            }
        }
        if (
            this.site.trialDaysElapsed >= 7 &&
            !this.site.notificationsSent?.[
                TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL
            ]
        ) {
            await this.updateNotificationsSent(
                TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL
            )
            return {
                trigger: TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL,
                priority: 3,
                category: 'summaries',
                trialDaysElapsed: this.site.trialDaysElapsed,
                // extraData: this._fetchWeeklyMetrics(this.site.trialDaysElapsed, +this.site.idSite)
            }
        }
        return null
    }

    private async checkLoginInactivity(): Promise<TriggerResult | null> {
        if (
            this.site.login.daysSinceLastLogin >= 10 &&
            this.site.snippet.installed &&
            !this.site.notificationsSent?.[
                TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO
            ]
        ) {
            await this.updateNotificationsSent(
                TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO
            )
            return {
                trigger: TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO,
                priority: 2,
                category: 'engagement',
            }
        }

        if (
            this.site.login.daysSinceLastLogin >= 5 &&
            this.site.snippet.installed &&
            !this.site.notificationsSent?.[
                TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY
            ]
        ) {
            await this.updateNotificationsSent(
                TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY
            )
            return {
                trigger: TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY,
                priority: 2,
                category: 'engagement',
            }
        }
        return null
    }

    private checkIpBlocking(): TriggerResult | null {
        if (!this.site.ipBlocking.implemented) {
            return {
                trigger: TRIGGER_TYPE.IP_BLOCKING_NOT_IMPLEMENTED,
                priority: 2,
                category: 'onboarding',
            }
        }
        return null
    }

    private checkTeamMatesNotInvited(): TriggerResult | null {
        if (!this.site.hasTeamMembers) {
            return {
                trigger: TRIGGER_TYPE.TEAM_MATES_NOT_INVITED,
                priority: 2,
                category: 'onboarding',
            }
        }
        return null
    }

    private async _fetchWeeklyMetrics(
        target: number,
        idSite: number
    ): Promise<WeeklyMetrics> {
        return await fetchWeeklyMetrics(target, idSite)
    }

    private async updateNotificationsSent(trigger: string) {
        if (!this.site.notificationsSent) this.site.notificationsSent = {}
        this.site.notificationsSent[trigger] = true
        await this.redis.hset(
            `site:${this.site.idSite}:state`,
            'notificationsSent',
            JSON.stringify(this.site.notificationsSent)
        )
    }
}
