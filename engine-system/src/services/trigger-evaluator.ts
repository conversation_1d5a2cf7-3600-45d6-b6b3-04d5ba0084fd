import { SiteState, WeeklyMetrics } from '../types/site'
import RedisClient from './redis'
import Redis from 'ioredis'
import { fetchWeeklyMetrics } from '../utils/fakes/weekly-metrics'
import Logger from '../utils/logger'
import { TRIGGER_TYPE } from '../enums/triggers'
import { RedisKeys } from '../constants/redis-keys'

const logger = Logger.getInstance()

interface TriggerResult {
    trigger: TRIGGER_TYPE
    priority: number
    category: string
    extraData?: Record<string, any>
    trialDaysElapsed?: number
}

export class TriggerEvaluator {
    private site: SiteState
    private today: Date
    private redis: Redis

    constructor(site: SiteState) {
        this.site = site
        this.today = new Date()
        this.redis = RedisClient.getInstance()
    }

    async evaluatePayingTriggers(): Promise<TriggerResult | null> {
        const snippetCheck = await this.checkSnippet()
        if (snippetCheck) return snippetCheck

        const sessionsCheck = await this.checkSessions()
        if (sessionsCheck) return sessionsCheck

        const revenueCheck = await this.checkRevenue()
        if (revenueCheck) return revenueCheck

        const loginActivityCheck = await this.checkLoginInactivity()
        if (loginActivityCheck) return loginActivityCheck

        const ipBlockingCheck = await this.checkIpBlocking()
        if (ipBlockingCheck) return ipBlockingCheck

        const teamMatesInvitedCheck = await this.checkTeamMatesNotInvited()
        if (teamMatesInvitedCheck) return teamMatesInvitedCheck

        return null
    }

    async evaluateTriggers(event?: {
        type: string
        value?: number
        timestamp?: string
    }): Promise<TriggerResult | null> {
        // Batch triggers (return first match due to early termination)
        const creditCardCheck = await this.checkCreditCard()
        if (creditCardCheck) return creditCardCheck

        const snippetCheck = await this.checkSnippet()
        if (snippetCheck) return snippetCheck

        const sessionsCheck = await this.checkSessions()
        if (sessionsCheck) return sessionsCheck

        const revenueCheck = await this.checkRevenue()
        if (revenueCheck) return revenueCheck

        const loginActivityCheck = await this.checkLoginInactivity()
        if (loginActivityCheck) return loginActivityCheck

        const ipBlockingCheck = await this.checkIpBlocking()
        if (ipBlockingCheck) return ipBlockingCheck

        const teamMatesInvitedCheck = await this.checkTeamMatesNotInvited()
        if (teamMatesInvitedCheck) return teamMatesInvitedCheck

        return (await this.checkWeeklySummaries()) || null
    }

    private async checkCreditCard(
        period: number = 1
    ): Promise<TriggerResult | null> {
        if (
            !this.site.creditCard.entered &&
            this.site.trialDaysElapsed >= period
        ) {
            if (
                !(await this.isTriggerLocked(
                    this.site.idSite!,
                    TRIGGER_TYPE.CREDIT_CARD
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.CREDIT_CARD,
                    priority: 1,
                    category: 'onboarding',
                }
            }
        }
        return null
    }

    private async checkSnippet(
        period: number = 1
    ): Promise<TriggerResult | null> {
        const condition =
            Boolean(this.site.creditCard.entered) &&
            !this.site.snippet.installed &&
            this.site.trialDaysElapsed >= period

        return this.checkOnBoardingDoubleTrigger(
            TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS,
            TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SCHEDULE_CALL,
            condition,
            this.site.idSite!
        )
    }
    private async checkSessions(): Promise<TriggerResult | null> {
        const condition =
            this.site.snippet.installed && !this.site.tracking.sessions.active

        return this.checkOnBoardingDoubleTrigger(
            TRIGGER_TYPE.SESSION_NOT_TRACKING_SHARE_DOCS,
            TRIGGER_TYPE.SESSION_NOT_TRACKING_SCHEDULE_CALL,
            condition,
            this.site.idSite!
        )
    }

    private async checkRevenue(): Promise<TriggerResult | null> {
        const condition =
            this.site.tracking.sessions.active &&
            !this.site.tracking.revenue.active

        return this.checkOnBoardingDoubleTrigger(
            TRIGGER_TYPE.REVENUE_NOT_TRACKING_SHARE_DOCS,
            TRIGGER_TYPE.REVENUE_NOT_TRACKING_SCHEDULE_CALL,
            condition,
            this.site.idSite!
        )
    }

    private async checkWeeklySummaries(): Promise<TriggerResult | null> {
        if (this.site.trialDaysElapsed >= 14) {
            if (
                !(await this.hasSentTrigger(
                    TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL,
                    priority: 3,
                    category: 'summaries',
                    trialDaysElapsed: this.site.trialDaysElapsed,
                }
            }
        }
        if (
            this.site.trialDaysElapsed >= 7 &&
            this.site.trialDaysElapsed < 14
        ) {
            if (
                !(await this.hasSentTrigger(
                    TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL,
                    priority: 3,
                    category: 'summaries',
                    trialDaysElapsed: this.site.trialDaysElapsed,
                }
            }
        }
        return null
    }

    private async checkLoginInactivity(): Promise<TriggerResult | null> {
        if (this.site.login.daysSinceLastLogin >= 10) {
            if (
                !(await this.hasSentTrigger(
                    TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO,
                    priority: 2,
                    category: 'engagement',
                }
            }
        }

        if (
            this.site.login.daysSinceLastLogin >= 5 &&
            this.site.login.daysSinceLastLogin < 10
        ) {
            if (
                !(await this.hasSentTrigger(
                    TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY,
                    priority: 2,
                    category: 'engagement',
                }
            }
        }
        return null
    }

    private async checkIpBlocking(): Promise<TriggerResult | null> {
        if (this.site.snippet.installed && !this.site.ipBlocking.implemented) {
            if (
                !(await this.isTriggerLocked(
                    this.site.idSite!,
                    TRIGGER_TYPE.IP_BLOCKING_NOT_IMPLEMENTED
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.IP_BLOCKING_NOT_IMPLEMENTED,
                    priority: 2,
                    category: 'onboarding',
                }
            }
        }
        return null
    }

    private async checkTeamMatesNotInvited(): Promise<TriggerResult | null> {
        if (this.site.snippet.installed && !this.site.hasTeamMembers) {
            if (
                !(await this.isTriggerLocked(
                    this.site.idSite!,
                    TRIGGER_TYPE.TEAM_MATES_NOT_INVITED
                ))
            ) {
                return {
                    trigger: TRIGGER_TYPE.TEAM_MATES_NOT_INVITED,
                    priority: 2,
                    category: 'onboarding',
                }
            }
        }
        return null
    }

    private async _fetchWeeklyMetrics(
        target: number,
        idSite: number
    ): Promise<WeeklyMetrics> {
        return await fetchWeeklyMetrics(target, idSite)
    }

    private getNotificationKey(
        idSite: number,
        channel: string = 'email'
    ): string {
        return RedisKeys.NOTIFICATIONS_SENT_CHANNEL_KEY(idSite, channel)
        // return `notifications:${this.site.idSite}:sent`
    }

    private getLockKey(
        idSite: number,
        trigger: TRIGGER_TYPE,
        channel: string = 'email'
    ): string {
        return RedisKeys.SITE_TRIGGER_CHANNEL_KEY(
            idSite,
            trigger.toString(),
            channel
        )
        // return `site:${this.site.idSite}:${trigger.toString()}`
    }

    private async hasSentTrigger(
        trigger: TRIGGER_TYPE,
        channel: string = 'email'
    ): Promise<boolean> {
        return Boolean(
            await this.redis.sismember(
                this.getNotificationKey(this.site.idSite!, channel),
                trigger.toString()
            )
        )
    }

    private async isTriggerLocked(
        idSite: number,
        trigger: TRIGGER_TYPE,
        channel: string = 'email'
    ): Promise<boolean> {
        const ttl = await this.redis.ttl(
            this.getLockKey(idSite, trigger, channel)
        )

        return ttl !== -2
    }
    private buildTriggerResult(
        trigger: TRIGGER_TYPE,
        category: string,
        priority: number,
        data?: Record<string, unknown>
    ): TriggerResult {
        return {
            trigger,
            category,
            priority,
            ...data,
        }
    }

    private async checkOnBoardingDoubleTrigger(
        docsTrigger: TRIGGER_TYPE,
        callTrigger: TRIGGER_TYPE,
        condition: boolean,
        idSite: number
    ): Promise<TriggerResult | null> {
        if (!condition) return null

        const hasSentDocs = await this.hasSentTrigger(docsTrigger)
        if (!hasSentDocs) {
            return this.buildTriggerResult(docsTrigger, 'onboarding', 1)
        }
        const isDocsLocked = await this.isTriggerLocked(idSite, docsTrigger)
        if (!isDocsLocked) {
            const hasSentCall = await this.hasSentTrigger(callTrigger)
            if (!hasSentCall) {
                return this.buildTriggerResult(callTrigger, 'onboarding', 1)
            }
            const isCallLocked = await this.isTriggerLocked(idSite, callTrigger)
            if (!isCallLocked) {
                return this.buildTriggerResult(callTrigger, 'onboarding', 1)
            }
        }
        return null
    }
}
