import prisma from '../../db'
import RedisClient from '../redis'
import { RedisKeys } from '../../constants/redis-keys'

const TRIGGER_CACHE_TTL = 60 * 60 * 24
const redis = RedisClient.getInstance()
async function getTriggerTypeId(value: string): Promise<{ id: number } | null> {
    try {
        return await prisma.triggerType.findUnique({
            where: {
                name: value,
            },
            select: {
                id: true,
            },
        })
    } finally {
        await prisma.$disconnect()
    }
}

export async function getCachedTriggerTypeId(
    value: string
): Promise<{ id: number } | null> {
    const cacheKey = RedisKeys.TRIGGER_TYPE_ID_KEY(value)

    const cache = await redis.get(cacheKey)
    if (cache) {
        return JSON.parse(cache) as { id: number }
    }

    const triggerType = await getTriggerTypeId(value)
    if (triggerType) {
        await redis.set(
            cacheKey,
            JSON.stringify(triggerType),
            'EX',
            TRIGGER_CACHE_TTL,
            'NX'
        )
    }
    return triggerType
}
