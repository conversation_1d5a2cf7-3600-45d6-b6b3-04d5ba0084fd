import prisma from '../../db'
import RedisClient from '../redis'
import { RedisKeys } from '../../constants/redis-keys'

const TRIGGER_CACHE_TTL = 60 * 60 * 24
const redis = RedisClient.getInstance()
async function getContentTypeId(value: string): Promise<{ id: number } | null> {
    try {
        return await prisma.contentType.findUnique({
            where: {
                name: value,
            },
            select: {
                id: true,
            },
        })
    } finally {
        await prisma.$disconnect()
    }
}

export async function getCachedContentTypeId(
    value: string
): Promise<{ id: number } | null> {
    const cacheKey = RedisKeys.CONTENT_TYPE_ID_KEY(value)

    const cache = await redis.get(cacheKey)
    if (cache) {
        return JSON.parse(cache) as { id: number }
    }

    const contentType = await getContentTypeId(value)
    if (contentType) {
        await redis.set(
            cacheKey,
            JSON.stringify(contentType),
            'EX',
            TRIGGER_CACHE_TTL,
            'NX'
        )
    }
    return contentType
}
