import prisma from '../../db'
import RedisClient from '../redis'
import { RedisKeys } from '../../constants/redis-keys'

const TRIGGER_CACHE_TTL = 60 * 60 * 24
const redis = RedisClient.getInstance()
async function getChannelTypeId(value: string): Promise<{ id: number } | null> {
    try {
        return await prisma.channelType.findUnique({
            where: {
                name: value,
            },
            select: {
                id: true,
            },
        })
    } finally {
        await prisma.$disconnect()
    }
}

export async function getCachedChannelTypeId(
    value: string
): Promise<{ id: number } | null> {
    const cacheKey = RedisKeys.CHANNEL_TYPE_ID_KEY(value)

    const cache = await redis.get(cacheKey)
    if (cache) {
        return JSON.parse(cache) as { id: number }
    }

    const channelType = await getChannelTypeId(value)
    if (channelType) {
        await redis.set(
            cacheKey,
            JSON.stringify(channelType),
            'EX',
            TRIGGER_CACHE_TTL,
            'NX'
        )
    }
    return channelType
}
