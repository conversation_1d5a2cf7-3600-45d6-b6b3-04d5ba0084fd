import prisma from '../../db'
import RedisClient from '../redis'
import { RedisKeys } from '../../constants/redis-keys'

const TRIGGER_CACHE_TTL = 60 * 60 * 24
const redis = RedisClient.getInstance()
async function getParameterTypeId(
    value: string
): Promise<{ id: number } | null> {
    try {
        return await prisma.parameterType.findUnique({
            where: {
                name: value,
            },
            select: {
                id: true,
            },
        })
    } finally {
        await prisma.$disconnect()
    }
}

export async function getCachedParameterTypeId(
    value: string
): Promise<{ id: number } | null> {
    const cacheKey = RedisKeys.PARAM_TYPE_ID_KEY(value)

    const cache = await redis.get(cacheKey)
    if (cache) {
        return JSON.parse(cache) as { id: number }
    }

    const paramType = await getParameterTypeId(value)
    if (paramType) {
        await redis.set(
            cacheKey,
            JSON.stringify(paramType),
            'EX',
            TRIGGER_CACHE_TTL,
            'NX'
        )
    }
    return paramType
}
