import { sendSlackMessage } from './send-message'

// async function run() {
//     const subject = '🚨 Deployment Failure Alert'
//     const message =
//         'The latest deployment to production failed.\nEngineers have been notified and are currently investigating the issue.'
//
//     await sendSlackMessage(36, message, subject)
// }
// run()

async function creditCard() {
    const subject = 'Your heatmap trial is ready—just one more step!'
    const message = `Hey there,

You're so close to unlocking powerful insights across your site.

Complete your signup by entering your credit card details to kick off your trial—*remember, your trial won't officially start until you do*.

Once activated, you'll gain instant access to actionable insights and analytics tailored to drive meaningful growth.

Let's get your trial activated today and start uncovering hidden revenue together!

👉 <https://portal.heatmap.com|Complete Sign-up Now>`
    await sendSlackMessage(36, message, subject)
}

async function ipBlocking() {
    const subject = 'Exclude your team visits for crystal-clear insights'
    const message = `Hi {{firstName}},

We noticed you haven't configured IP blocking yet. Implementing this helps *exclude internal traffic* and gives you accurate insights into your actual customer data.

It takes less than 2 minutes—let’s set it up now for more precise analytics!

👉 <https://intercom.help/heatmapcom/en/articles/10023965-understanding-and-using-ip-blocking|Configure IP Blocking>`

    await sendSlackMessage(36, message, subject)
}

async function loginBookDemo() {
    const subject =
        'Let’s jumpstart your heatmap trial with a personalized demo'

    const message = `Hey {{firstName}},

It’s been {{daysSinceLastLogin}} days since you checked in—let's fix that!

With your scale (*over $5M GMV*), a quick, personalized demo can unlock massive opportunities. Let us show you exactly how Heatmap can boost your revenue.

👉 <https://meetings.hubspot.com/markomeco/demo?uuid=************************************|Book Your Demo>`

    await sendSlackMessage(36, message, subject)
}

async function loginCaseStudy() {
    const subject = 'See how brands boosted revenue 15% with heatmap'

    const message = `Hi {{firstName}},

It’s been a few days since your last login (*{{lastLoginDate}}*), and we miss you!

Top eCommerce brands are using Heatmap to boost their revenue by *over 15%*. See exactly how they did it—and how you can too.

📖 <https://www.heatmap.com/case-studies/how-that-works-used-heatmap-to-achieve-a-13-increase-in-revenue-per-session-for-a-growing-uk-fashion-brand|Read the Case Study>

Let’s get your trial back on track!`

    await sendSlackMessage(36, message, subject)
}

async function revenueScheduleCall() {
    const subject = 'Need help with revenue tracking? Let’s talk.'

    const message = `Hello!

Still not seeing your revenue data tracking properly?

This information is key to maximizing your site's profitability.

Schedule a quick call with our team—we’ll personally walk you through the solution, ensuring everything is properly set up.

Let's get you fully operational and capturing insights now!

👉 <https://portal.heatmap.com|Schedule Revenue Tracking Call>`

    await sendSlackMessage(36, message, subject)
}

async function revenueShareDocs() {
    const subject = "Revenue data missing? Here's your quick guide."

    const message = `Hi,

Your snippet is working, but we’re not seeing revenue data yet.

To start capturing crucial revenue insights, follow our straightforward troubleshooting guide.

Quickly resolve this and start identifying exactly which elements are driving revenue on your site.

Get your full revenue tracking working today!

👉 <https://intercom.help/heatmapcom/en/articles/10022307-troubleshooting-dots-visible-but-no-revenue-data|Resolve Revenue Tracking>`

    await sendSlackMessage(36, message, subject)
}

async function sessionScheduleCall() {
    const subject = "Let's solve your session tracking issue together"

    const message = `Hi there,

Looks like you're still experiencing trouble tracking session data.

Accurate session tracking is crucial for understanding user behavior and improving your site’s performance. Let’s hop on a quick call and resolve this swiftly, so you can get back to optimizing your revenue.

Our team is ready to help!

👉 <https://portal.heatmap.com|Schedule Tracking Help Call>`

    await sendSlackMessage(36, message, subject)
}

async function sessionShareDocs() {
    const subject = "Sessions not tracking? Here's how to fix it fast!"

    const message = `Hey,

Great job installing the heatmap snippet!

However, we noticed your session data isn't tracking yet, which limits your visibility into customer interactions.

Don’t worry—this is usually an easy fix. Check out our detailed troubleshooting guide below and you'll be tracking sessions accurately in no time.

👉 <https://intercom.help/heatmapcom/en/articles/10022307-troubleshooting-dots-visible-but-no-revenue-data|Fix Session Tracking>`

    await sendSlackMessage(36, message, subject)
}

async function snippetScheduleCall() {
    const subject = 'Still need help with snippet installation?'

    const message = `Hi!

Looks like you're still having trouble with installing your snippet.

We’d love to help you get started so you can start benefiting from the insights Heatmap offers. Let's schedule a quick call—our team will guide you step-by-step to ensure you're fully set up. The sooner you're set, the quicker you'll find new revenue opportunities!

👉 <https://portal.heatmap.com|Schedule Installation Help>`

    await sendSlackMessage(36, message, subject)
}

async function snippetShareDocs() {
    const subject = 'Still need help with snippet installation?'

    const message = `Hey!

We noticed your heatmap snippet isn’t installed yet, meaning you're not yet tapping into the revenue insights available at your fingertips.

Installing the snippet is straightforward and takes just a few minutes.

We've put together an easy-to-follow installation guide to help you start gaining immediate value. Don’t let another day pass without actionable insights!

Snippet Installation Guide:

{{#if (eq platform 'shopify')}}
👉 <https://44015528.hs-sites.com/share/hubspotvideo/181826246573|Snippet Installation Video>
👉 <https://intercom.help/heatmapcom/en/articles/10024056-how-to-install-heatmap-snippets-on-your-shopify-store|Shopify Installation Guide>
{{else if (eq platform 'bigcommerce')}}
👉 <https://intercom.help/heatmapcom/en/articles/10024052-heatmap-big-commerce-snippet-installation|BigCommerce Installation>
{{else if (eq platform 'wordpress')}}
👉 <https://intercom.help/heatmapcom/en/articles/10508409-wordpress-woocommerce|WordPress Installation Guide>
{{else if (eq platform 'woocommerce')}}
👉 <https://intercom.help/heatmapcom/en/articles/10508409-wordpress-woocommerce|WooCommerce Installation Guide>
{{else}}
👉 <https://44015528.hs-sites.com/share/hubspotvideo/181826246573|Snippet Installation Video>
{{/if}}`

    await sendSlackMessage(36, message, subject)
}

async function teamNotInvited() {
    const subject = 'heatmap is better with your team'

    const message = `Hey {{firstName}},

Great analytics become even more powerful when shared. Invite your team to Heatmap so you can collaborate and maximize insights together.

Inviting your team only takes seconds—let’s get everyone on board!

👉 <https://portal.heatmap.com|Invite Your Team>`

    await sendSlackMessage(36, message, subject)
}

async function weeklyEndTrial() {
    const subject = 'Your Weekly heatmap Insights are Here!'

    const message = `Hey {{firstName}},

Your trial has ended, but you've already uncovered valuable insights:
Here’s how your site ({{siteName}}) performed this week:
From _{{startDate}}_ to _{{endDate}}_

* Total Sessions: {{totalWeeklySessions}}
* Total Revenue: $ {{totalWeeklyRevenue}}
* Revenue Per Session (RPS): $ {{siteWideRPSWeekly}}
* Average Scroll Depth: {{siteWideScrollDepthWeekly}}%

Top 5 revenue-driving pages:
{{#each top5RPSPages}}
• <{{url}}|{{name}}>
{{/each}}

Keep it up! Check out detailed insights to drive even more revenue next week.

👉 <https://portal.heatmap.com|View Weekly Insights>`

    await sendSlackMessage(36, message, subject)
}

async function weeklyMidTrial() {
    const subject = 'Your Weekly heatmap Highlights'

    const message = `Hey {{firstName}},

You're halfway through your trial, and you're already uncovering valuable insights:
Here’s how your site ({{siteName}}) performed this week:
From _{{startDate}}_ to _{{endDate}}_

* Total Sessions: {{totalWeeklySessions}}
* Total Revenue: $ {{totalWeeklyRevenue}}
* Revenue Per Session (RPS): $ {{siteWideRPSWeekly}}
* Average Scroll Depth: {{siteWideScrollDepthWeekly}}%

Here's what's driving your revenue so far—your top 5 pages by Revenue Per Session:
{{#each top5RPSPages}}
• <{{url}}|{{name}}>
{{/each}}

Let's keep optimizing your site's performance!

👉 <https://portal.heatmap.com|See Detailed Insights>`

    await sendSlackMessage(36, message, subject)
}

async function weeklyRecurring() {
    const subject = 'Your Weekly heatmap Insights are Here!'

    const message = ` Hey {{firstName}},

    Here’s how your site ({{siteName}}) performed this week:

        From _{{startDate}}_ to _{{endDate}}_

    * Total Sessions: {{totalWeeklySessions}}
* Total Revenue: $ {{totalWeeklyRevenue}}
* Revenue Per Session (RPS): $ {{siteWideRPSWeekly}}
* Average Scroll Depth: {{siteWideScrollDepthWeekly}}%

    Top 5 revenue-driving pages:
    {{#each top5RPSPages}}
• <{{url}}|{{name}}>
    {{/each}}

        Keep it up! Check out detailed insights to drive even more revenue next week.

👉 <https://portal.heatmap.com|View Weekly Insights> `

    await sendSlackMessage(36, message, subject)
}

async function run() {
    await loginBookDemo()
}

run()
