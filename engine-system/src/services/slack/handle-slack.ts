import prisma from '../../db'
import { TokenEncryptor } from '../../utils/security/token-encyptor'
import { envConfig } from '../../env.constant'

const tokenEncryptor = new TokenEncryptor(envConfig.encryptionKey)
const slackChannelId = 2

export async function getSlackCredentials(idSite: number, triggerId?: number) {
    try {
        let sitePreference = triggerId
            ? await prisma.siteNotificationPreference.findUnique({
                  where: {
                      idSite_channelId_triggerNormalized: {
                          idSite: idSite,
                          channelId: slackChannelId,
                          triggerNormalized: triggerId,
                      },
                  },
                  select: {
                      idSite: true,
                      triggerNormalized: true,
                      destination: true,
                      channelId: true,
                      triggerId: true,
                  },
              })
            : null
        if (!sitePreference) {
            sitePreference = await prisma.siteNotificationPreference.findUnique(
                {
                    where: {
                        idSite_channelId_triggerNormalized: {
                            idSite: idSite,
                            channelId: slackChannelId,
                            triggerNormalized: 0,
                        },
                    },
                    select: {
                        idSite: true,
                        triggerNormalized: true,
                        destination: true,
                        channelId: true,
                        triggerId: true,
                    },
                }
            )
        }
        let decryptedToken: string | null = null
        if (sitePreference) {
            const siteToken = await prisma.siteToken.findUnique({
                where: {
                    idSite_channelId: {
                        idSite: idSite,
                        channelId: slackChannelId,
                    },
                },
                select: {
                    idSite: true,
                    accessTokenEncrypted: true,
                    iv: true,
                    tag: true,
                },
            })
            if (siteToken) {
                decryptedToken = tokenEncryptor.decrypt(
                    siteToken.accessTokenEncrypted,
                    siteToken.iv,
                    siteToken.tag
                )
            }
        }
        return { decryptedToken, sitePreference }
    } finally {
        await prisma.$disconnect()
    }
}
