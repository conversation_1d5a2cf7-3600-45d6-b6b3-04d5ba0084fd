type Priority = 'low' | 'high' | 'medidum'

interface BaseNotification {
    id?: string
    priority?: Priority
    timestamp?: Date
}

export interface EmailNotificationContent extends BaseNotification {
    type: 'email'
    subject: string
    body: string
    html?: string
    from?: string
    to: string[]
    cc?: string[]
    bcc?: string[]
    attachments?: Array<{
        filename: string
        content: Buffer | string
        contentType?: string
    }>
}

export interface SlackNotificationContent extends BaseNotification {
    type: 'slack'
    channel: string
    text: string
    blocks?: Array<{
        type: 'section' | 'header' | 'divider'
        text?: {
            type: 'plain_text' | 'mrkdwn'
            text: string
        }
        fields?: Array<{
            type: 'plain_text' | 'mrkdwn'
            text: string
        }>
    }>
    threadTs?: string
    username?: string
    iconEmoji?: string
    iconUrl?: string
}

export interface SmsNotificationContent extends BaseNotification {
    type: 'sms'
    to: string // phone nunber
    message: string
    sender?: string
    unicode?: boolean
}

export interface PushNotificationContent extends BaseNotification {
    type: 'push'
    title: string
    body: string
    deviceTokens: string[]
    data?: Record<string, string>
    badge?: number
    sound?: string
    icon?: string
    clickAction?: string
}

export type NotificationContent =
    | EmailNotificationContent
    | SlackNotificationContent
    | SmsNotificationContent
    | PushNotificationContent

export interface NotificationResult {
    success: boolean
    notificationId: string
    channel: NotificationContent['type']
    timestamp: Date
    error?: string
    metadata?: Record<string, unknown>
}
