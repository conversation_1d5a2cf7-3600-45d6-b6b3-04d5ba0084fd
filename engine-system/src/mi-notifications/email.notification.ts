import { NotificationSender } from './abstracts/notification-sender.abstract'
import {
    EmailNotificationContent,
    NotificationResult,
} from './interfaces/notification.interface'
import { NotificationType } from './enums/notification-type.enum'
import { SmtpConfig } from './interfaces/config.interface'

export class EmailNotification extends NotificationSender<EmailNotificationContent> {
    readonly channel = NotificationType.EMAIL as const

    constructor(private config: SmtpConfig) {
        super()
    }

    async send(content: EmailNotificationContent): Promise<NotificationResult> {
        try {
            this.validateContent(content)

            if (!content.to.length) {
                throw new Error('no recipients')
            }
            if (!content.subject || !content.body) {
                throw new Error('subject and body required')
            }
            console.log(
                `sending email notification: ${JSON.stringify(content)}`
            )

            return {
                channel: this.channel,
                notificationId: crypto.randomUUID(),
                timestamp: new Date(),
                metadata: {
                    recipientCount: content.to.length,
                    hasAttachments: !!content.attachments?.length,
                },
                success: true,
            }
        } catch (error) {
            return {
                success: false,
                notificationId: crypto.randomUUID(),
                timestamp: new Date(),
                channel: this.channel,
                error: error instanceof Error ? error.message : 'Unknown Error',
            }
        }
    }
}
