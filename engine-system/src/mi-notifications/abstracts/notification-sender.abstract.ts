import {
    NotificationContent,
    NotificationResult,
} from '../interfaces/notification.interface'

export abstract class NotificationSender<T extends NotificationContent> {
    abstract readonly channel: T['type']

    abstract send(content: T): Promise<NotificationResult>

    protected validateContent(content: T): void {
        if (content.type !== this.channel) {
            throw new Error(
                `Invalid notification type. Expected ${this.channel}, got ${content.type}`
            )
        }
    }
}
