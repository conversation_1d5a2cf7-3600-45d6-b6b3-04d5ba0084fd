import { NotificationSender } from '../abstracts/notification-sender.abstract'
import {
    SlackNotificationContent,
    NotificationResult,
} from '../interfaces/notification.interface'
import { NotificationType } from '../enums/notification-type.enum'
import { SlackConfig } from '../interfaces/config.interface'

export class SlackNotification extends NotificationSender<SlackNotificationContent> {
    readonly channel = NotificationType.SLACK as const

    constructor(private config: SlackConfig) {
        super()
    }

    async send(content: SlackNotificationContent): Promise<NotificationResult> {
        try {
            this.validateContent(content)
            console.log(
                `sending email notification: ${JSON.stringify(content)}`
            )

            return {
                channel: this.channel,
                notificationId: crypto.randomUUID(),
                timestamp: new Date(),
                metadata: {},
                success: true,
            }
        } catch (error) {
            return {
                success: false,
                notificationId: crypto.randomUUID(),
                timestamp: new Date(),
                channel: this.channel,
                error: error instanceof Error ? error.message : 'Unknown Error',
            }
        }
    }
}
