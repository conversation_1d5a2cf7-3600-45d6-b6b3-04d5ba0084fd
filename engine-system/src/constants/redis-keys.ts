export const RedisKeys = {
    // A single key definition for daily, weekly, active, and trial bot jobs
    BOT_JOB_KEY: (
        type: 'active' | 'trial',
        interval: 'daily' | 'weekly',
        date: string,
        jobId: string
    ) => `bot:${type}:${interval}:${date}:${jobId}`,

    SITE_STATE_KEY: (idSite: number) => `site:${idSite}:state`,

    NOTIFICATIONS_SENT_CHANNEL_KEY: (
        idSite: number,
        channel: string = 'email'
    ) => `notifications:${idSite.toString()}:sent:${channel}`,

    SITE_TRIGGER_CHANNEL_KEY: (
        idSite: number,
        trigger: string,
        channel: string = 'email'
    ) => `site:${idSite}:${trigger}:${channel}`,

    NOTIFICATION_EMAIL_LOG_KEY: (env: string) =>
        `notifications:${env}:email_log`,

    TRIGGER_ID_KEY: (triggerValue: string) => `triggers:id:${triggerValue}`,

    CONTENT_TYPE_ID_KEY: (value: string) => `content-types:id:${value}`,

    TRIGGER_TYPE_ID_KEY: (value: string) => `trigger-types:id:${value}`,

    CHANNEL_TYPE_ID_KEY: (value: string) => `channel-types:id:${value}`,

    PARAM_TYPE_ID_KEY: (value: string) => `parameter-types:id:${value}`,

    PENDING_NOTIFICATIONS_LOG_KEY: () => `pending-notification-logs`,

    // SITE_TRIGGER_KEY: (idSite: number, trigger: string | number) =>
    //     `site:${idSite}:${trigger.toString()}`,
    // NOTIFICATIONS_SENT_KEY: (idSite: number) =>
    //     `notifications:${idSite.toString()}:sent`,
}
