// import { WebClient } from '@slack/web-api';
// import { config } from 'dotenv';
//
//
// config();
//
// export class SlackService {
//     private slackClient: WebClient;
//
//     constructor() {
//         const token = `${process.env.SLACK_BOT_TOKEN}`;
//         this.slackClient = new WebClient(token);
//     }
//
//     async sendMessage(message: string, channelId: string) {
//         let channel = channelId ? channelId : `${process.env.SLACK_TARGET_CHANNEL_ID}`;
//         if(!channel){
//             console.error(`Slack bot token missing`);
//             return;
//         }
//
//         return this.slackClient.chat.postMessage({
//           channel,
//           text: message,
//         });
//     }
//
//     async sendDirectMessage(message: string, email: string){
//         const result = await this.slackClient.users.lookupByEmail({ email });
//         const userId = result.user?.id;
//         if(userId){
//             this.slackClient.chat.postMessage({
//                 channel: userId,
//                 text: message,
//             });
//         } else {
//             console.error(`Account with email: ${email} not registered on slack`)
//         }
//         return;
//     }
// }
