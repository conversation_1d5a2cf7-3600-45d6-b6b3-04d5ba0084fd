import {
    ConnectionConfig,
    BaseMessage,
    SendResult,
} from '../types/notification'

/**
 * Base Connection class for messaging and notifications
 */
export abstract class Connection<
    TConfig extends ConnectionConfig,
    TMessage extends BaseMessage,
> {
    protected config: TConfig
    protected isInitialized: boolean

    constructor(config: TConfig) {
        this.config = config
        this.isInitialized = false
    }

    /**
     * Initialize the connection
     * @returns Promise<boolean> Whether initialization was successful
     */
    abstract initialize(): Promise<boolean>

    /**
     * Send a message through the connection
     * @param message The message to send
     * @returns Promise<SendResult> Result of the send operation
     */
    abstract send(message: TMessage): Promise<SendResult>

    /**
     * Validate that required parameters exist
     * @param params The parameters to validate
     * @param required Array of required parameter keys
     * @returns Array<string> Missing parameters, empty if all present
     */
    protected validateRequiredParams(
        params: Record<string, any>,
        required: string[]
    ): string[] {
        const missing: string[] = []

        required.forEach((param) => {
            if (
                params[param] === undefined ||
                params[param] === null ||
                params[param] === ''
            ) {
                missing.push(param)
            }
        })

        return missing
    }
}
