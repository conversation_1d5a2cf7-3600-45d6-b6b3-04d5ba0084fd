import { Connection } from './base'
import { NotificationService } from './notification-service'
import { SmtpConnection } from './smtp'

/**
 * Available connection types
 */
export type ConnectionType = 'smtp' | 'sms' | 'slack' | 'hubspot'

/**
 * Factory for creating different types of connections
 */
export class ConnectionFactory {
    /**
     * Create a connection instance based on type
     * @param type The connection type (e.g., 'smtp', 'sms', 'hubspot')
     * @param config Configuration for the connection
     * @returns A connection instance
     */
    static createConnection(
        type: ConnectionType,
        config: any = {}
    ): Connection<any, any> {
        const connectionType = type.toLowerCase() as ConnectionType

        if (connectionType === 'smtp') {
            return SmtpConnection.getInstance(config)
        }

        throw new Error(`Unsupported connection type: ${type}`)
    }

    /**
     * Get the notification service (convenience method)
     */
    static getNotificationService(smtpConfig = {}): NotificationService {
        return NotificationService.getInstance(smtpConfig)
    }
}
