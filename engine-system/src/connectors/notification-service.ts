import { SmtpConnection } from './smtp'
import { SendResult, EmailMessage, SmtpConfig } from '../types/notification'

/**
 * High-level notification service for easy use
 */
export class NotificationService {
    private readonly smtpConnection: SmtpConnection
    private static instance: NotificationService | null = null

    private constructor(smtpConfig: SmtpConfig = {}) {
        this.smtpConnection = SmtpConnection.getInstance(smtpConfig)
    }

    /**
     * Get singleton instance
     */
    public static getInstance(
        smtpConfig: SmtpConfig = {}
    ): NotificationService {
        if (!this.instance) {
            this.instance = new NotificationService(smtpConfig)
        }
        return this.instance
    }

    /**
     * Initialize all connections
     */
    public async initialize(): Promise<boolean> {
        return this.smtpConnection.initialize()
    }

    /**
     * Send an HTML email
     * @param to Recipient email address
     * @param subject Email subject
     * @param htmlContent HTML content
     * @param options Additional email options
     */
    public async sendHtmlEmail(
        to: string,
        subject: string,
        htmlContent: string,
        options: Partial<
            Omit<EmailMessage, 'html' | 'text' | 'to' | 'subject'>
        > = {}
    ): Promise<SendResult> {
        return this.smtpConnection.sendHtml(to, subject, htmlContent, options)
    }

    /**
     * Send a text email
     * @param to Recipient email address
     * @param subject Email subject
     * @param textContent Text content
     * @param options Additional email options
     */
    public async sendTextEmail(
        to: string,
        subject: string,
        textContent: string,
        options: Partial<
            Omit<EmailMessage, 'html' | 'text' | 'to' | 'subject'>
        > = {}
    ): Promise<SendResult> {
        return this.smtpConnection.sendText(to, subject, textContent, options)
    }
}
