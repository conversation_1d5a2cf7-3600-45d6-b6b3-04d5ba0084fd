import * as nodemailer from 'nodemailer'
import { SendR<PERSON>ult, SmtpConfig, EmailMessage } from '../types/notification'
import { Connection } from './base'
import '../config/config'
import { envConfig } from '../env.constant'

/**
 * Extended email message with raw HTML support
 */
export interface RawHtmlEmailMessage extends EmailMessage {
    rawHtml?: string
}

/**
 * SMTP Connection implementation using direct SMTP with raw HTML support
 */
export class SmtpConnection extends Connection<
    SmtpConfig,
    RawHtmlEmailMessage
> {
    private transporter: nodemailer.Transporter | null
    private static instance: SmtpConnection | null = null

    constructor(config: SmtpConfig = {}) {
        // Merge provided config with environment variables for defaults
        const mergedConfig = SmtpConnection.getDefaultConfig(config)
        super(mergedConfig)
        this.transporter = null
    }

    /**
     * Get singleton instance with default config
     */
    public static getInstance(config: SmtpConfig = {}): SmtpConnection {
        if (!this.instance) {
            this.instance = new SmtpConnection(config)
        } else if (Object.keys(config).length > 0) {
            // Update config if provided
            this.instance.config = { ...this.instance.config, ...config }
            this.instance.isInitialized = false // Force re-initialization with new config
        }
        return this.instance
    }

    /**
     * Merge provided config with environment variables
     */
    private static getDefaultConfig(config: SmtpConfig): SmtpConfig {
        return {
            defaultFrom:
                config.defaultFrom ||
                envConfig.smtp.defaultEmailFrom ||
                '<EMAIL>',
            defaultReplyTo: config.defaultReplyTo,
            defaultSubject: config.defaultSubject || 'Notification',
        }
    }

    /**
     * Initialize the SMTP connection
     * @returns Promise<boolean> Whether initialization was successful
     */
    async initialize(): Promise<boolean> {
        try {
            // Skip if already initialized
            if (this.isInitialized) {
                return true
            }

            // Check required SMTP configuration
            const requiredEnvVars = ['SMTP_HOST', 'SMTP_PORT']
            const missingEnvVars = requiredEnvVars.filter(
                (varName) => !process.env[varName]
            )

            if (missingEnvVars.length > 0) {
                throw new Error(
                    `Missing required SMTP environment variables: ${missingEnvVars.join(', ')}`
                )
            }

            // Create SMTP transporter
            this.transporter = nodemailer.createTransport({
                host: envConfig.smtp.host,
                port: Number(envConfig.smtp.port),
                secure: envConfig.smtp.secure === 'true',
                auth: {
                    user: envConfig.smtp.username,
                    pass: envConfig.smtp.password,
                },
                connectionTimeout: 10000,
                greetingTimeout: 10000,
                socketTimeout: 10000,
            })

            // Verify connection
            await this.transporter.verify()

            this.isInitialized = true
            return true
        } catch (error) {
            console.error('Failed to initialize SMTP connection:', error)
            this.isInitialized = false
            throw error
        }
    }

    /**
     * Process raw HTML content
     * @param rawHtml The raw HTML content
     * @returns string Processed HTML content
     */
    private processRawHtml(rawHtml: string): string {
        // This is a simple implementation for processing raw HTML
        // You can add more complex processing logic as needed
        return rawHtml
    }

    /**
     * Send an email through the configured SMTP provider
     * @param message The email message to send
     * @returns Promise<SendResult> Result of the send operation
     */
    async send(message: RawHtmlEmailMessage): Promise<SendResult> {
        try {
            // Auto-initialize if not already initialized
            if (!this.isInitialized) {
                await this.initialize()
            }

            if (!this.transporter) {
                throw new Error('Connection not initialized properly.')
            }

            // Apply default values from config
            const emailMessage: EmailMessage = {
                from: message.from || this.config.defaultFrom || '',
                to: message.to,
                subject: message.subject || this.config.defaultSubject || '',
                text: message.text || '',
                html: message.html || '',
                cc: message.cc,
                bcc: message.bcc,
                attachments: message.attachments,
                replyTo: message.replyTo || this.config.defaultReplyTo,
            }

            // Process raw HTML if specified
            if (message.rawHtml) {
                try {
                    const processedHtml = this.processRawHtml(message.rawHtml)
                    emailMessage.html = processedHtml

                    // Generate text version if not provided
                    if (!emailMessage.text) {
                        // Simple HTML to text conversion
                        emailMessage.text = processedHtml
                            .replace(/<[^>]*>/g, '')
                            .replace(/\s+/g, ' ')
                            .trim()
                    }
                } catch (error) {
                    return {
                        success: false,
                        error: `Raw HTML processing failed: ${error}`,
                    }
                }
            }

            // Validate required message parameters
            const requiredParams = ['to']
            const missingParams = this.validateRequiredParams(
                emailMessage,
                requiredParams
            )

            if (missingParams.length > 0) {
                return {
                    success: false,
                    error: `Missing required message parameters: ${missingParams.join(', ')}`,
                }
            }

            // Require either text or html content
            if (!emailMessage.text && !emailMessage.html) {
                return {
                    success: false,
                    error: 'Email must have either text or HTML content',
                }
            }

            // Send email using nodemailer
            const result = await this.transporter.sendMail(emailMessage)
            return {
                success: true,
                messageId: result.messageId,
                response: result.response,
            }
        } catch (error: any) {
            console.error('Failed to send email:', error)
            return {
                success: false,
                error: error.message,
            }
        }
    }

    /**
     * Send an email with raw HTML content
     * @param to Recipient email address
     * @param subject Email subject
     * @param htmlContent HTML content
     * @param options Optional additional email options
     * @returns Promise<SendResult> Result of the send operation
     */
    async sendHtml(
        to: string,
        subject: string,
        htmlContent: string,
        options: Partial<
            Omit<EmailMessage, 'html' | 'text' | 'to' | 'subject'>
        > = {}
    ): Promise<SendResult> {
        const message: RawHtmlEmailMessage = {
            ...options,
            to,
            subject,
            html: htmlContent,
        }

        return this.send(message)
    }

    /**
     * Send a text email
     * @param to Recipient email address
     * @param subject Email subject
     * @param textContent Text content
     * @param options Optional additional email options
     * @returns Promise<SendResult> Result of the send operation
     */
    async sendText(
        to: string,
        subject: string,
        textContent: string,
        options: Partial<
            Omit<EmailMessage, 'html' | 'text' | 'to' | 'subject'>
        > = {}
    ): Promise<SendResult> {
        const message: RawHtmlEmailMessage = {
            ...options,
            to,
            subject,
            text: textContent,
        }

        return this.send(message)
    }
}
