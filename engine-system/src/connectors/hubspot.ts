// import axios from 'axios';
// import { config } from 'dotenv';
// import { HubSpotEmailMessage } from '../types/notification';
//
// config();
//
// export class HubspotEmailService {
//
//   constructor() {}
//
//   async sendEmail(data: HubSpotEmailMessage) {
//
//     const axiosOptions = {
//         url: `${process.env.HUBSPOT_REST_API_BASE_URL}transactional/single-email/send`,
//         method: "POST",
//         headers: {
//             Authorization: `Bearer ${process.env.HUBSPOT_ACCESS_TOKEN}`,
//             'Content-Type': 'application/json',
//         },
//         data: {
//             message: {
//                 to: data.to,
//                 from: data.from,
//                 subject: data.subject,
//                 cc: data.cc,
//                 bcc: data.bcc
//             },
//             html: data.html,
//             customProperties: data.params
//         }
//     }
//     try {
//         await axios.request(axiosOptions)
//         return;
//     } catch (error:any) {
//         console.error('HubSpot Email Error:', error.response?.data || error.message);
//         return;
//     }
//   }
// }
