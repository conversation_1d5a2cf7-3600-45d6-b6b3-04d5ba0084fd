export enum TRIGGER_TYPE {
    CREDIT_CARD = 'credit-card',
    SNIPPET_NOT_INSTALLED_SHARE_DOCS = 'snippet-share-docs',
    SNIPPET_NOT_INSTALLED_SCHEDULE_CALL = 'snippet-schedule-call',
    SESSION_NOT_TRACKING_SHARE_DOCS = 'session-share-docs',
    SESSION_NOT_TRACKING_SCHEDULE_CALL = 'session-schedule-call',
    REVENUE_NOT_TRACKING_SHARE_DOCS = 'revenue-share-docs',
    REVENUE_NOT_TRACKING_SCHEDULE_CALL = 'revenue-schedule-call',
    WEEKLY_SUMMARY_MID_TRIAL = 'weekly-summary-mid-trial',
    WEEKLY_SUMMARY_END_TRIAL = 'weekly-summary-end-trial',
    WEEKLY_SUMMARY_RECURRING = 'weekly-summary-recurring',
    LOGIN_INACTIVE_SHARE_CASE_STUDY = 'login-case-study',
    LOGIN_INACTIVE_BOOK_DEMO = 'login-book-demo',
    IP_BLOCKING_NOT_IMPLEMENTED = 'ip-blocking-not-implemented',
    TEAM_MATES_NOT_INVITED = 'team-not-invited',
    NPS = 'nps',
}
