import './config/config'
import cron from 'cron-validate'

const EXPECTED_NODE_ENV = ['development', 'production'] as const
const EXPECTED_BUILD_TARGET = ['dev', 'prod']

type NODE_ENV_TYPE = (typeof EXPECTED_NODE_ENV)[number]

class EnvConfigurationError extends Error {
    constructor(message: string) {
        super(message)
        this.name = 'EnvConfigurationError'
        Error.captureStackTrace(this, EnvConfigurationError)
    }
}

function validateRequiredEnv(
    varName: string,
    value: string | undefined
): string {
    if (!value) {
        console.error(`Missing required env variable`)
        console.error(`${varName}:`, value)
        throw new EnvConfigurationError(`Missing required env (${varName})`)
    }
    return value
}
function isValidCron(cronString: string): string {
    const cronResult = cron(cronString)
    if (!cronResult.isValid()) {
        throw new EnvConfigurationError(`Invalid cron string "${cronString}"`)
    }
    return cronString
}

function validateNodeEnv(
    env: string | undefined
): asserts env is NODE_ENV_TYPE {
    if (!env || !EXPECTED_NODE_ENV.includes(env as NODE_ENV_TYPE)) {
        console.error("NODE_ENV must be either 'development' or 'production'")
        throw new EnvConfigurationError('Invalid NODE_ENV value')
    }
}

export const envConfig = (() => {
    const nodeEnv = (() => {
        validateNodeEnv(process.env.NODE_ENV)
        return process.env.NODE_ENV
    })()
    const port = Number.parseInt(
        validateRequiredEnv('PORT', process.env.PORT || '3000')
    )
    const redisHost = validateRequiredEnv('REDIS_HOST', process.env.REDIS_HOST)
    const redisPort = Number.parseInt(
        validateRequiredEnv('REDIS_PORT', process.env.REDIS_PORT)
    )
    // const rabbitUrl = validateRequiredEnv(
    //     'RABBIT_MQ_HOST',
    //     process.env.RABBIT_MQ_HOST
    // )
    const rabbitHost = validateRequiredEnv(
        'RABBITMQ_HOST',
        process.env.RABBITMQ_HOST
    )
    const rabbitUsername = validateRequiredEnv(
        'RABBITMQ_USERNAME',
        process.env.RABBITMQ_USERNAME
    )
    const rabbitPassword = validateRequiredEnv(
        'RABBITMQ_PASSWORD',
        process.env.RABBITMQ_PASSWORD
    )
    const smtpHost = validateRequiredEnv('SMTP_HOST', process.env.SMTP_HOST)
    const smtpPort = validateRequiredEnv('SMTP_PORT', process.env.SMTP_PORT)
    const smtpSecure = validateRequiredEnv(
        'SMTP_SECURE',
        process.env.SMTP_SECURE
    )
    const smtpUsername = validateRequiredEnv(
        'SMTP_USERNAME',
        process.env.SMTP_USERNAME
    )
    const smtpPassword = validateRequiredEnv(
        'SMTP_PASSWORD',
        process.env.SMTP_PASSWORD
    )
    const defaultEmailFrom = validateRequiredEnv(
        'DEFAULT_EMAIL_FROM',
        process.env.DEFAULT_EMAIL_FROM
    )
    const trialSitesSchedule = isValidCron(
        validateRequiredEnv(
            'TRIAL_SITES_SCHEDULE',
            process.env.TRIAL_SITES_SCHEDULE
        )
    )
    const activeSitesSchedule = isValidCron(
        validateRequiredEnv(
            'ACTIVE_SITES_SCHEDULE',
            process.env.ACTIVE_SITES_SCHEDULE
        )
    )
    const weekMetricsSchedule = isValidCron(
        validateRequiredEnv(
            'WEEKLY_METRICS_SCHEDULE',
            process.env.WEEKLY_METRICS_SCHEDULE
        )
    )
    const loggerToggle = validateRequiredEnv(
        'LOGGER_TOGGLE',
        process.env.LOGGER_TOGGLE
    )
    validateRequiredEnv('DATABASE_URL', process.env.DATABASE_URL)

    const emailOn = validateRequiredEnv('EMAIL_ON', process.env.EMAIL_ON)
    const runSeed = validateRequiredEnv('RUN_SEED', process.env.RUN_SEED)
    const buildTarget = validateRequiredEnv(
        'BUILD_TARGET',
        process.env.BUILD_TARGET
    ).trim()

    if (!EXPECTED_BUILD_TARGET.includes(buildTarget)) {
        throw new EnvConfigurationError(
            'Invalid build target, expected "dev" or "prod"'
        )
    }

    const heatmapContextBaseUrl = validateRequiredEnv(
        'HEATMAP_CONTEXT_BASE_URL',
        process.env.HEATMAP_CONTEXT_BASE_URL
    ).replace(/\/+$/, '')

    const slackClientId = validateRequiredEnv(
        'SLACK_CLIENT_ID',
        process.env.SLACK_CLIENT_ID
    )
    const slackClientSecret = validateRequiredEnv(
        'SLACK_CLIENT_SECRET',
        process.env.SLACK_CLIENT_SECRET
    )
    const slackRedirectBaseUrl = validateRequiredEnv(
        'SLACK_REDIRECT_BASE_URL',
        process.env.SLACK_REDIRECT_BASE_URL
    )
    const encryptionKey = validateRequiredEnv(
        'ENCRYPTION_KEY',
        process.env.ENCRYPTION_KEY
    )

    const observationUrl = validateRequiredEnv(
        'OBSERVATION_URL',
        process.env.OBSERVATION_URL
    ).replace(/\/+$/, '')

    return Object.freeze({
        nodeEnv,
        port,
        trialSitesSchedule,
        activeSitesSchedule,
        weekMetricsSchedule,
        redis: {
            host: redisHost,
            port: redisPort,
        },
        heatmapContextBaseUrl,
        buildTarget,
        rabbit: {
            host: rabbitHost,
            password: rabbitPassword,
            username: rabbitUsername,
        },
        slack: {
            clientId: slackClientId,
            clientSecret: slackClientSecret,
            redirectBaseUrl: slackRedirectBaseUrl,
        },
        encryptionKey,
        observationUrl,
        smtp: {
            host: smtpHost,
            port: smtpPort,
            secure: smtpSecure,
            username: smtpUsername,
            password: smtpPassword,
            defaultEmailFrom,
        },
        loggerToggle: loggerToggle === 'true',
        emailOn: emailOn === 'true',
        runSeed: runSeed === 'true',
    })
})()
