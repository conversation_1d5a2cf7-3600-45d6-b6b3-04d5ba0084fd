export type WebsiteInfo = {
    platform: string
    url: string
    id: number
    name: string
    lead: boolean
    features: string[]
}

export type ExtractedSiteData = {
    start_date: string // iso format
    end_date: string // iso format
    website: WebsiteInfo
}

export interface RawData {
    category: string[]
    chart: {
        [key: string]: number[]
    }
    legend: {
        [key: string]: string
    }
}

export interface FormattedEntry {
    date: string
    [metricKey: string]: string | number
}

export interface MetricStats {
    sum: number
    avg: number
    max: number
    min: number
    displayName: string
}

export interface AggregatedStats {
    [metricKey: string]: MetricStats
}

export interface FormattedByMetric {
    [metricKey: string]: {
        displayName: string
        data: {
            date: string
            value: number
        }[]
    }
}

export type ApiResponse<T> = {
    status: string
    data: T
    cused: boolean
    requestId: string
}
