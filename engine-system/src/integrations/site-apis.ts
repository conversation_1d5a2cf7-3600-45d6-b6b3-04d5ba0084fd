import Logger from '../utils/logger'
import { SiteState, SummaryMetrics } from '../types/site'
import axios from 'axios'
import { AggregatedStats } from './types'
import {
    ApiResponse,
    ExtractedSite,
    PaginatedApiResponse,
    Plan,
} from '../types/apis'
import { Transformer } from './transformer'
import { envConfig } from '../env.constant'

export class SiteApi {
    private readonly baseUrl: string
    // private readonly apiKey: string
    // private readonly httpClient: AxiosInstance
    private readonly logger: ReturnType<typeof Logger.getInstance>

    constructor(
        baseUrl: string = envConfig.heatmapContextBaseUrl,
        logger: ReturnType<typeof Logger.getInstance> = Logger.getInstance()
    ) {
        this.baseUrl = baseUrl
        this.logger = logger
    }

    async fetchSummaryMetrics(
        idSite: number,
        startDate: Date,
        endDate: Date
    ): Promise<ApiResponse<SummaryMetrics>['data']> {
        const url = `${this.baseUrl}/metrics/${idSite}`
        const start = startDate.toISOString().split('T')[0]
        const end = endDate.toISOString().split('T')[0]

        try {
            const response = await axios.get<ApiResponse<SummaryMetrics>>(url, {
                params: {
                    start: start,
                    end: end,
                },
            })
            return response.data.data
        } catch (error) {
            this.logger.error(
                `Failed to fetch site details for site id ${idSite}.`
            )
            throw error
        }
    }
    async fetchSiteStats(idSite: number, startDate: Date, endDate: Date) {
        const start = startDate.toISOString().split('T')[0]
        const end = endDate.toISOString().split('T')[0]

        const dateRange = [start, end].join(',')
        const payload = {
            idSite,
            date: dateRange,
            request: 'chart',
        }

        try {
            const response = await axios.post(
                this.baseUrl + '/statistics',
                payload,
                {
                    headers: { 'Content-Type': 'application/json' },
                }
            )
            const data = response.data
            return data
        } catch (error) {
            console.error(error)
        }
    }

    async getOnlyTransformedStats(
        idSite: number,
        startDate: Date,
        endDate: Date
    ): Promise<AggregatedStats> {
        const data = await this.fetchSiteStats(idSite, startDate, endDate)
        return Transformer.calculateStats(data.data)
    }

    async fetchSiteDetails(
        idSite: number
    ): Promise<ApiResponse<SiteState>['data']> {
        try {
            const response = await axios.get<ApiResponse<SiteState>>(
                `${this.baseUrl}/sites/${idSite}`,
                {
                    headers: {
                        Accept: 'application/json',
                    },
                }
            )
            this.logger.info('Fetched site details for site :', idSite)

            return response.data.data
        } catch (error) {
            this.logger.error(
                `Failed to fetch site details for site id ${idSite}.`
            )
            throw error
        }
    }

    async fetchAllTrailingSites(): Promise<ExtractedSite[]> {
        return this.fetchSitesByStatus('trialing')
    }
    async fetchAllPayingSites(): Promise<ExtractedSite[]> {
        return this.fetchSitesByStatus('active')
    }
    async fetchAllInActiveSites(): Promise<ExtractedSite[]> {
        return this.fetchSitesByStatus('inactive')
    }
    async fetchAllCancelledSites(): Promise<ExtractedSite[]> {
        return this.fetchSitesByStatus('cancelled')
    }
    async fetchAllPendingSites(): Promise<ExtractedSite[]> {
        return this.fetchSitesByStatus('pending')
    }
    async fetchSitesByStatus(
        status:
            | 'trialing'
            | 'active'
            | 'inactive'
            | 'cancelled'
            | 'expired'
            | 'upgraded'
            | 'pending'
    ): Promise<ExtractedSite[]> {
        const baseUrl = this.baseUrl + '/sites'
        try {
            const data = await this.fetchAllRecords<Plan>(baseUrl, {
                status: status,
            })

            const extracted: ExtractedSite[] = data
                .filter(
                    (entry) =>
                        Array.isArray(entry.websites) &&
                        entry.websites.length > 0
                )
                .flatMap((entry) => {
                    const email = entry.created_by.email
                    return entry.websites.map((site) => {
                        return {
                            idSite: site.id,
                            siteName: site.name,
                            siteUrl: site.url,
                            email: email,
                            platform: site.platform,
                            lead: site.lead,
                            features: site.features,
                        }
                    })
                })

            return extracted
        } catch (error) {
            console.error('Error fetching or processing data:', error)
            return []
        }
    }

    async fetchAllRecords<T>(
        baseUrl: string,
        additionalParams?: Record<string, string>
    ): Promise<PaginatedApiResponse<T>['data']> {
        let allRecords: any[] = []
        let currentPage = 1
        let pageCount = 1

        while (currentPage <= pageCount) {
            try {
                const response = await axios.get<PaginatedApiResponse<T>>(
                    baseUrl,
                    { params: { page: currentPage, ...additionalParams } }
                )
                const data = response.data
                allRecords.push(...data.data)

                pageCount = data.pagination.pageCount
                currentPage++
            } catch (error) {
                console.error(error)
                break
            }
        }
        return allRecords
    }
}
