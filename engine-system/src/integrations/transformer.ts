import {
    AggregatedStats,
    FormattedByMetric,
    FormattedEntry,
    RawData,
} from './types'

export class Transformer {
    static formatByDate(data: RawData): FormattedEntry[] {
        return data.category.map((date, index) => {
            const entry: FormattedEntry = { date: date }

            for (const [metricKey, values] of Object.entries(data.chart)) {
                entry[metricKey] = values[index]
            }
            return entry
        })
    }
    static calculateStats(data: RawData): AggregatedStats {
        const stats: AggregatedStats = {}

        for (const [metricKey, values] of Object.entries(data.chart)) {
            const displayName = data.legend[metricKey] || metricKey
            const sum = values.reduce((a, b) => a + b, 0)
            const avg = sum / values.length
            const max = Math.max(...values)
            const min = Math.min(...values)

            stats[metricKey] = {
                displayName,
                sum,
                avg,
                max,
                min,
            }
        }
        return stats
    }
    static formatByMetric(data: RawData): FormattedByMetric {
        const formattedByMetric: FormattedByMetric = {}
        for (const [metricKey, values] of Object.entries(data.chart)) {
            const displayName = data.legend[metricKey] || metricKey
            formattedByMetric[metricKey] = {
                displayName,
                data: values.map((value, index) => {
                    return {
                        date: data.category[index],
                        value,
                    }
                }),
            }
        }
        return formattedByMetric
    }
    static transformAndAggregateWithStats(data: RawData): {
        formattedByDate: FormattedEntry[]
        stats: AggregatedStats
        formattedByMetric: FormattedByMetric
    } {
        return {
            formattedByDate: Transformer.formatByDate(data),
            formattedByMetric: Transformer.formatByMetric(data),
            stats: Transformer.calculateStats(data),
        }
    }
    static formatDate(dateString: string): string {
        const [day, month, year] = dateString.split('.')
        const formattedDate = new Date(`${year}-${month}-${day}`)
        return formattedDate.toISOString().split('T')[0]
    }
}
