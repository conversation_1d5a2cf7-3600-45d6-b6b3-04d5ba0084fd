import { SiteApi } from '../../../integrations/site-apis'
import { SiteState } from '../../../types/site'
import { getFirstLongToken } from '../../../utils/extracting'
import Logger from '../../../utils/logger'
import { TRIGGER_TYPE } from '../../../enums/triggers'
import { TriggerJobData } from '../jobs'
const logger = Logger.getInstance()

export async function getPreviousWeekMetrics(
    state: SiteState,
    idSite: number
): Promise<Record<string, any> | null> {
    const today = new Date()

    const endDate = new Date(today)
    endDate.setDate(today.getDate() - 1)

    const startDate = new Date(endDate)
    startDate.setDate(endDate.getDate() - 6)

    let s = new SiteApi()
    const summaryMetrics = await s.fetchSummaryMetrics(
        idSite,
        startDate,
        endDate
    )
    const firstName =
        getFirstLongToken(state.userName) ||
        getFirstLongToken(state.full_name) ||
        state.siteName
    const r = {
        firstName: firstName,
        totalWeeklySessions: summaryMetrics.sessions,
        totalWeeklyRevenue: summaryMetrics.revenue,
        siteWideRPSWeekly: summaryMetrics.rps,
        siteWideScrollDepthWeekly: summaryMetrics.scroll_depth,
        top5RPSPages: summaryMetrics.top_5_pages_rps,
        startDate: startDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        }),
        endDate: endDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        }),
        siteName: state.siteName,
    }
    Logger.dbLogger(idSite.toString(), r)

    return r
}

export async function getTrialingWeeklyMetrics(
    state: SiteState,
    idSite: number
): Promise<Record<string, any> | null> {
    const trialStartDate = new Date(state.trialStartDate)
    const trialDaysElapsed = state.trialDaysElapsed

    let startDate: Date | null = null
    let endDate: Date | null = null
    if (trialDaysElapsed < 7) return null

    if (trialDaysElapsed >= 14) {
        const trialEnd = new Date(trialStartDate)
        trialEnd.setDate(trialStartDate.getDate() + 14)

        startDate = new Date(trialEnd)
        startDate.setDate(trialEnd.getDate() - 6) // 7 days back from end

        endDate = trialEnd
    } else if (trialDaysElapsed >= 7) {
        startDate = new Date(trialStartDate)
        endDate = new Date(trialStartDate)

        endDate.setDate(trialStartDate.getDate() + 6) // +6 to cover 7 days
    }

    let s = new SiteApi()
    const summaryMetrics = await s.fetchSummaryMetrics(
        idSite,
        startDate!,
        endDate!
    )
    const firstName =
        getFirstLongToken(state.userName) ||
        getFirstLongToken(state.full_name) ||
        state.siteName
    return {
        firstName: firstName,
        totalWeeklySessions: summaryMetrics.sessions,
        totalWeeklyRevenue: summaryMetrics.revenue,
        siteWideRPSWeekly: summaryMetrics.rps,
        siteWideScrollDepthWeekly: summaryMetrics.scroll_depth,
        top5RPSPages: summaryMetrics.top_5_pages_rps,
        startDate: startDate!.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        }),
        endDate: endDate!.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        }),
        siteName: state.siteName,
    }
}
export async function getTriggersNotificationData(
    trigger: TRIGGER_TYPE,
    cachedState: SiteState,
    data: Partial<TriggerJobData['data']>,
    idSite: number
) {
    const firstName =
        getFirstLongToken(cachedState.userName) ||
        getFirstLongToken(cachedState.full_name) ||
        cachedState.siteName
    switch (trigger) {
        case TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL:
        case TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL:
            return await getTrialingWeeklyMetrics(cachedState, idSite)
        case TRIGGER_TYPE.WEEKLY_SUMMARY_RECURRING:
            return await getPreviousWeekMetrics(cachedState, idSite)
        case TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY:
        case TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO:
            return {
                lastLoginDate: new Date(
                    cachedState.login?.lastLogin
                ).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                }),
                daysSinceLastLogin: cachedState.login?.daysSinceLastLogin,
            }
        case TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS:
            return {
                platform: cachedState.platform?.toLowerCase(),
            }
        case TRIGGER_TYPE.IP_BLOCKING_NOT_IMPLEMENTED:
        case TRIGGER_TYPE.NPS:
        case TRIGGER_TYPE.TEAM_MATES_NOT_INVITED:
            return {
                firstName: firstName,
            }
        default:
            return {}
    }
}
