import {
    ObservationGenerator,
    ObservationList,
} from '../../../services/observations/observer-evaluator'
import { logObservation } from '../../../services/observations/log-observation'

export async function loggingSiteObservations(
    idSite: number,
    observationResults: ObservationList[]
): Promise<void> {
    // Logger.dbLogger(siteState.idSite!.toString(), observationResults)
    if (observationResults.length === 0) return

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const yesterday = new Date(today)
    yesterday.setDate(today.getDate() - 1)

    const data = {
        idsite: idSite,
        observations: observationResults,
        dateFilterInput: [yesterday, today],
    }

    await logObservation(data)
}
