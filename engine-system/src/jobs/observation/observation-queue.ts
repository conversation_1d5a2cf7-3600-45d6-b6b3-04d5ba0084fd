import { Job, Queue, QueueEvents, Worker } from 'bullmq'
import { ObservationJobData, SystemObservationJobData } from './jobs'
import Redis from 'ioredis'
import RedisClient from '../../services/redis'

import Logger from '../../utils/logger'
import { EventListenerQueue } from '../event-listener-queue'
import { loggingSiteObservations } from './tasks/gen-observation'

const logger = Logger.getInstance()

interface RedisOptions {
    host: string
    port: number
}

export class ObservationQueue extends EventListenerQueue {
    private static instance: ObservationQueue | null = null
    private queue: Queue
    protected workers: Worker[]
    protected queueEvents: QueueEvents
    private readonly redisOptions: RedisOptions
    private isInitialized: boolean = false
    private NUM_WORKERS: number = 2
    private CONCURRENCY: number = 2
    private redis: Redis

    protected readonly queueName: string = 'observationQueue'

    constructor(redisOptions: RedisOptions) {
        super()
        this.redisOptions = redisOptions
        this.queue = new Queue(this.queueName, {
            connection: this.redisOptions,
            defaultJobOptions: {
                attempts: 3,
                backoff: { type: 'exponential', delay: 1000 },
            },
        })
        this.queueEvents = new QueueEvents(this.queueName, {
            connection: this.redisOptions,
        })
        this.workers = this.createWorkers(
            this.redisOptions,
            this.NUM_WORKERS,
            this.CONCURRENCY
        )
        this.initListeners()
        this.redis = RedisClient.getInstance()
        this.isInitialized = true
    }

    public static getInstance(redisOptions: RedisOptions) {
        if (!ObservationQueue.instance) {
            if (!redisOptions) {
                throw new Error(
                    '' + 'First call to getInstance must provide redis config'
                )
            }
            ObservationQueue.instance = new ObservationQueue(redisOptions)
        }
        return ObservationQueue.instance
    }
    private createWorkers(
        redisOptions: RedisOptions,
        workerCount: number,
        concurrency: number = 1
    ): Worker[] {
        return Array.from(
            { length: workerCount },
            () =>
                new Worker(this.queueName, this.processJob.bind(this), {
                    connection: redisOptions,
                    concurrency: concurrency,
                })
        )
    }

    private async processJob(job: Job<ObservationJobData>): Promise<void> {
        if (this.isSystemObservation(job)) {
            const { idSite, data } = job.data
            await loggingSiteObservations(idSite, data)
        }
    }

    public async addJob(data: ObservationJobData): Promise<void> {
        await this.queue.add('observation', data)
    }
    isSystemObservation(
        job: Job<ObservationJobData>
    ): job is Job<SystemObservationJobData> {
        return job.data.type === 'system'
    }

    public async start(): Promise<void> {
        await this.queue.waitUntilReady()
        for (const worker of this.workers) {
            await worker.waitUntilReady()
        }
        logger.info('ObservationQueue Started')
    }

    private async closeResources(): Promise<void> {
        if (!this.isInitialized) {
            logger.error('ObservationQueue not initialized, skipping shutdown')
            return
        }
        await Promise.all([
            this.workers.map((worker) => worker.close()),
            this.queue.close(),
            this.queueEvents.close(),
        ])
        this.isInitialized = false
        logger.info('ObservationQueue closed')
    }

    public static async shutdown(): Promise<void> {
        if (!ObservationQueue.instance) {
            logger.info('No ObservationQueue instance to shutdown')
            return
        }
        try {
            await ObservationQueue.instance.closeResources()
            ObservationQueue.instance = null
            logger.info('ObservationQueue closed')
        } catch (error) {
            logger.error('Failed to shutdown Trigger queue', error)
            throw error
        }
    }
    public getQueue() {
        return this.queue
    }
}
