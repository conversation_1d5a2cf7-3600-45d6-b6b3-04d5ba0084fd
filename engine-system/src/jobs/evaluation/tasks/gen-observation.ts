import {
    ObservationGenerator,
    ObservationList,
} from '../../../services/observations/observer-evaluator'
import { logObservation } from '../../../services/observations/log-observation'
import { SiteState } from '../../../types/site'
import Logger from '../../../utils/logger'

// export async function generateAndLogObservations(
//     siteState: SiteState
// ): Promise<void> {
//     const observer = new ObservationGenerator(siteState)
//     const observationResults = observer.generateObservations()
//
//     // Logger.dbLogger(siteState.idSite!.toString(), observationResults)
//     if (observationResults.length === 0) return
//
//     const today = new Date()
//     today.setHours(0, 0, 0, 0)
//
//     const yesterday = new Date(today)
//     yesterday.setDate(today.getDate() - 1)
//
//     const data = {
//         idsite: Number(siteState.idSite),
//         observations: observationResults,
//         dateFilterInput: [yesterday, today],
//     }
//
//     await logObservation(data)
// }
export function evaluateObservation(siteState: SiteState): ObservationList[] {
    const observer = new ObservationGenerator(siteState)
    return observer.generateObservations()

    // Logger.dbLogger(siteState.idSite!.toString(), observationResults)
}
