import { Queue, QueueEvents, Worker } from 'bullmq'
import { EvaluationJobData } from './jobs'
import Redis from 'ioredis'
import RedisClient from '../../services/redis'
import { SiteState } from '../../types/site'
import { fetchSiteState } from '../../utils/fakes/site-state'

import Logger from '../../utils/logger'
import { EventListenerQueue } from '../event-listener-queue'
import { executePayingTriggers } from './tasks/eval-paid-triggers'
import { executeTrialingTriggers } from './tasks/eval-trial-triggers'

const logger = Logger.getInstance()

interface RedisOptions {
    host: string
    port: number
}

export class EvaluationQueue extends EventListenerQueue {
    private static instance: EvaluationQueue | null = null
    private queue: Queue
    protected workers: Worker[]
    protected queueEvents: QueueEvents
    private readonly redisOptions: RedisOptions
    private isInitialized: boolean = false
    private NUM_WORKERS: number = 4
    private CONCURRENCY: number = 2
    private redis: Redis

    protected readonly queueName: string = 'evaluationQueue'

    constructor(redisOptions: RedisOptions) {
        super()
        this.redisOptions = redisOptions
        this.queue = new Queue(this.queueName, {
            connection: this.redisOptions,
            defaultJobOptions: {
                attempts: 3,
                backoff: { type: 'exponential', delay: 1000 },
            },
        })
        this.queueEvents = new QueueEvents(this.queueName, {
            connection: this.redisOptions,
        })
        this.workers = this.createWorkers(
            this.redisOptions,
            this.NUM_WORKERS,
            this.CONCURRENCY
        )
        this.initListeners()
        this.redis = RedisClient.getInstance()
        this.isInitialized = true
    }

    public static getInstance(redisOptions: RedisOptions) {
        if (!EvaluationQueue.instance) {
            if (!redisOptions) {
                throw new Error(
                    '' + 'First call to getInstance must provide redis config'
                )
            }
            EvaluationQueue.instance = new EvaluationQueue(redisOptions)
        }
        return EvaluationQueue.instance
    }
    private createWorkers(
        redisOptions: RedisOptions,
        workerCount: number,
        concurrency: number = 1
    ): Worker[] {
        return Array.from(
            { length: workerCount },
            () =>
                new Worker(this.queueName, this.processJob.bind(this), {
                    connection: redisOptions,
                    concurrency: concurrency,
                })
        )
    }

    private async processJob(job: { data: EvaluationJobData }): Promise<void> {
        const { idSite, eventType = 'trial', value, timestamp } = job.data
        if (eventType === 'trial') {
            await executeTrialingTriggers(+idSite, this.redis)
        } else if (eventType === 'paid') {
            await executePayingTriggers(+idSite, this.redis)
        } else {
            console.log('awch! nothing here')
        }
    }

    private async _fetchSiteState(idSite: number): Promise<SiteState> {
        return fetchSiteState(idSite)
    }
    public async addJob(
        data: EvaluationJobData,
        priority: number = 1
    ): Promise<void> {
        await this.queue.add('evaluation', data)
    }

    public async addJobsInBulk(
        list: EvaluationJobData[],
        priority: number = 1
    ): Promise<void> {
        const name = 'evaluation'
        const jobs = list.map((el) => {
            return {
                data: el,
                name,
            }
        })

        await this.queue.addBulk(jobs)
    }

    public async start(): Promise<void> {
        await this.queue.waitUntilReady()
        for (const worker of this.workers) {
            await worker.waitUntilReady()
        }
        logger.info('EvaluationQueue Started')
    }

    private async closeResources(): Promise<void> {
        if (!this.isInitialized) {
            logger.error('EvaluationQueue not initialized, skipping shutdown')
            return
        }
        await Promise.all([
            this.workers.map((worker) => worker.close()),
            this.queue.close(),
            this.queueEvents.close(),
        ])
        this.isInitialized = false
        logger.info('EvaluationQueue closed')
    }

    public static async shutdown(): Promise<void> {
        if (!EvaluationQueue.instance) {
            logger.info('No EvaluationQueue instance to shutdown')
            return
        }
        try {
            await EvaluationQueue.instance.closeResources()
            EvaluationQueue.instance = null
            logger.info('EvaluationQueue closed')
        } catch (error) {
            logger.error('Failed to shutdown Trigger queue', error)
            throw error
        }
    }
    public getQueue() {
        return this.queue
    }
}
