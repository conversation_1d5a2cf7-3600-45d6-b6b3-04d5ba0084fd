import { QueueE<PERSON>s, Worker } from 'bullmq'
import Logger from '../utils/logger'

const logger = Logger.getInstance()

export abstract class EventListenerQueue {
    protected abstract queueEvents: QueueEvents
    protected abstract workers: Worker[]
    protected abstract readonly queueName: string

    protected setupQueueEventListeners(): void {
        this.queueEvents.on('waiting', ({ jobId }) => {
            logger.info(`[${this.queueName}] Job ${jobId} is waiting`)
        })
        this.queueEvents.on('active', ({ jobId, prev }) => {
            logger.info(
                `[${this.queueName}] Job ${jobId} is now active; previous status was ${prev}`
            )
        })
        this.queueEvents.on('completed', ({ jobId, returnvalue }) => {
            logger.info(`[${this.queueName}] Job ${jobId} has completed`, {
                result: returnvalue,
            })
        })
        this.queueEvents.on('failed', ({ jobId, failedReason }) => {
            logger.error(`[${this.queueName}] Job ${jobId} has failed`, {
                reason: failedReason,
            })
        })
    }
    protected setupWorkerListeners(): void {
        this.workers.forEach((worker) => {
            worker.on('failed', (job, err) => {
                logger.error(`[${this.queueName}] Job ${job?.name} failed`, {
                    jobId: job?.id,
                    error: err.message,
                    stack: err.stack,
                })
            })
            worker.on('completed', (job) => {
                logger.info(
                    `[${this.queueName}] Job ${job.name} completed successfully`
                )
            })
            worker.on('error', (err) => {
                logger.error(`[${this.queueName}] Worker error`, { error: err })
            })
            worker.on('closed', () => {
                logger.warn(`[${this.queueName}] Worker ${worker.name} closed`)
            })
        })
    }

    protected initListeners(): void {
        this.setupQueueEventListeners()
        this.setupWorkerListeners()
    }
}
