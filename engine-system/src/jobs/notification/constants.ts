import { TemplateEngine } from '../../template-engine'
import { templateRegistry } from '../../template-registry'
import { envConfig } from '../../env.constant'

export const DEFAULT_FREQUENCY = 60 * 60 * 24 * 2

export const CHANNEL_EMAIL_ID = 1
export const CHANNEL_SLACK_ID = 2
export const CHANNEL_SMS_ID = 3

const _path =
    envConfig.buildTarget === 'prod'
        ? '/dist/new-templates'
        : '/src/new-templates'
const _engineSystem = new TemplateEngine(
    process.cwd() + _path,
    '',
    templateRegistry
)

;(async () => {
    await _engineSystem.init()
})()

export const engineSystem = _engineSystem
