import { Job } from 'bullmq'
import { NotificationJobData, SystemNotificationJobData } from '../jobs'
import { DEFAULT_FREQUENCY } from '../constants'
import { envConfig } from '../../../env.constant'
import Logger from '../../../utils/logger'
import { acquireNotificationLock } from './notification-locks'
import Redis from 'ioredis'
import { handleSystemNotification } from './handle-sys-notitifcations'
const logger = Logger.getInstance()

export async function processSystemNotificationJob(
    job: Job<SystemNotificationJobData>,
    redis: Redis
): Promise<void> {
    const { idSite, trigger, data, channel, type = 'system' } = job.data
    try {
        if (
            !(await acquireNotificationLock(
                idSite,
                trigger,
                DEFAULT_FREQUENCY,
                redis,
                channel
            ))
        ) {
            if (envConfig.loggerToggle) {
                Logger.dbLogger(
                    idSite.toString(),
                    `trigger:${trigger} recently processed for site : ${idSite}`
                )
            }
            logger.info(
                `Notification recently processed for trigger ${trigger} & idSite : ${idSite} `
            )
            return
        }

        if (envConfig.loggerToggle) {
            Logger.dbLogger(idSite.toString(), `trigger:${trigger} fired`)
        }
        await handleSystemNotification(
            idSite,
            trigger,
            data,
            channel,
            type,
            redis
        )
    } catch (error) {
        console.error(error)
        logger.error(
            `Failed to process notification for trigger : ${trigger}`,
            {
                error,
                idSite,
                channel,
                trigger,
            }
        )
        throw error
    }
}
