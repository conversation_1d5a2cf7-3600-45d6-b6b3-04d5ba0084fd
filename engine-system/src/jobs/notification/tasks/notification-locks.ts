import { TRIGGER_TYPE } from '../../../enums/triggers'
import { RedisKeys } from '../../../constants/redis-keys'
import Redis from 'ioredis'

export async function acquireNotificationLock(
    idSite: number,
    trigger: TRIGGER_TYPE,
    frequencySecs: number,
    redis: Redis,
    channel: string = 'email'
): Promise<boolean> {
    const lockKey = RedisKeys.SITE_TRIGGER_CHANNEL_KEY(
        idSite,
        trigger.toString(),
        channel
    )
    const result = await redis.set(lockKey, '1', 'EX', frequencySecs, 'NX')

    return result === 'OK'
}

async function isNotificationProcessed(
    trigger: TRIGGER_TYPE,
    idSite: number,
    redis: Redis,
    channel: string = 'email'
): Promise<boolean> {
    const key = RedisKeys.NOTIFICATIONS_SENT_CHANNEL_KEY(idSite, channel)
    return Boolean(await redis.sismember(key, trigger.toString()))
}
