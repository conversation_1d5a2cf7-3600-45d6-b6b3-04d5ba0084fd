import { Job } from 'bullmq'
import { RealTimeNotificationJobData } from '../jobs'
import Redis from 'ioredis'
import { handleNotification } from './handle-real-notifications'
import Logger from '../../../utils/logger'

const logger = Logger.getInstance()

export async function processRealTimeNotificationJob(
    job: Job<RealTimeNotificationJobData>,
    redis: Redis
): Promise<void> {
    const { data, type, channel } = job.data
    try {
        await handleNotification(data, channel, type, redis)
    } catch (error) {
        logger.error(error)
    }
}
