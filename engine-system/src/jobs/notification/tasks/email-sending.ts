import Redis from 'ioredis'
import Logger from '../../../utils/logger'
import { TemplateEngine } from '../../../template-engine'
import { NotificationJobData, SystemNotificationJobData } from '../jobs'
import { TRIGGER_TYPE } from '../../../enums/triggers'
import { RedisKeys } from '../../../constants/redis-keys'
import { envConfig } from '../../../env.constant'
import { getCachedTriggerId } from '../../../services/triggers'
import { getCachedChannelTypeId } from '../../../services/look-up/channel-types'
import { CHANNEL_EMAIL_ID } from '../constants'
import { createNotificationLog } from '../../../services/notification-logs'
import { NotificationService } from '../../../connectors/notification-service'
const logger = Logger.getInstance()

const notificationService = NotificationService.getInstance()
export async function handleSystemTriggerEmail(
    idSite: number,
    trigger: TRIGGER_TYPE,
    data: SystemNotificationJobData['data'],
    redis: Redis,
    engineSystem: TemplateEngine
) {
    logger.info(`Preparing Email Notification`, {
        idSite,
        trigger,
        email: data.email,
    })

    const body = engineSystem.render(trigger, 'email', data)
    const subject = engineSystem.registry[trigger].subject

    // const hashKey = `notifications:email:${notificationId}`
    const notificationKey = RedisKeys.NOTIFICATIONS_SENT_CHANNEL_KEY(
        idSite,
        'email'
    )
    // const hashData = {
    //     idSite: idSite,
    //     email: data.email,
    //     subject: subject,
    //     id: notificationId,
    //     body: body.toString(),
    //     timestamp: Date.now().toString(),
    // }
    const env = envConfig.nodeEnv === 'development' ? 'dev' : 'prod'
    const notificationListKey = RedisKeys.NOTIFICATION_EMAIL_LOG_KEY(env)
    const item = JSON.stringify({
        idSite,
        trigger,
        email: data.email,
        subject: subject,
        timestamp: Date.now().toString(),
    })

    if (envConfig.loggerToggle) {
        Logger.dbLogger(idSite.toString(), {
            idSite,
            email: data.email,
            subject,
            trigger,
            timestamp: Date.now().toString(),
        })
        Logger.dbLogger(idSite.toString(), body.toString(), '', true)
    }

    const [triggerObj, channelObj] = await Promise.all([
        getCachedTriggerId(trigger),
        getCachedChannelTypeId('email'),
    ])
    const triggerId = triggerObj !== null ? triggerObj.id : undefined
    const channelId = channelObj !== null ? channelObj.id : CHANNEL_EMAIL_ID

    const tasks: Promise<any>[] = [
        redis.lpush(notificationListKey, item),
        redis.sadd(notificationKey, trigger.toString()),
        redis.ltrim(notificationListKey, 0, 10000),
    ]
    if (envConfig.nodeEnv === 'production' && envConfig.emailOn) {
        tasks.push(
            notificationService.sendHtmlEmail(
                data.email,
                subject,
                body.toString()
            )
        )
        logger.info(
            `Sending email notification job enqueued for site: ${idSite}`
        )
    }
    await Promise.all(tasks)
    const logItem = JSON.stringify({
        idSite,
        channelId: channelId,
        triggerId: triggerId,
        status: 'sent',
        recipient: data.email,
        triggerValue: trigger.toString(),
        metadata: data,
        createdAt: new Date(),
        updatedAt: new Date(),
    })
    await redis.rpush(RedisKeys.PENDING_NOTIFICATIONS_LOG_KEY(), logItem)
    // await createNotificationLog({
    //     idSite: idSite,
    //     channelId: channelId,
    //     status: 'sent',
    //     recipient: data.email,
    //     triggerValue: trigger.toString(),
    //     triggerId: triggerId,
    //     metadata: data,
    // })
    logger.info(`Logged email notification in ${env} mode`, {
        idSite,
        trigger,
        email: data.email,
    })
}
