import { NotificationService } from '../../../connectors/notification-service'
import Logger from '../../../utils/logger'

const logger = Logger.getInstance()

const notificationService = NotificationService.getInstance()
export async function sendEmail(
    email: string,
    subject: string,
    body: string
): Promise<void> {
    try {
        await notificationService.sendHtmlEmail(email, subject, body)
        logger.info('email sent', { email, subject, body })
    } catch (e) {
        logger.error(e)
    }
}
