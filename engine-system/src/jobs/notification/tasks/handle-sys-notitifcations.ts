import { TRIGGER_TYPE } from '../../../enums/triggers'
import { NotificationJobData, SystemNotificationJobData } from '../jobs'
import Logger from '../../../utils/logger'
import { handleSystemTriggerEmail } from './email-sending'
import { TemplateEngine } from '../../../template-engine'
import Redis from 'ioredis'
import { engineSystem } from '../constants'

const logger = Logger.getInstance()

export async function handleSystemNotification(
    idSite: number,
    trigger: TRIGGER_TYPE,
    data: SystemNotificationJobData['data'],
    channel: SystemNotificationJobData['channel'],
    type: NotificationJobData['type'],
    redis: Redis
) {
    const handlers: Record<
        SystemNotificationJobData['channel'],
        () => Promise<void>
    > = {
        email: () =>
            handleSystemEmail(idSite, trigger, data, type, redis, engineSystem),
        message: () => handleSystemMessage(idSite, trigger, data, type),
        slack: () => handleSystemSlack(idSite, trigger, data, type),
    }

    const handler = handlers[channel]
    if (!handler) {
        logger.warn(`Unsupported channel :${channel}`, { idSite, trigger })
        return
    }
    await handler()
}
async function handleSystemMessage(
    idSite: number,
    trigger: TRIGGER_TYPE,
    data: SystemNotificationJobData['data'],
    type: NotificationJobData['type']
) {
    logger.info(`Preparing Message Notification`, {
        idSite,
        trigger,
        email: data.email,
    })
}

async function handleSystemSlack(
    idSite: number,
    trigger: TRIGGER_TYPE,
    data: SystemNotificationJobData['data'],
    type: NotificationJobData['type']
) {
    logger.info(`Preparing Slack Notification`, {
        idSite,
        trigger,
        email: data.email,
    })
}
async function handleSystemEmail(
    idSite: number,
    trigger: TRIGGER_TYPE,
    data: SystemNotificationJobData['data'],
    type: NotificationJobData['type'],
    redis: Redis,
    engineSystem: TemplateEngine
) {
    logger.info(`Preparing Email Notification`, {
        idSite,
        trigger,
        email: data.email,
    })
    if (type === 'system') {
        await handleSystemTriggerEmail(
            idSite,
            trigger,
            data,
            redis,
            engineSystem
        )
    }
}
