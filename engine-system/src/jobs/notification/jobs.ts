import { TRIGGER_TYPE } from '../../enums/triggers'

export interface SystemNotificationJobData {
    type: 'system'
    idSite: number
    channel: string
    data: Record<string, any>
    trigger: TRIGGER_TYPE
    priority?: number
}
export interface RealTimeNotificationJobData {
    type: 'real-time'
    channel: string
    data: Record<string, any>
    priority?: number

    // body: string
    // subject: string
    // email: string
    // idSite?: number
}

export type NotificationJobData =
    | SystemNotificationJobData
    | RealTimeNotificationJobData

// export interface NotificationJobData {
//     idSite: number
//     channel: string
//     data: Record<string, any>
//     trigger: TRIGGER_TYPE
//     type: 'system' | 'custom'
// }
