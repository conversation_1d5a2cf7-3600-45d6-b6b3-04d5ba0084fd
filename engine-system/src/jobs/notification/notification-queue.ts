import { Job, Queue, QueueEvents, Worker } from 'bullmq'
import {
    NotificationJobData,
    RealTimeNotificationJobData,
    SystemNotificationJobData,
} from './jobs'

import Logger from '../../utils/logger'
import { EventListenerQueue } from '../event-listener-queue'
import Redis from 'ioredis'
import RedisClient from '../../services/redis'
const logger = Logger.getInstance()
import { processSystemNotificationJob } from './tasks/process-system-trigger-notifications'
import { processRealTimeNotificationJob } from './tasks/process-real-time-notifications'
// const engineSystem = new TemplateEngine(
//     process.cwd() + '/src/new-templates',
//     '',
//     templateRegistry
// )
//
// ;(async () => {
//     await engineSystem.init()
// })()

interface RedisOptions {
    host: string
    port: number
}

export class NotificationQueue extends EventListenerQueue {
    private static instance: NotificationQueue | null = null
    private queue: Queue
    protected workers: Worker[]
    protected queueEvents: QueueEvents
    private readonly redisOptions: RedisOptions
    private isInitialized: boolean = false
    private NUM_WORKERS: number = 4
    private CONCURRENCY: number = 2
    protected readonly queueName = 'notificationQueue'
    private redis: Redis

    constructor(redisOptions: RedisOptions) {
        super()
        this.redisOptions = redisOptions
        this.queue = new Queue(this.queueName, {
            connection: this.redisOptions,
            defaultJobOptions: {
                attempts: 3,
                backoff: { type: 'exponential', delay: 1000 },
            },
        })
        this.queueEvents = new QueueEvents(this.queueName, {
            connection: this.redisOptions,
        })
        this.workers = this.createChildWorkers(
            this.redisOptions,
            this.NUM_WORKERS,
            this.CONCURRENCY
        )
        this.initListeners()
        this.isInitialized = true
        this.redis = RedisClient.getInstance()
    }

    public static getInstance(redisOptions: RedisOptions) {
        if (!NotificationQueue.instance) {
            if (!redisOptions) {
                throw new Error(
                    '' + 'First call to getInstance must provide redis config'
                )
            }
            NotificationQueue.instance = new NotificationQueue(redisOptions)
        }
        return NotificationQueue.instance
    }
    private createChildWorkers(
        redisOptions: RedisOptions,
        workerCount: number,
        concurrency: number = 1
    ): Worker[] {
        return Array.from(
            { length: workerCount },
            () =>
                new Worker(this.queueName, this.processJob.bind(this), {
                    connection: redisOptions,
                    concurrency: concurrency,
                })
        )
    }

    private async processJob(job: Job<NotificationJobData>): Promise<void> {
        if (this.isSystemNotification(job)) {
            await processSystemNotificationJob(job, this.redis)
            return
        } else if (this.isRealTimeNotification(job)) {
            await processRealTimeNotificationJob(job, this.redis)
            return
        } else {
            console.log('awch not supported')
        }
    }
    isSystemNotification(
        job: Job<NotificationJobData>
    ): job is Job<SystemNotificationJobData> {
        return job.data.type === 'system'
    }
    isRealTimeNotification(
        job: Job<NotificationJobData>
    ): job is Job<RealTimeNotificationJobData> {
        return job.data.type === 'real-time'
    }
    public async addJob(
        data: NotificationJobData,
        priority: number = 1
    ): Promise<void> {
        await this.queue.add('notify', data, { priority })
    }

    public async addJobsInBulk(
        list: NotificationJobData[],
        priority: number = 1
    ): Promise<void> {
        const name = 'notify'
        const jobs = list.map((el) => {
            return {
                data: el,
                name,
            }
        })

        await this.queue.addBulk(jobs)
    }

    public async start(): Promise<void> {
        await this.queue.waitUntilReady()
        for (const worker of this.workers) {
            await worker.waitUntilReady()
        }
        logger.info('Notification Queue started')
    }

    private async closeResources(): Promise<void> {
        if (!this.isInitialized) {
            logger.error('NotificationQueue not initialized, skipping shutdown')
            return
        }
        await Promise.all([
            this.workers.map((worker) => worker.close()),
            this.queue.close(),
            this.queueEvents.close(),
        ])
        this.isInitialized = false
        logger.info('NotificationQueue closed')
    }

    public static async shutdown(): Promise<void> {
        if (!NotificationQueue.instance) {
            logger.info('No Notification Queue instance to shutdown')
            return
        }
        try {
            await NotificationQueue.instance.closeResources()
            NotificationQueue.instance = null
            logger.info('NotificationQueue closed')
        } catch (error) {
            logger.error('Failed to shutdown Notification queue', error)
            throw error
        }
    }
    public getQueue() {
        return this.queue
    }
}
