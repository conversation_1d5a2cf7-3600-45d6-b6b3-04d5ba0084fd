import { SiteApi } from '../../../integrations/site-apis'
import { TRIGGER_TYPE } from '../../../enums/triggers'
import { publishToTriggersExchangeV1 } from '../../../messaging/producers'
import Redis from 'ioredis'

import Logger from '../../../utils/logger'
import { QueueService } from '../../../messaging/queue-service'
import { RedisKeys } from '../../../constants/redis-keys'

const queueService = QueueService.getInstance()
const logger = Logger.getInstance()

export async function executeWeeklyMetricsPush(
    jobId: string,
    redis: Redis
): Promise<void> {
    try {
        logger.info('Running Weekly Metrics Push')
        await queueService.initialize()

        const date = new Date().toISOString().split('T')[0]

        // const redisKey = `bot:active:weekly:${date}:${jobId}`
        const redisKey = RedisKeys.BOT_JOB_KEY('active', 'weekly', date, jobId)
        const ttlSeconds = 60 * 60 * 24

        const tomorrow = new Date(date)
        tomorrow.setUTCDate(tomorrow.getUTCDate() + 1)
        tomorrow.setUTCHours(0, 0, 0, 0)
        const expireAtUnix = Math.floor(tomorrow.getTime() / 1000)

        const siteApi = new SiteApi()
        const activeSites = await siteApi.fetchAllPayingSites()
        logger.info(`Fetched ${activeSites.length} active sites`)

        for (const site of activeSites) {
            const { idSite } = site
            const isProcessed = await redis.sismember(
                redisKey,
                idSite.toString()
            )

            if (!isProcessed) {
                const data = {
                    idSite: site.idSite,
                    data: { email: site.email },
                    trigger: TRIGGER_TYPE.WEEKLY_SUMMARY_RECURRING,
                    category: 'summaries',
                }
                await Promise.all([
                    publishToTriggersExchangeV1(data),
                    redis.sadd(redisKey, idSite.toString()),
                ])
                logger.debug(`Published site ${idSite} to triggers exchange`)
            } else {
                logger.debug(
                    `Skipping site ${idSite} ; already published to triggers exchange`
                )
            }
        }
        // await this.redis.expire(redisKey, ttlSeconds)
        await redis.expireat(redisKey, expireAtUnix)
        logger.info(
            `Processed ${activeSites.length} active sites, stored in ${redisKey}`
        )
    } catch (error) {
        logger.error(
            `Weekly bot task failed: ${error instanceof Error ? error.message : error}`,
            error
        )
        throw error
    }
}
