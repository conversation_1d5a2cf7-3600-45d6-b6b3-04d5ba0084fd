import Redis from 'ioredis'
import { RedisKeys } from '../../../constants/redis-keys'
import Logger from '../../../utils/logger'
import prisma from '../../../db'
import { createManyNotificationLogs } from '../../../services/notification-logs'
import { NotificationStatus } from '../../../generated/prisma'

const logger = Logger.getInstance()

const BATCH_SIZE = 512
export async function handleNotificationBulkInsert(redis: Redis) {
    const items = await redis.lrange(
        RedisKeys.PENDING_NOTIFICATIONS_LOG_KEY(),
        0,
        BATCH_SIZE - 1
    )

    if (items.length === 0) {
        logger.info(`No Logs to process`)
        return
    }
    await redis.ltrim(
        RedisKeys.PENDING_NOTIFICATIONS_LOG_KEY(),
        items.length,
        -1
    )

    const parsedLogs = transformLogs(items)
    const r = await createManyNotificationLogs(parsedLogs)
    logger.info(`${r.count} notifications logs inserted`)
}

type RawLog = {
    idSite: number
    channelId: number
    status: string
    recipient: string
    triggerValue: string
    triggerId?: number
    metadata?: any
    createdAt?: string
    updatedAt?: string
}

type PrismaLog = {
    idSite: number
    channelId: number
    status: NotificationStatus
    recipient: string
    triggerValue: string
    triggerId?: number
    metadata?: any
    createdAt?: Date
    updatedAt?: Date
}

export function transformLogs(logStrings: string[]): PrismaLog[] {
    return logStrings.map((logStr) => {
        const log: RawLog = JSON.parse(logStr)
        const dateCreated = log?.createdAt ? new Date(log.createdAt) : undefined
        const dateUpdated = log?.updatedAt ? new Date(log.updatedAt) : undefined
        return {
            idSite: log.idSite,
            channelId: log.channelId,
            status: log.status as NotificationStatus,
            recipient: log.recipient,
            triggerValue: log.triggerValue,
            triggerId: log.triggerId,
            metadata: log.metadata,
            createdAt: dateCreated,
            updatedAt: dateUpdated,
        }
    })
}
