import Logger from '../../../utils/logger'
import { QueueService } from '../../../messaging/queue-service'
import Redis from 'ioredis'
import { SiteApi } from '../../../integrations/site-apis'

const logger = Logger.getInstance()
const queueService = QueueService.getInstance()
import { RedisKeys } from '../../../constants/redis-keys'

export async function executeDailyActiveBot(
    jobId: string,
    redis: Redis
): Promise<void> {
    try {
        logger.info('Running daily bot task for active users')
        await queueService.initialize()

        const date = new Date().toISOString().split('T')[0]

        // const redisKey = `bot:active:daily:${date}:${jobId}`
        const redisKey = RedisKeys.BOT_JOB_KEY('active', 'daily', date, jobId)
        const ttlSeconds = 60 * 60 * 24

        const tomorrow = new Date(date)
        tomorrow.setUTCDate(tomorrow.getUTCDate() + 1)
        tomorrow.setUTCHours(0, 0, 0, 0)
        const expireAtUnix = Math.floor(tomorrow.getTime() / 1000)

        const exchange = 'site_evaluation'
        const routingKey = 'site_evaluation.all'

        const siteApi = new SiteApi()
        const activeSites = await siteApi.fetchAllPayingSites()
        logger.info(`Fetched ${activeSites.length} active sites`)

        for (const site of activeSites) {
            const { idSite } = site
            const isProcessed = await redis.sismember(
                redisKey,
                idSite.toString()
            )
            const enhancedSite = {
                ...site,
                eventType: 'paid',
            }

            if (!isProcessed) {
                await Promise.all([
                    queueService.publishToExchange(
                        exchange,
                        routingKey,
                        Buffer.from(JSON.stringify(enhancedSite)),
                        'direct'
                    ),
                    redis.sadd(redisKey, idSite.toString()),
                ])
                logger.debug(`Published site ${idSite} to ${exchange}`)
            } else {
                logger.debug(
                    `Skipping site ${idSite} ; already published ${exchange}`
                )
            }
        }
        // await this.redis.expire(redisKey, ttlSeconds)
        await redis.expireat(redisKey, expireAtUnix)
        logger.info(
            `Processed ${activeSites.length} active sites, stored in ${redisKey}`
        )
    } catch (error) {
        logger.error(
            `Daily bot task failed: ${error instanceof Error ? error.message : error}`,
            error
        )
        throw error
    }
}
