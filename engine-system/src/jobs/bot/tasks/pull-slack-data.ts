// import axios from 'axios';
// import { envConfig } from '../../../env.constant';
// import Logger from '../../../utils/logger';
// import { TokenEncryptor } from '../../../utils/security/token-encyptor';
// import prisma from '../../../db';
// import { SQSClient, ReceiveMessageCommand, DeleteMessageCommand } from '@aws-sdk/client-sqs';
//
// const logger = Logger.getInstance();
// const tokenEncryptor = new TokenEncryptor(envConfig.encryptionKey);
// const sqs = new SQSClient({ region: envConfig.awsRegion });
// const QUEUE_URL = envConfig.sqsQueueUrl;
//
// export async function pullDataFromQueue() {
//     const command = new ReceiveMessageCommand({
//         QueueUrl: QUEUE_URL,
//         MaxNumberOfMessages: 10,
//         WaitTimeSeconds: 10,
//         VisibilityTimeout: 30
//     });
//
//     const response = await sqs.send(command);
//
//     for (const message of response.Messages || []) {
//         if(message.Body){
//             const {state, code} = JSON.parse(message.Body);
//
//             if (!code) {
//                 logger.error('Missing authorization code');
//                 continue;
//             }
//
//             try {
//                 const axiosResponse = await axios.post(
//                     'https://slack.com/api/oauth.v2.access',
//                     null,
//                     {
//                         params: {
//                             client_id: envConfig.slack.clientId,
//                             client_secret: envConfig.slack.clientSecret,
//                             code: code,
//                             redirect_uri: `${envConfig.slack.redirectBaseUrl}/slack/oauth/callback`,
//                         },
//                     }
//                 )
//
//                 const slackData = axiosResponse.data;
//
//                 if (!slackData.ok) {
//                     logger.error(`OAuth Error: ${slackData.error}`);
//                     continue;
//                 }
//
//                 let customParams: Record<string, any> = {};
//                 if (state) {
//                     try {
//                         customParams = JSON.parse(Buffer.from(state, 'base64').toString());
//                     } catch (e) {
//                         console.warn('Could not parse state parameter:', e);
//                         continue;
//                     }
//                 }
//
//                 const { access_token, incoming_webhook } = slackData;
//
//                 if (incoming_webhook) {
//                     const { channel, channel_id } = incoming_webhook;
//                     const { idsite, channel_type_id } = customParams;
//
//                     try {
//                         // Save token to site tokens table
//                         const encryptedTokenPayload = tokenEncryptor.encrypt(access_token);
//                         const { iv, tag, encryptedData } = encryptedTokenPayload;
//                         await prisma.siteToken.create({
//                             data: {
//                                 iv,
//                                 tag,
//                                 accessTokenEncrypted: encryptedData,
//                                 idSite: Number(idsite),
//                                 channelId: Number(channel_type_id),
//                             },
//                         });
//
//                         // Save to site notification preferences table
//                         await prisma.siteNotificationPreference.create({
//                             data: {
//                                 idSite: Number(idsite),
//                                 channelId: Number(channel_type_id),
//                                 isEnabled: true,
//                                 destination: {
//                                     channel_id,
//                                     channelName: channel,
//                                     default: true
//                                 },
//                             },
//                         });
//                     } catch (err) {
//                         logger.error(
//                             `Error saving slack details to site notification preferences table: ${err}`
//                         );
//                     }
//                 }
//             } catch (error) {
//                 logger.error('Error during OAuth:', error);
//             }
//
//             // Delete message after processing
//             await sqs.send(new DeleteMessageCommand({
//               QueueUrl: QUEUE_URL,
//               ReceiptHandle: message.ReceiptHandle!
//             }));
//         }
//     }
//
// }
