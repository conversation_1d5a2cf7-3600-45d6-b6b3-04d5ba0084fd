import { Queue, QueueE<PERSON>s, Worker, Job } from 'bullmq'
import Redis from 'ioredis'
import RedisClient from '../../services/redis'
import Logger from '../../utils/logger'
import { BotJobData } from './jobs'
import { EventListenerQueue } from '../event-listener-queue'
import { executeDailyTrialingBot } from './tasks/pull-trialing-users'
import { executeWeeklyMetricsPush } from './tasks/push-weekly-metrics'
import { executeDailyActiveBot } from './tasks/pull-active-users'

const logger = Logger.getInstance()
import {
    TRIAL_DAILY_CRON_SCHEDULE,
    WEEKLY_METRICS_PUSH_CRON,
    PAID_DAILY_CRON_SCHEDULE,
    BULK_NOTIFICATION_INSERT_RUN_SCHEDULE,
} from './constants'
import { handleNotificationBulkInsert } from './tasks/process-notification-logs'

interface RedisOptions {
    host: string
    port: number
}

export class BotQ<PERSON>ue extends EventListenerQueue {
    private static instance: BotQueue | null = null
    private queue: Queue
    protected workers: Worker[]
    protected queueEvents: QueueEvents
    private readonly redisOptions: RedisOptions
    private isInitialized: boolean = false
    private NUM_WORKERS: number = 4
    private redis: Redis

    protected readonly queueName: string = 'bot'

    constructor(redisOptions: RedisOptions) {
        super()
        this.redisOptions = redisOptions
        this.queue = new Queue(this.queueName, {
            connection: this.redisOptions,
            defaultJobOptions: {
                attempts: 3,
                backoff: { type: 'exponential', delay: 1000 },
            },
        })
        this.queueEvents = new QueueEvents(this.queueName, {
            connection: this.redisOptions,
        })
        this.workers = this.createWorkers(this.redisOptions, this.NUM_WORKERS)
        this.initListeners()
        this.redis = RedisClient.getInstance()
        this.isInitialized = true
    }

    public static getInstance(redisOptions: RedisOptions) {
        if (!BotQueue.instance) {
            if (!redisOptions) {
                throw new Error(
                    '' + 'First call to getInstance must provide redis config'
                )
            }
            BotQueue.instance = new BotQueue(redisOptions)
        }
        return BotQueue.instance
    }
    private createWorkers(
        redisOptions: RedisOptions,
        workerCount: number,
        concurrency: number = 1
    ): Worker[] {
        return Array.from(
            { length: workerCount },
            () =>
                new Worker(this.queueName, this.processJob.bind(this), {
                    connection: redisOptions,
                    concurrency: concurrency,
                })
        )
    }

    private async closeResources(): Promise<void> {
        if (!this.isInitialized) {
            logger.error('BotQueue not initialized, skipping shutdown')
            return
        }
        await Promise.all([
            this.workers.map((worker) => worker.close()),
            this.queue.close(),
            this.queueEvents.close(),
        ])
        this.isInitialized = false
        logger.info('BotQueue closed')
    }
    private async processJob(job: Job<BotJobData>): Promise<void> {
        const { type, data } = job.data

        if (type === 'trial') {
            await this.runTrialDailyBot(job)
        } else if (type === 'paid') {
            await this.runActiveDailyBot(job)
        } else if (type === 'weekly-metrics') {
            await this.runWeeklyMetricsPush(job)
        } else if (type === 'bulk-notifications') {
            await this.runBulkNotificationJob(job)
        } else {
            console.log('chai')
        }
    }

    private async runTrialDailyBot(job: Job) {
        await executeDailyTrialingBot(job.id!, this.redis)
    }

    private async runBulkNotificationJob(job: Job<BotJobData>): Promise<void> {
        await handleNotificationBulkInsert(this.redis)
    }

    private async runActiveDailyBot(job: Job<BotJobData>) {
        await executeDailyActiveBot(job.id!, this.redis)
    }

    private async runWeeklyMetricsPush(job: Job) {
        await executeWeeklyMetricsPush(job.id!, this.redis)
    }
    private async scheduleTrialSitesRun(cronSchedule: string): Promise<void> {
        await this.queue.upsertJobScheduler(
            'trial-daily-run',
            {
                pattern: cronSchedule,
            },
            {
                data: {
                    type: 'trial',
                },
                opts: {
                    attempts: 5,
                },
            }
        )
        logger.info(`Trialing sites daily-run scheduled at ${cronSchedule}`)
    }
    private async scheduleActiveSitesRun(cronSchedule: string): Promise<void> {
        await this.queue.upsertJobScheduler(
            'active-daily-run',
            {
                pattern: cronSchedule,
            },
            {
                data: {
                    type: 'paid',
                },
                opts: {
                    attempts: 5,
                },
            }
        )
        logger.info(`Active sites daily-run scheduled at ${cronSchedule}`)
    }
    private async scheduleWeeklySitesMetricsRun(
        cronSchedule: string
    ): Promise<void> {
        await this.queue.upsertJobScheduler(
            'weekly-metrics',
            {
                pattern: cronSchedule,
            },
            {
                data: {
                    type: 'weekly-metrics',
                },
                opts: {
                    attempts: 10,
                },
            }
        )
        logger.info(`Weekly metrics metrics run scheduled at ${cronSchedule}`)
    }

    private async scheduleBulkNotificationInsertions(
        cronSchedule: string
    ): Promise<void> {
        await this.queue.upsertJobScheduler(
            'bulk-notifications',
            {
                pattern: cronSchedule,
            },
            {
                data: {
                    type: 'bulk-notifications',
                },
            }
        )
        logger.info(`Bulk Insertions happening every ${cronSchedule}`)
    }

    async start(): Promise<void> {
        await this.queue.waitUntilReady()
        for (const worker of this.workers) {
            await worker.waitUntilReady()
        }
        logger.info('BotQueue Started')
        await this.scheduleTrialSitesRun(TRIAL_DAILY_CRON_SCHEDULE)
        await this.scheduleActiveSitesRun(PAID_DAILY_CRON_SCHEDULE)
        await this.scheduleWeeklySitesMetricsRun(WEEKLY_METRICS_PUSH_CRON)
        await this.scheduleBulkNotificationInsertions(
            BULK_NOTIFICATION_INSERT_RUN_SCHEDULE
        )
    }

    public static async shutdown(): Promise<void> {
        if (!BotQueue.instance) {
            logger.info('No BotQueue instance to shutdown')
            return
        }
        try {
            await BotQueue.instance.closeResources()
            BotQueue.instance = null
            logger.info('BotQueue closed')
        } catch (error) {
            logger.error('Failed to shutdown Trigger queue', error)
            throw error
        }
    }
    public getQueue() {
        return this.queue
    }
}
