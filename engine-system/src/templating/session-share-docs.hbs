{{#extend 'email-template'}}
    {{#content 'title'}}Fix Your Session Tracking{{/content}}

    {{#content 'body'}}
        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Hey,
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Great job installing the heatmap snippet!
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            However, we noticed your session data isn't tracking yet, which
            limits your visibility into customer interactions.
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Don’t worry—this is usually an easy fix. Check out our detailed
            troubleshooting guide below and you'll be tracking sessions
            accurately in no time.
        </p>

        <p>
            <a
                href='https://intercom.help/heatmapcom/en/articles/10022307-troubleshooting-dots-visible-but-no-revenue-data'
                style='background-color: #004225; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; display: inline-block; padding: 12px 25px; border-radius: 4px; font-family: Arial, sans-serif; border: 2px solid transparent; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'
                target='_blank'
                rel='noopener noreferrer'
                onmouseover="this.style.backgroundColor='#005d42'; this.style.borderColor='#004225'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.backgroundColor='#004225'; this.style.borderColor='transparent'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
            >
                Fix Session Tracking
            </a>
        </p>
    {{/content}}
{{/extend}}