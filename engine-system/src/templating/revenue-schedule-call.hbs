{{#extend 'email-template'}}
    {{#content 'title'}}Let’s Get Your Revenue Tracking Fixed{{/content}}

    {{#content 'body'}}
        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Hello!
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Still not seeing your revenue data tracking properly?
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            This information is key to maximizing your site's profitability.
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Schedule a quick call with our team—we’ll personally walk you
            through the solution, ensuring everything is properly set up.
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Let’s get you fully operational and capturing insights now\!
        </p>

        <p>
            <a
                href='https://heatmap.com'
                style='background-color: #004225; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; display: inline-block; padding: 12px 25px; border-radius: 4px; font-family: Arial, sans-serif; border: 2px solid transparent; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'
                target='_blank'
                rel='noopener noreferrer'
                onmouseover="this.style.backgroundColor='#005d42'; this.style.borderColor='#004225'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.backgroundColor='#004225'; this.style.borderColor='transparent'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
            >
                Schedule Revenue Tracking Call
            </a>

        </p>
    {{/content}}
{{/extend}}