{{#extend 'email-template'}}
    {{#content 'title'}}Revenue Data Missing? Let’s Fix It{{/content}}

    {{#content 'body'}}
        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Hi,
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Your snippet is working, but we’re not seeing revenue data yet.
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            To start capturing crucial revenue insights, follow our
            straightforward troubleshooting guide.
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Quickly resolve this and start identifying exactly which elements
            are driving revenue on your site.
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Get your full revenue tracking working today!
        </p>

        <p>
            <a
                href='https://intercom.help/heatmapcom/en/articles/10022307-troubleshooting-dots-visible-but-no-revenue-data'
                style='background-color: #004225; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; display: inline-block; padding: 12px 25px; border-radius: 4px; font-family: Arial, sans-serif; border: 2px solid transparent; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'
                target='_blank'
                rel='noopener noreferrer'
                onmouseover="this.style.backgroundColor='#005d42'; this.style.borderColor='#004225'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.backgroundColor='#004225'; this.style.borderColor='transparent'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
            >
                Resolve Revenue Tracking
            </a>

        </p>
    {{/content}}
{{/extend}}