{{#extend 'email-template'}}
    {{#content 'title'}} {{title}} {{/content}}

    {{#content 'body'}}
        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Hi
            {{firstName}},
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            It’s been a few days since your last login ({{lastLoginDate}}), and
            we miss you!
        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;'
        >
            Did you know top eCommerce brands are using Heatmap to boost their
            revenue by over 15%? See exactly how they did it — and how you can
            do it too.
        </p>

        <p>
            <a
                href='https://www.heatmap.com/case-studies/how-that-works-used-heatmap-to-achieve-a-13-increase-in-revenue-per-session-for-a-growing-uk-fashion-brand'
                style='background-color: #004225; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: 500; display: inline-block; padding: 12px 25px; border-radius: 4px; font-family: Arial, sans-serif; border: 2px solid transparent; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);'
                target='_blank'
                rel='noopener noreferrer'
                onmouseover="this.style.backgroundColor='#005d42'; this.style.borderColor='#004225'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.1)';"
                onmouseout="this.style.backgroundColor='#004225'; this.style.borderColor='transparent'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
            >
                Read the Case Study
            </a>

        </p>

        <p
            style='color: #333333; font-size: 16px; line-height: 1.6; margin: 20px 0 0 0;'
        >
            Let’s get your trial back on track!
        </p>
    {{/content}}
{{/extend}}