import { EvaluationQueue } from './jobs/evaluation/evaluation-queue'
import { QueueService } from './messaging/queue-service'
import { TriggerQueue } from './jobs/trigger/trigger-queue'
import { NotificationQueue } from './jobs/notification/notification-queue'
import { envConfig } from './env.constant'
import RedisClient from './services/redis'
import { startSiteEvaluationConsumer } from './messaging/consumers/site-evaluation'
// import { startRealTimeConsumer } from './messaging/consumers/real-time'
import { startTriggerConsumer } from './messaging/consumers/trigger'
import { startNotificationConsumer } from './messaging/consumers/notification'
import Logger from './utils/logger'
import app from './app'
import { Server } from 'http'
import { BotQueue } from './jobs/bot/bot-queue'
import { ObservationQueue } from './jobs/observation/observation-queue'

let isShuttingDown = false
let server: Server | null = null

const logger = Logger.getInstance()

const queueService = QueueService.getInstance()

async function startQueuesAndConsumers(): Promise<void> {
    await queueService.initialize()

    await RedisClient.waitUntilReady()
    await RedisClient.checkConnection()

    const evaluationQueue = EvaluationQueue.getInstance({
        host: envConfig.redis.host,
        port: envConfig.redis.port,
    })
    const triggerQueue = TriggerQueue.getInstance({
        host: envConfig.redis.host,
        port: envConfig.redis.port,
    })
    const observationQueue = ObservationQueue.getInstance({
        host: envConfig.redis.host,
        port: envConfig.redis.port,
    })
    const notificationQueue = NotificationQueue.getInstance({
        host: envConfig.redis.host,
        port: envConfig.redis.port,
    })
    const botQueue = BotQueue.getInstance({
        host: envConfig.redis.host,
        port: envConfig.redis.port,
    })

    await evaluationQueue.start()
    await observationQueue.start()

    await triggerQueue.start()
    await notificationQueue.start()

    await startSiteEvaluationConsumer(evaluationQueue)
    // await startRealTimeConsumer(evaluationQueue)
    await startTriggerConsumer(triggerQueue)
    await startNotificationConsumer(notificationQueue)

    await botQueue.start()

    logger.info('Queues and consumers initialized')
}
async function shutdown(exitCode: number = 1): Promise<void> {
    if (server) {
        await new Promise<void>((resolve, reject) => {
            server?.close((err) => {
                if (err) {
                    logger.error(`Error while shutdown: ${err}`)
                    // reject(err);
                } else {
                    logger.info('express server closed')
                    // resolve();
                }
                resolve()
            })
        })
        server = null
    }
    await BotQueue.shutdown()
    await EvaluationQueue.shutdown()
    await ObservationQueue.shutdown()
    await TriggerQueue.shutdown()
    await NotificationQueue.shutdown()
    await queueService.close()
    await RedisClient.close()
    await Logger.closeLogger()

    logger.info('shutdown complete')
    process.exit(exitCode)
}

async function main() {
    try {
        const port = envConfig.port

        server = app.listen(port, () => {
            logger.info('Listening on port', port)
        })
        await startQueuesAndConsumers()

        await new Promise<void>((resolve, reject) => {
            console.error('called in here')
            server?.on('close', resolve)
            server?.on('error', reject)
        })
    } catch (err) {
        console.error(err)
        logger.error(
            `Startup error: ${err instanceof Error ? err.message : err}`
        )
        // if (!isShuttingDown) {
        //     isShuttingDown = true;
        //     await shutdown(1)
        // }
    }
}
async function handleSignal(signal: NodeJS.Signals) {
    if (isShuttingDown) return
    isShuttingDown = true
    logger.info(`Received signal: ${signal}, initiating shutdown...`)
    await shutdown(0)
}

process.on('SIGINT', handleSignal)
process.on('SIGTERM', handleSignal)

main()
