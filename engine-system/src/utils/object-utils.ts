export function stringifyNestedObjects<T extends Record<string, any>>(
    obj: T
): Record<string, string | any> {
    const result: Record<string, any> = { ...obj }

    for (const [key, value] of Object.entries(obj as any)) {
        if (
            value &&
            typeof value === 'object' &&
            !Array.isArray(value) &&
            Object.keys(value).length > 0
        ) {
            // Handle nested objects (but not empty objects)
            result[key] = JSON.stringify(value)
        } else if (Array.isArray(value)) {
            // Handle arrays (e.g., onboardingSteps)
            result[key] = JSON.stringify(value)
        }
        // Primitive values (string, number, boolean, null) are left as-is
    }
    return result
}

export function parseNestedObjects<T>(
    obj: Record<string, string | any>
): Partial<T> {
    const result: Record<string, any> = { ...obj }
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string') {
            try {
                // Attempt to parse JSO<PERSON>; if it’s a valid object or array, replace the string
                const parsed = JSON.parse(value)
                if (typeof parsed === 'object' && parsed !== null) {
                    result[key] = parsed
                }
            } catch (e) {
                // If parsing fails, leave the value as a string (e.g., trialStartDate)
            }
        }
    }
    return result as Partial<T>
}
