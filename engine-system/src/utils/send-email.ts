import axios from 'axios'
import { BadRequestError } from '../api/utils/custom.errors'

export async function sendEmailUtil(
    emailTo: string,
    subject: string,
    body: string,
    isHtml: boolean = false
) {
    try {
        const payload = {
            subject: subject,
            email: emailTo,
            body: body,
            ...(isHtml && { type: 'raw' }),
        }

        const response = await axios.post(
            'https://stage10.heatmapcore.com/backend/emails/send',
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        )

        const result = {
            success: true,
            message: 'Email sent successfully',
            result: response.data,
        }
        console.log(result)
        return result
    } catch (err) {
        console.error(err)
        if (err instanceof Error) {
            throw new BadRequestError(err.message)
        }

        console.error(err)
        throw new BadRequestError(
            'An unknown error occured while sending email'
        )
    }
}
