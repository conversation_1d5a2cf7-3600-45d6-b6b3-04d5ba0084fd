import pino from 'pino'
import path from 'node:path'
import fs from 'fs'
import { envConfig } from '../env.constant'

class Logger {
    private static instance: pino.Logger

    // private static readonly logsDirectory = path.resolve(
    //     process.cwd(),
    //     'logs',
    //     'engine-system'
    // )
    private static readonly logsDirectory = path.resolve(
        __dirname,
        '../../../logs/engine-system'
    )

    private static readonly logFiles = ['combined.log', 'error.log']

    // Initialize the logging directory and files
    static initialize() {
        this.ensureDirectoryExists(this.logsDirectory)
        this.ensureLogFilesExist()
    }

    private static readonly streams = Logger.logFiles
        .map((file) => ({
            level: file === 'combined.log' ? 'info' : 'error',
            stream: pino.destination({
                dest: path.resolve(Logger.logsDirectory, file),
                mkdir: true,
                sync: false,
            }),
        }))
        .concat({
            level: 'debug',
            stream: pino.destination({ sync: false }),
        })

    private constructor() {}

    public static getInstance(): pino.Logger {
        if (!Logger.instance) {
            Logger.initialize() // Ensure logs directory and files exist before creating the logger
            Logger.instance = Logger.createLogger()
        }
        return Logger.instance
    }

    private static createLogger(): pino.Logger {
        return pino(
            {
                level: envConfig.nodeEnv === 'production' ? 'info' : 'debug',
                base: { service: 'engine-system' },
            },
            pino.multistream(Logger.streams)
        )
    }

    private static ensureDirectoryExists(directory: string): void {
        if (!fs.existsSync(directory)) {
            try {
                fs.mkdirSync(directory, { recursive: true })
            } catch (error) {
                console.error(`Failed to create directory ${directory}:`, error)
                throw error
            }
        }
    }

    private static ensureLogFilesExist(): void {
        Logger.logFiles.forEach((file) => {
            const filePath = path.resolve(Logger.logsDirectory, file)
            if (!fs.existsSync(filePath)) {
                fs.writeFileSync(filePath, '', 'utf-8')
            }
        })
    }

    public static setLogLevel(level: pino.LevelWithSilent): void {
        if (Logger.instance) {
            Logger.instance.level = level
        }
    }

    /**
     * Logs test data to a site-specific log file with a readable timestamp.
     * Supports both plain text and JSON-formatted data.
     *
     * @param siteName Name of the site (used for the log file name)
     * @param data Data to log (string or object)
     * @param status Status of the test (e.g., 'sent', 'failed')
     * @param isHtml where file is an html file
     */
    public static dbLogger(
        siteName: string,
        data: string | Record<string, any>,
        status: string = '',
        isHtml: boolean = false
    ): void {
        // Ensure the logs directory exists
        this.ensureDirectoryExists(this.logsDirectory)

        const now = new Date()

        const formattedDate = now.toLocaleDateString('en-US', {
            weekday: 'long',
            day: 'numeric',
            month: 'long',
            year: 'numeric',
        })

        const formattedTime = now
            .toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
                hour12: true,
            })
            .toLowerCase()

        // Convert data to string if it's an object
        const stringifiedData =
            typeof data === 'object' ? JSON.stringify(data, null, 2) : data

        // Build the log entry
        const logEntry = `${stringifiedData} - ${status} | ${formattedDate} | ${formattedTime}\n`
        const jsonEntry = `${stringifiedData}\n`
        const htmlEntry = `${stringifiedData}\n`

        const siteLogDir = path.resolve(this.logsDirectory, siteName)
        this.ensureDirectoryExists(siteLogDir)

        const extension = isHtml ? 'html' : 'log'
        // Define the log file path
        const logFilePath = path.resolve(siteLogDir, `${siteName}.${extension}`)

        try {
            // Write to the site-specific log file
            fs.appendFileSync(
                logFilePath,
                typeof data === 'object'
                    ? jsonEntry
                    : isHtml
                      ? htmlEntry
                      : logEntry,
                'utf-8'
            )

            // Centralized logging
            const logger = this.getInstance()
            logger.info(
                {
                    site: siteName,
                    test: data,
                    status,
                    timestamp: now.toISOString(),
                },
                `Site test: ${siteName} - ${status}`
            )
        } catch (error) {
            console.error(
                `Failed to write to site log file ${logFilePath}:`,
                error
            )
            const logger = this.getInstance()
            logger.error(
                {
                    error,
                    site: siteName,
                    test: data,
                    status,
                },
                'Failed to write to site log file'
            )
        }
    }

    public static async closeLogger(): Promise<void> {
        if (Logger.instance) {
            try {
                Logger.instance.flush()
            } catch (error) {
                console.error('Failed to flush logger:', error)
            }
        }
    }
}

export default Logger
