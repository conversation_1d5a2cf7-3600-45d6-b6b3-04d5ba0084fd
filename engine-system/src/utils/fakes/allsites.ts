import { faker } from '@faker-js/faker'

interface TrialSite {
    idSite: string
    siteName: string
    email: string
    trialStartDate: string
    status: string
}

export async function fetchTrialSites(): Promise<Array<TrialSite>> {
    const trialSites: TrialSite[] = []

    for (let i = 0; i < 1000; i++) {
        const siteNameBase = faker.company
            .name()
            .replace(/[^a-zA-Z0-9]/g, '')
            .toLowerCase()
        const siteName = `${siteNameBase}.com`

        trialSites.push({
            idSite: `${i + 1}`,
            siteName: siteName,
            email: `${siteNameBase}@example.com`,
            trialStartDate: faker.date.recent({ days: 14 }).toISOString(),
            status: 'trial',
        })
    }
    return trialSites
}
