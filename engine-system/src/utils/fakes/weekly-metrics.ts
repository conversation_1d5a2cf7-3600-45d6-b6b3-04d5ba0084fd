import { faker } from '@faker-js/faker'
import { loadSiteStateFromCache } from './site-state'
import RedisClient from '../../services/redis'

interface WeeklyMetrics {
    sessions: {
        total: number
        daily: { date: string; count: number }[]
    }
    revenue: {
        total: number
        daily: { date: string; amount: number }[]
        topPages: { url: string; revenue: number }[] // Top 5 revenue pages
    }
    dateRange: {
        startDate: string // ISO date string (YYYY-MM-DD)
        endDate: string // ISO date string (YYYY-MM-DD)
    }
}

export async function fetchWeeklyMetrics(
    trialDayTarget: number,
    idSite: number
): Promise<WeeklyMetrics> {
    const cachedState = await loadSiteStateFromCache(
        RedisClient.getInstance(),
        idSite
    )
    const sessionsActive = cachedState.tracking?.sessions.active || false
    const revenueActive = cachedState.tracking?.revenue.active || false
    const trialStart = new Date(
        cachedState.trialStartDate || faker.date.recent({ days: 14 })
    )

    const days = 7
    const dailySessions: { date: string; count: number }[] = []
    const dailyRevenue: { date: string; amount: number }[] = []
    const topPages: { url: string; revenue: number }[] = []
    let sessionsTotal = 0
    let revenueTotal = 0

    let startDate: Date
    let endDate: Date

    if (trialDayTarget >= 14) {
        const trialEnd = new Date(trialStart)
        trialEnd.setDate(trialStart.getDate() + 14)
        startDate = new Date(trialEnd)
        startDate.setDate(trialEnd.getDate() - 6) // 7 days back from end
        endDate = new Date(trialEnd)
    } else if (trialDayTarget >= 7) {
        startDate = new Date(trialStart)
        endDate = new Date(trialStart)
        endDate.setDate(trialStart.getDate() + 6) // +6 to cover 7 days
    } else {
        // return ;
        console.error(new Error('unsupported trial day target'))
        throw new Error(`Unsupported trialDayTarget: ${trialDayTarget}`)
    }

    for (let i = 0; i < days; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        const dateStr = date.toISOString().split('T')[0]

        const sessionCount = sessionsActive
            ? faker.number.int({ min: 0, max: 2000 })
            : 0
        const revenueAmount = revenueActive
            ? faker.number.int({ min: 0, max: 100000 })
            : 0

        dailySessions.push({ date: dateStr, count: sessionCount })
        dailyRevenue.push({ date: dateStr, amount: revenueAmount })
        sessionsTotal += sessionCount
        revenueTotal += revenueAmount
    }
    // Generate top 5 pages if revenue is active
    if (revenueActive && revenueTotal > 0) {
        const pageCount = 5
        const pageRevenues: { url: string; revenue: number }[] = []
        let remainingRevenue = revenueTotal

        for (let i = 0; i < pageCount; i++) {
            const maxRevenue =
                i === pageCount - 1
                    ? remainingRevenue
                    : remainingRevenue *
                      faker.number.float({ min: 0.1, max: 0.4 })
            const revenue = Math.min(maxRevenue, remainingRevenue)
            const url = `${cachedState.siteName}/${faker.lorem.slug()}`
            pageRevenues.push({ url, revenue })
            remainingRevenue -= revenue
        }

        // Sort by revenue descending
        pageRevenues.sort((a, b) => b.revenue - a.revenue)
        topPages.push(...pageRevenues.slice(0, 5))
    }

    // For end-trial, align with SiteState totalLast7Days
    if (trialDayTarget === 14) {
        const s = cachedState?.tracking?.sessions?.totalLast7Days || 0
        if (sessionsActive && sessionsTotal < s) {
            sessionsTotal = s
        }
        const r = cachedState?.tracking?.revenue?.totalLast7Days || 0
        if (revenueActive && revenueTotal < r) {
            revenueTotal = r
        }
    }

    return {
        sessions: {
            total: sessionsTotal,
            daily: dailySessions,
        },
        revenue: {
            total: revenueTotal,
            daily: dailyRevenue,
            topPages,
        },
        dateRange: {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
        },
    }
}
