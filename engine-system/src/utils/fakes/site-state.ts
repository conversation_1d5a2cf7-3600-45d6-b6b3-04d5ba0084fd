import { faker } from '@faker-js/faker'
import Redis from 'ioredis'
import { SiteState } from '../../types/site'
import { parseNestedObjects } from '../object-utils'

export async function fetchSiteState(siteId: number): Promise<SiteState> {
    // const trialStartDate = faker.date.recent({days: 14});
    const today = new Date('2025-03-26')
    const trialStartDate = faker.date.between({
        from: new Date(today.getTime() - 14 * 24 * 60 * 60 * 1000),
        to: today,
    })
    const trialDaysElapsed = Math.floor(
        (today.getTime() - trialStartDate.getTime()) / (1000 * 60 * 60 * 24)
    )
    const daysSinceLastLogin = faker.number.int({ min: 0, max: 14 })
    const lastLogin = new Date(today)
    lastLogin.setDate(today.getDate() - daysSinceLastLogin)
    const siteNameBase = faker.company
        .name()
        .replace(/[^a-zA-Z0-9]/g, '')
        .toLowerCase()
    const verified = faker.datatype.boolean()
    const verifiedDate = verified
        ? faker.date
              .between({
                  from: trialStartDate,
                  to: today,
              })
              .toISOString()
        : ''
    const snippetInstalled = verified && faker.datatype.boolean()
    const sessionsActive = faker.datatype.boolean()
    const revenueActive = faker.datatype.boolean()
    const name = faker.person.fullName()

    return {
        userName: name,
        full_name: name,
        idSite: siteId,
        siteName: `${siteNameBase}`,
        url: `${siteNameBase}.com`,
        email: `${siteNameBase}@example.com`,
        trialStartDate: trialStartDate.toISOString(),
        status: 'trial',
        verified: verified,
        verified_date: verifiedDate,
        website_type: 'ecommerce',
        snippet: {
            installed: snippetInstalled,
            installDate: snippetInstalled
                ? faker.date
                      .between({
                          from: verifiedDate,
                          to: today,
                      })
                      .toISOString()
                : '',
        },
        tracking: {
            sessions: {
                active: sessionsActive,
                totalLast7Days:
                    snippetInstalled && sessionsActive
                        ? faker.number.int({ min: 0, max: 10000 })
                        : 0,
            },
            revenue: {
                active: revenueActive,
                totalLast7Days:
                    snippetInstalled && sessionsActive && revenueActive
                        ? faker.number.int({ min: 0, max: 500000 })
                        : 0,
            },
        },
        login: {
            lastLogin: lastLogin.toISOString(),
            daysSinceLastLogin,
        },
        creditCard: {
            entered: faker.datatype.boolean(),
        },
        onboardingSteps: faker.helpers.arrayElements([1, 2, 3, 4], {
            min: 0,
            max: 4,
        }),
        trialDaysElapsed,
        ipBlocking: {
            implemented: snippetInstalled && faker.datatype.boolean(),
        },
        features: [],
        platform: 'shopify',
        hasTeamMembers: snippetInstalled && faker.datatype.boolean(),
        notificationsSent: faker.datatype.boolean()
            ? { [faker.lorem.word()]: true }
            : {},
    }
}

export async function loadSiteStateFromCache(
    r: Redis,
    idSite: number
): Promise<SiteState> {
    const cachedStateRaw = await r.hgetall(`site:${idSite}:state`)

    return parseNestedObjects(cachedStateRaw) as SiteState
}
