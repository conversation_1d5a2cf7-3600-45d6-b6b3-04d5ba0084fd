import * as crypto from 'crypto'

export class TokenEncryptor {
    private readonly algorithm = 'aes-256-gcm'
    private readonly secretKey: Buffer

    constructor(secretKey: string) {
        if (secretKey.length !== 64) {
            throw new Error(
                'Secret key must be a 64-character hex string (32 bytes)'
            )
        }
        this.secretKey = Buffer.from(secretKey, 'hex')
    }

    encrypt(plainText: string) {
        const iv = crypto.randomBytes(16)
        const cipher = crypto.createCipheriv(this.algorithm, this.secretKey, iv)
        const encrypted = Buffer.concat([
            cipher.update(plainText, 'utf8'),
            cipher.final(),
        ])
        const tag = cipher.getAuthTag()
        return {
            encryptedData: encrypted.toString('hex'),
            iv: iv.toString('hex'),
            tag: tag.toString('hex'),
        }
    }

    decrypt(encryptedData: string, iv: string, tag: string) {
        const decipher = crypto.createDecipheriv(
            this.algorithm,
            this.secret<PERSON><PERSON>,
            Buffer.from(iv, 'hex')
        )
        decipher.setAuthTag(Buffer.from(tag, 'hex'))
        const decrypted = Buffer.concat([
            decipher.update(Buffer.from(encryptedData, 'hex')),
            decipher.final(),
        ])
        return decrypted.toString('utf8')
    }
}
