import { z } from 'zod'
import { TRIGGER_TYPE } from '../enums/triggers'

export const templateRegistry: Record<
    TRIGGER_TYPE,
    { [key: string]: any; schema: z.ZodObject<any>; subject: string }
> = {
    [TRIGGER_TYPE.CREDIT_CARD]: {
        subject: 'Your heatmap trial is ready—just one more step!',
        schema: z.object({
            email: z.string(),
        }),
    },
    [TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SHARE_DOCS]: {
        subject: 'Still need help with snippet installation?',
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.SNIPPET_NOT_INSTALLED_SCHEDULE_CALL]: {
        subject: 'Still need help with snippet installation?',
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.SESSION_NOT_TRACKING_SHARE_DOCS]: {
        subject: "Sessions not tracking? Here's how to fix it fast!",
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.SESSION_NOT_TRACKING_SCHEDULE_CALL]: {
        subject: "Let's solve your session tracking issue together",
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.REVENUE_NOT_TRACKING_SHARE_DOCS]: {
        subject: "Revenue data missing? Here's your quick guide.",
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.REVENUE_NOT_TRACKING_SCHEDULE_CALL]: {
        subject: 'Need help with revenue tracking? Let’s talk.',
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.WEEKLY_SUMMARY_MID_TRIAL]: {
        subject: 'Your Weekly heatmap Highlights',
        schema: z.object({
            email: z.string().email(),
            firstName: z.string(),
            totalSessionsWeekly: z.number(),
            totalRevenueWeekly: z.number(),
            siteWideRPSWeekly: z.number(),
            siteWideScrollDepthWeekly: z.number(),
            top5RPSPages: z.record(z.any()),
        }),
    },
    [TRIGGER_TYPE.WEEKLY_SUMMARY_END_TRIAL]: {
        subject: 'Your Weekly heatmap Insights are Here!',
        schema: z.object({
            email: z.string().email(),
            firstName: z.string(),
            totalSessionsWeekly: z.number(),
            totalRevenueWeekly: z.number(),
            siteWideRPSWeekly: z.number(),
            siteWideScrollDepthWeekly: z.number(),
            top5RPSPages: z.record(z.any()),
        }),
    },
    [TRIGGER_TYPE.WEEKLY_SUMMARY_RECURRING]: {
        subject: 'Your Weekly heatmap Insights are Here!',
        schema: z.object({
            email: z.string().email(),
            firstName: z.string(),
            totalSessionsWeekly: z.number(),
            totalRevenueWeekly: z.number(),
            siteWideRPSWeekly: z.number(),
            siteWideScrollDepthWeekly: z.number(),
            top5RPSPages: z.record(z.any()),
        }),
    },
    [TRIGGER_TYPE.LOGIN_INACTIVE_SHARE_CASE_STUDY]: {
        subject: 'See how brands boosted revenue 15% with heatmap',
        schema: z.object({
            email: z.string().email(),
            lastLoginDate: z.string(),
        }),
    },
    [TRIGGER_TYPE.LOGIN_INACTIVE_BOOK_DEMO]: {
        subject: 'Let’s jumpstart your heatmap trial with a personalized demo',
        schema: z.object({
            email: z.string().email(),
            lastLoginDate: z.string(),
            daysSinceLastLogin: z.number(),
        }),
    },
    [TRIGGER_TYPE.IP_BLOCKING_NOT_IMPLEMENTED]: {
        subject: 'Exclude your team visits for crystal-clear insights',
        schema: z.object({
            email: z.string().email(),
            FirstName: z.string(),
        }),
    },
    [TRIGGER_TYPE.TEAM_MATES_NOT_INVITED]: {
        subject: 'heatmap is better with your team',
        schema: z.object({
            email: z.string().email(),
        }),
    },
    [TRIGGER_TYPE.NPS]: {
        subject: 'How’s heatmap working for you?',
        schema: z.object({
            email: z.string().email(),
        }),
    },
}
