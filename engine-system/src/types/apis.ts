export type WebsiteInfo = {
    platform: string
    url: string
    id: number
    name: string
    lead: boolean
    features: string[]
}

export type WebsiteInformation = Pick<
    Website,
    'platform' | 'url' | 'id' | 'name' | 'lead' | 'features'
>

export type ExtractedData = {
    start_date: string
    end_date: string
    website: WebsiteInfo
}
export type ExtractedSite = {
    idSite: number
    siteName: string
    siteUrl: string
    email: string
    platform: string
    lead: boolean
    features: string[]
}

export interface CreatedBy {
    name: string
    login: string
    email: string
    [key: string]: any
}
export interface Website {
    id: number
    url: string
    name: string
    industry: string
    platform: string
    verified: number
    created_at: string
    verified_date: string
    lead: boolean
    features: string[]
    [key: string]: any
}
export interface Plan {
    plan: number
    amount: string
    interval: string
    start_date: string
    end_date: string
    status: string
    upgrade: boolean
    plan_name: string
    limit: number
    package_tier: string
    created_by: CreatedBy
    websites: Website[]
    max_sites: string
    sites_count: string
    [key: string]: any
}

export interface Pagination {
    pageCount: number
    currentPage: number
    total: number
    [key: string]: number
}

export type ApiResponse<T> = {
    status: string
    cused: boolean
    data: T
    requestId: string
    [key: string]: any
}

export type PaginatedApiResponse<T> = {
    status: string
    cused: boolean
    data: T[]
    pagination: Pagination
    requestId: string
    [key: string]: any
}
