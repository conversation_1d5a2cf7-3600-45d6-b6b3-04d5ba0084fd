export interface SiteStateOld {
    idSite: string
    email: string
    trialStartDate: string
    trialDaysElapsed: number
    snippetInstalled: boolean
    sessionsActive: boolean
    revenueActive: boolean
    creditCardEntered: boolean
    lastLogin: string | null
    daysSinceLastLogin: number
    gmv: number | null
    avgAov: number | null
    notificationsSent: Record<string, boolean>
}

export interface TrialSite {
    idSite: string
    siteName: string
    email: string
    trialStartDate: string
    status: string
}
export interface SiteStateV0 {
    idSite: string
    siteName: string
    email: string
    trialStartDate: string
    status: 'trial'
    snippet: {
        installed: boolean
        installDate: string | null
    }
    tracking: {
        sessions: {
            active: boolean
            totalLast7Days: number
        }
        revenue: {
            active: boolean
            totalLast7Days: number
        }
    }
    login: {
        lastLogin: string
        daysSinceLastLogin: number
    }
    creditCard: {
        entered: boolean
    }
    onboardingSteps: number[]
    trialDaysElapsed: number
    userId?: string
    // notificationsSent?: Record<string, boolean>
}

export interface WeeklyMetrics {
    sessions: {
        total: number
        daily: { date: string; count: number }[]
    }
    revenue: {
        total: number
        daily: { date: string; amount: number }[]
        topPages: { url: string; revenue: number }[] // Top 5 revenue pages
    }
    dateRange: {
        startDate: string // ISO date string (YYYY-MM-DD)
        endDate: string // ISO date string (YYYY-MM-DD)
    }
}

export interface SiteState {
    siteName: string
    url: string
    platform: string
    verified: boolean
    verified_date?: string // iso date
    website_type: string
    email: string
    status: string
    trialStartDate: string // iso date
    userName: string
    full_name: string
    snippet: {
        installed: boolean
        installDate: string // iso date
    }
    tracking: {
        sessions: {
            totalLast7Days: number
            active: boolean
        }
        revenue: {
            totalLast7Days: number
            active: boolean
        }
    }
    login: {
        lastLogin: string
        daysSinceLastLogin: number
    }
    creditCard: { entered: boolean | number }
    onboardingSteps: number[]
    ipBlocking: {
        implemented: boolean
    }
    hasTeamMembers: boolean
    features: string[]
    trialDaysElapsed: number
    idSite?: number
    notificationsSent?: Record<string, boolean>
}

export interface SummaryMetrics {
    revenue: string
    sessions: string
    rps: string
    time_on_site: string
    scroll_depth: string
    top_5_pages_rps: {
        url: string
        name: string
        rps: string
        revenue: string
    }[]
}
