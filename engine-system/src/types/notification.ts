export interface NotificationJob {
    idSite: number
    trigger: string
    priority?: number
    data: Record<string, any>
    channel: string
}

// types/notification.ts
export interface BaseMessage {
    [key: string]: any
}

export interface SendResult {
    success: boolean
    messageId?: string
    response?: string
    error?: string
}

export interface ConnectionConfig {
    [key: string]: any
}

export interface SmtpConfig extends ConnectionConfig {
    defaultFrom?: string
    defaultReplyTo?: string
    defaultSubject?: string
}

export interface EmailMessage extends BaseMessage {
    from?: string
    to: string
    subject?: string
    text?: string
    html?: string
    cc?: string | string[]
    bcc?: string | string[]
    attachments?: any[]
    replyTo?: string
}

export type MailAttachmentT = {
    name: string
    content: string
}

export type EmailParamsT = {
    [key: string]: any
}

export interface HubSpotEmailMessage {
    from: string
    to: string | string[]
    subject: string
    text: string
    html: string
    cc?: string | string[]
    bcc?: string | string[]
    attachments?: MailAttachmentT[]
    params?: EmailParamsT
}
