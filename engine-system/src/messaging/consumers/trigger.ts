import { QueueService } from '../queue-service'

import { TriggerQueue } from '../../jobs/trigger/trigger-queue'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()

export async function startTriggerConsumer(triggerQueue: TriggerQueue) {
    const queueService = QueueService.getInstance()
    await queueService.initialize()

    const exchange = 'triggers'

    const triggerCategories = [
        {
            queue: 'triggers.onboarding',
            binding: 'triggers.onboarding.*',
            prefetch: 50,
        },
        {
            queue: 'triggers.summaries',
            binding: 'triggers.summaries.*',
            prefetch: 30,
        },
        {
            queue: 'triggers.engagement',
            binding: 'triggers.engagement.*',
            prefetch: 40,
        },
        {
            queue: 'triggers.default',
            binding: 'triggers.default.*',
            prefetch: 20,
        },
    ]

    for (const { queue, binding, prefetch } of triggerCategories) {
        await queueService.assertExchange(exchange, 'topic')
        // await queueService.assertQueue(queue, { durable: true , arguments: { "x-max-length": 1000}})
        await queueService.assertQueue(queue, { durable: true })
        await queueService.bindQueue(queue, exchange, binding)
        await queueService.prefetch(prefetch)

        await queueService.consume(
            queue,
            async (msg) => {
                if (msg) {
                    const job = JSON.parse(msg.content.toString())
                    if (queue === 'triggers.default') {
                        logger.warn(
                            `uncategorized trigger routed to default queue: ${job.trigger || 'unknown'} `
                        )
                    }
                    await triggerQueue.addJob(job, job.priority)
                    await queueService.ack(msg)
                }
            },
            { noAck: false }
        )

        logger.info(`Trigger consumer started for queue: ${queue}`)
    }
    logger.info('All trigger consumers initialized')
}
