import { QueueService } from '../queue-service'
import { TriggerQueue } from '../../jobs/trigger/trigger-queue'
import { EvaluationQueue } from '../../jobs/evaluation/evaluation-queue'
import { TrialSite } from '../../types/site'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()

export async function startSiteEvaluationConsumer(
    evaluationQueue: EvaluationQueue
): Promise<void> {
    const queueService = QueueService.getInstance()
    await queueService.initialize()

    const exchange = 'site_evaluation'

    const queue = 'site_evaluation.all'

    const routingKey = 'site_evaluation.all'

    await queueService.assertExchange(exchange, 'direct')
    // await queueService.assertQueue(queue, {  durable: true , arguments: { 'x-max-length': 10000}})
    await queueService.assertQueue(queue, { durable: true })
    await queueService.bindQueue(queue, exchange, routingKey)

    await queueService.prefetch(50)

    await queueService.consume(queue, async (msg) => {
        if (msg) {
            const job = JSON.parse(msg.content.toString()) as TrialSite
            await evaluationQueue.addJob({ ...job }, 1)
            await queueService.ack(msg)
        }
    })
    logger.info('site evaluation consumer started')
}
