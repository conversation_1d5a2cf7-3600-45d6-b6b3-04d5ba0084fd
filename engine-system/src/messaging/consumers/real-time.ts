// import { EvaluationQueue } from '../../jobs/evaluation/evaluation-queue'
// import { QueueService } from '../queue-service'
// import Logger from '../../utils/logger'

// const logger = Logger.getInstance()

// export async function startRealTimeConsumer(evaluationQueue: EvaluationQueue) {
//     const queueService = QueueService.getInstance()
//     await queueService.initialize()

//     const exchange = 'real_time_events'
//     const events = [
//         {
//             queue: 'real_time_events.aov_drop',
//             binding: 'real_time_events.aov_drop',
//         },
//         {
//             queue: 'real_time_events.user_login',
//             binding: 'real_time_events.user_login',
//         },
//         {
//             queue: 'real_time_events.revenue_spike',
//             binding: 'real_time_events.revenue_spike',
//         },
//     ]

//     for (const { queue, binding } of events) {
//         await queueService.assertExchange(exchange, 'direct')
//         await queueService.assertQueue(queue, { durable: true })
//         await queueService.bindQueue(queue, exchange, binding)
//         await queueService.prefetch(50)

//         await queueService.consume(queue, async (msg) => {
//             if (msg) {
//                 const job = JSON.parse(msg.content.toString())
//                 await evaluationQueue.addJob(job, job.priority || 1)
//                 await queueService.ack(msg)
//             }
//         })

//         logger.info(`Real-time consumer started for ${queue}`)
//     }
// }
