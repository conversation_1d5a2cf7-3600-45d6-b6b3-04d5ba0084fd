import { <PERSON><PERSON><PERSON><PERSON> } from '../../types/trigger'
import { NotificationJob } from '../../types/notification'
import { QueueService } from '../queue-service'
import Logger from '../../utils/logger'
import { NotificationJobData } from '../../jobs/notification/jobs'

const logger = Logger.getInstance()

type PublishableJob = TriggerJob | NotificationJobData

export async function publishToExchange<T extends PublishableJob>(
    exchange: string,
    routingKey: string,
    data: T,
    retries: number = 3
): Promise<void> {
    const queueService = QueueService.getInstance()

    let attempt = 0

    while (attempt < retries) {
        try {
            await queueService.publishToExchange(
                exchange,
                routingKey,
                Buffer.from(JSON.stringify(data))
            )
            logger.info(
                `Published to ${exchange} with routingKey ${routingKey}`
            )
            return
        } catch (e) {
            attempt++
            logger.error(
                `Failed to publish to ${exchange}  (attempt ${attempt}/${retries}): ${e instanceof Error ? e.message : e}`
            )
            if (attempt === retries) {
                logger.error(
                    `Max retries reached for ${exchange}. Dropping message`
                )
                return
            }
            await new Promise((resolve) => setTimeout(resolve, 1000 * attempt))
        }
    }
}

export async function publishToTriggersExchange(data: TriggerJob) {
    const t = data.trigger || data.eventType?.toLowerCase() || 'all'
    await publishToExchange('triggers', `triggers.${t}`, data)
}

export async function publishToTriggersExchangeV1(data: TriggerJob) {
    const trigger = data.trigger || data.eventType?.toLowerCase() || 'default'
    let category = data.category?.toLowerCase() || 'default'

    const routingKey = `triggers.${category}.${trigger}`
    await publishToExchange('triggers', routingKey, data)
}

export async function publishToNotificationExchange(data: NotificationJobData) {
    const ch = data.channel

    await publishToExchange('notifications', `notifications.${ch}`, data)
}
