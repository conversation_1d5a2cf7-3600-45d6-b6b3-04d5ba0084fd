import { fetchTrialSites } from '../../utils/fakes/allsites'
import { Request, Response } from 'express'
import { fetchSiteState } from '../../utils/fakes/site-state'

export const getTrialingSites = async (req: Request, res: Response) => {
    const sites = await fetchTrialSites()
    res.json({ data: sites })
}

export const getSiteState = async (req: Request, res: Response) => {
    const { params } = req
    if (!params || !params.idSite) {
        res.status(400).json({
            error: 'Missing required parameters (idSite)',
        })
        return
    }
    const siteState = await fetchSiteState(Number.parseInt(params.idSite))
    res.json({ data: siteState })
}
