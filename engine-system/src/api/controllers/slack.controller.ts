import { Request, Response } from 'express'
import {
    BadRequestError,
    createErrorResponse,
} from '../utils/custom.errors'
import axios from 'axios'
import { WebClient } from '@slack/web-api'
import prisma from '../../db'
import Logger from '../../utils/logger'
import { TokenEncryptor } from '../../utils/security/token-encyptor'
import { envConfig } from '../../env.constant'
import { getCachedChannelTypeId } from '../../services/look-up/channel-types'
import { NotificationStateT, validateNotificationState } from '../schemas/channels'
import { z } from 'zod'
import RedisClient from '../../services/redis'

const redis = RedisClient.getInstance()
const logger = Logger.getInstance()
const tokenEncryptor = new TokenEncryptor(envConfig.encryptionKey)


export const sendNotification = async(req: Request, res: Response) => {
    try {
        const { body } = req;

        let notificationState: NotificationStateT

        try {
            notificationState = validateNotificationState(body)
        } catch (error) {
            if (error instanceof z.ZodError) {
                throw new BadRequestError('Validation Error', error.issues)
            } else {
                throw new BadRequestError(
                    'Json Parsing Failed -- Invalid Payload'
                )
            }
        }

        const { idsite, trigger_id, message } = notificationState;

        const userSiteToken = await prisma.siteToken.findFirst({
            where: { idSite: Number(idsite) },
        })
        if (!userSiteToken)
            throw new Error(`Please connect slack app to fetch channels`);

        const slackChannelType = await getCachedChannelTypeId('slack')
        let channel_type_id = 2
        if (slackChannelType) {
            channel_type_id = slackChannelType.id
        }

        // const [triggerChannelId, defaultChannel] = await Promise.all([
        //     prisma.siteNotificationPreference.findFirst({
        //         where: {
        //             idSite: idsite, triggerId: trigger_id, channelId: channel_type_id,
        //             destination: {path:'default', equals: false}
        //         }
        //     }),
        //     prisma.siteNotificationPreference.findFirst({
        //         where: {
        //             idSite: idsite, channelId: channel_type_id,
        //             destination: {path: 'default', equals: true}
        //         }
        //     })
        // ]);

        const triggerChannelId: any = await prisma.$queryRaw`SELECT * FROM site_notification_preferences
            WHERE id_site = ${idsite}
            AND channel_id = ${channel_type_id}
            AND trigger_id = ${trigger_id}
            AND JSON_UNQUOTE(JSON_EXTRACT(destination, '$.default')) = 'false'
            LIMIT 1
        `

        const defaultChannel: any = await prisma.$queryRaw`SELECT * FROM site_notification_preferences
        WHERE id_site = ${idsite}
        AND channel_id = ${channel_type_id}
        AND JSON_UNQUOTE(JSON_EXTRACT(destination, '$.default')) = 'true'
        LIMIT 1
        `

        if(!defaultChannel || defaultChannel.length < 1) throw new Error(`Please connect slack app to fetch channels`);

        const { iv, tag, accessTokenEncrypted } = userSiteToken
        const decryptedToken = tokenEncryptor.decrypt(accessTokenEncrypted, iv, tag);


        let slackChannelId: string = (defaultChannel as any)[0].destination.channel_id;

        if(triggerChannelId && triggerChannelId.length > 0){
            slackChannelId = (triggerChannelId as any)[0].destination?.channel_id
        }

        const slackClient = new WebClient(decryptedToken);

        await slackClient.chat.postMessage({
            channel: slackChannelId,
            text: message,
        });

        res.status(200).json({message: "Notification sent"})
    } catch(error){
        logger.error(error)
        return createErrorResponse(error, res)
    }

}