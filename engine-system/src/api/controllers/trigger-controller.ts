import { z } from 'zod'
import { Request, Response } from 'express'
import { BadRequestError, createErrorResponse } from '../utils/custom.errors'
import { SiteStateT, validateSiteState } from '../schemas/site-state'
import RedisClient from '../../services/redis'
import { stringifyNestedObjects } from '../../utils/object-utils'
import { TriggerEvaluator } from '../../services/trigger-evaluator'
import { SiteState } from '../../types/site'
import { publishToTriggersExchangeV1 } from '../../messaging/producers'
import Logger from '../../utils/logger'
import { RedisKeys } from '../../constants/redis-keys'

const redis = RedisClient.getInstance()
const logger = Logger.getInstance()

export const testTriggerController = async (req: Request, res: Response) => {
    try {
        const { body } = req
        if (!body) {
            throw new BadRequestError('body is missing')
        }
        let triggerState: SiteStateT

        try {
            triggerState = validateSiteState(body)
        } catch (error) {
            if (error instanceof z.ZodError) {
                throw new BadRequestError('Validation Error', error.issues)
            } else {
                throw new BadRequestError(
                    'Json Parsing Failed -- Invalid Payload'
                )
            }
        }
        const siteStateRedisKey = RedisKeys.SITE_STATE_KEY(triggerState.idSite)
        await redis.hset(
            siteStateRedisKey,
            stringifyNestedObjects({
                ...triggerState,
                notificationsSent: triggerState.notificationsSent || {},
            })
        )

        const evaluator = new TriggerEvaluator(triggerState as SiteState)
        const triggerResult = await evaluator.evaluateTriggers()
        const redisKey = RedisKeys.NOTIFICATIONS_SENT_CHANNEL_KEY(
            triggerState.idSite
        )

        if (!triggerResult) {
            const notifications = await redis.smembers(redisKey)
            if (notifications.length === 0) {
                res.status(200).json({ message: 'No Trigger Fired' })
            } else {
                res.status(200).json({
                    message:
                        'No Trigger Fired, check notifications already sent',
                    notifications,
                })
            }
            // createSuccessResponse(res, {message: "No Trigger Fired"})
            return
        }
        // if (await redis.sismember(redisKey, triggerResult.trigger.toString())) {
        //     res.status(200).json({
        //         message: `Notification already processed for trigger:${triggerResult?.trigger}`,
        //         trigger: triggerResult?.trigger,
        //     })
        //     return
        // }

        await publishToTriggersExchangeV1({
            idSite: +triggerState.idSite,
            data: {
                email: triggerState.email,
                trialDaysElapsed: triggerState.trialDaysElapsed,
            },
            priority: triggerResult.priority,
            trigger: triggerResult.trigger,
            category: triggerResult.category,
        })
        res.status(200).json({
            message:
                'Trigger evaluated and job produced, should receive a notification shortly',
            trigger: triggerResult.trigger,
        })
    } catch (err) {
        logger.error(err)
        return createErrorResponse(err, res)
    }
}
