import { publishToNotificationExchange } from '../../messaging/producers'
import { Request, Response } from 'express'
import { BadRequestError, createErrorResponse } from '../utils/custom.errors'
import { z } from 'zod'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()

import {
    RealTimeNotification,
    validateRealTimeNotification,
} from '../schemas/real-time.notification'

export const sendNotificationController = async (
    req: Request,
    res: Response
) => {
    try {
        if (!req.body) {
            throw new BadRequestError('body is required')
        }
        let realTimeNotification: RealTimeNotification
        try {
            realTimeNotification = validateRealTimeNotification(req.body)
        } catch (error) {
            if (error instanceof z.ZodError) {
                logger.error('Validation Error', error.issues)
                throw new BadRequestError('validation Error', error.issues)
            } else {
                logger.error('Invalid JSON Payload -- Parsing Failed:', error)
                throw new BadRequestError(
                    'Invalid JSON Payload -- Parsing Failed'
                )
            }
        }

        const message = {
            type: 'real-time',
            body: realTimeNotification.body,
            subject: realTimeNotification.subject,
            email: realTimeNotification.email,
        }

        await publishToNotificationExchange({
            type: 'real-time',
            channel: 'email',
            data: {
                email: message.email,
                subject: message.subject,
                body: message.body,
            },
        })
        res.status(202).json({ message: 'Notification queued successfully' })
    } catch (err) {
        logger.error('Publish error:', err)
        return createErrorResponse(err, res)
    }
}
