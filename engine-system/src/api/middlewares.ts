import { Express, NextFunction, Request, Response } from 'express'
import helmet from 'helmet'
import cors from 'cors'
import bodyParser from 'body-parser'
import prisma from '../db'

const EXCLUDED_PATHS = ['/admin/queues', '/up']

export const disconnectPrismaV1 = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    if (EXCLUDED_PATHS.some((path) => req.originalUrl.startsWith(path))) {
        return next()
    }
    let isCleanUp = false
    const cleanup = async (name: string) => {
        if (isCleanUp) {
            return
        }
        try {
            isCleanUp = true
            await prisma.$disconnect()
            console.log('prisma disconnect activated in', name)
        } catch (err) {
            console.error('Prisma disconnect failed in', name)
            isCleanUp = false
        }
    }

    res.on('close', async () => {
        await cleanup('close')
    })

    res.on('finish', async () => {
        await cleanup('finish')
    })

    next()
}

export const setupMiddlewares = (app: Express) => {
    app.use(helmet())
    app.use(cors())
    app.use(bodyParser.json())
    app.use(bodyParser.urlencoded({ extended: true }))
    app.use(disconnectPrismaV1)
}
