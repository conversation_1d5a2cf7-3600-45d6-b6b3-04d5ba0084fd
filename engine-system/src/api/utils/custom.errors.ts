import { Response as ExpressResponse } from 'express'

class HttpError extends Error {
    statusCode: number
    errors?: object

    constructor({
        message,
        statusCode,
        errors = {},
    }: {
        message: string
        statusCode: number
        errors?: object
    }) {
        super(message)
        this.statusCode = statusCode
        this.errors = errors
        Error.captureStackTrace(this, HttpError)
    }
}

export class ClientError extends HttpError {}
export class ServerError extends HttpError {}

export class BadRequestError extends ClientError {
    constructor(message: string = 'Bad Request', errors: object = {}) {
        super({ message, statusCode: 400, errors })
    }
}

export class NotFoundError extends ClientError {
    constructor(message: string = 'Not Found', errors: object = {}) {
        super({ message, statusCode: 404, errors })
    }
}

export class UnauthorizedError extends ClientError {
    constructor(message: string = 'Unauthorized', errors: object = {}) {
        super({ message, statusCode: 401, errors })
    }
}

export class ForbiddenError extends ClientError {
    constructor(message: string = 'Forbidden', errors: object = {}) {
        super({ message, statusCode: 403, errors })
    }
}

export class UnprocessableEntityError extends ClientError {
    constructor(message: string = 'Unprocessable Entity', errors: object = {}) {
        super({ message, statusCode: 422, errors })
    }
}

export class ConflictError extends ClientError {
    constructor(message: string = 'Conflict', errors: object = {}) {
        super({ message, statusCode: 409, errors })
    }
}

export class InternalServerError extends ServerError {
    constructor(
        message: string = 'Internal Server Error',
        errors: object = {}
    ) {
        super({ message, statusCode: 500, errors })
    }
}

export function createErrorResponse(
    error: unknown,
    res: ExpressResponse
): void {
    console.error(error)

    if (error instanceof HttpError) {
        res.status(error.statusCode).json({
            message: error.message,
            errors: error.errors,
        })
    } else {
        res.status(500).json({
            message: 'Internal Server Error',
        })
    }
}
