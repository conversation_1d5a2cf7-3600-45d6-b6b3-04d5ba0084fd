import { Express } from 'express'
import { ExpressAdapter } from '@bull-board/express'
import { createBullBoard } from '@bull-board/api'
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter'
import { EvaluationQueue } from '../../jobs/evaluation/evaluation-queue'
import { envConfig } from '../../env.constant'
import { BotQueue } from '../../jobs/bot/bot-queue'
import { TriggerQueue } from '../../jobs/trigger/trigger-queue'
import { NotificationQueue } from '../../jobs/notification/notification-queue'
import { ObservationQueue } from '../../jobs/observation/observation-queue'

const evaluationQueue = EvaluationQueue.getInstance({
    port: envConfig.redis.port,
    host: envConfig.redis.host,
})
const botQueue = BotQueue.getInstance({
    port: envConfig.redis.port,
    host: envConfig.redis.host,
})
const triggerQueue = TriggerQueue.getInstance({
    port: envConfig.redis.port,
    host: envConfig.redis.host,
})
const notificationQueue = NotificationQueue.getInstance({
    port: envConfig.redis.port,
    host: envConfig.redis.host,
})
const observationQueue = ObservationQueue.getInstance({
    port: envConfig.redis.port,
    host: envConfig.redis.host,
})
const serverAdapter = new ExpressAdapter()
serverAdapter.setBasePath('/admin/queues')
export const setupBullBoard = (app: Express): void => {
    const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard(
        {
            queues: [
                new BullMQAdapter(botQueue.getQueue()),
                new BullMQAdapter(evaluationQueue.getQueue()),
                new BullMQAdapter(observationQueue.getQueue()),
                new BullMQAdapter(notificationQueue.getQueue()),
                new BullMQAdapter(triggerQueue.getQueue()),
            ],
            serverAdapter: serverAdapter,
        }
    )

    app.use('/admin/queues', serverAdapter.getRouter())
}
