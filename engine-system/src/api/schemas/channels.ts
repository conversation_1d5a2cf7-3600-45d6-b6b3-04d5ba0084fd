import { z } from 'zod'


const _IdsiteBase = z.object({
    idsite: z.string(),
    limit: z.number().optional(),
    page: z.number().optional()
});

export const _IdSiteState = _IdsiteBase.transform((data) => (data))

export type IdSiteStateT = z.infer<typeof _IdSiteState>

export function validateIdSiteState(data: unknown): IdSiteStateT{
    const result = _IdSiteState.parse(data)
    return result as IdSiteStateT;
}


const _ChannelBase = z.object({
    idsite: z.number(),
    channel_id: z.number(),
    trigger_id: z.number(),
    slack_channel_name: z.string(),
    slack_channel_id: z.string(),
});

export const _ChannelState = _ChannelBase.transform((data) => (data))

export type ChannelStateT = z.infer<typeof _ChannelState>

export function validateChannelState(data: unknown): ChannelStateT{
    const result = _ChannelState.parse(data)
    return result as ChannelStateT;
}

const _NotificationBase = z.object({
    idsite: z.number(),
    trigger_id: z.number(),
    message: z.string()
});

export const _NotificationState = _NotificationBase.transform((data) => (data))

export type NotificationStateT = z.infer<typeof _NotificationState>

export function validateNotificationState(data: unknown): NotificationStateT{
    const result = _NotificationState.parse(data)
    return result as NotificationStateT;
}