import { z } from 'zod'

const calculateDaysElapsed = (startDateStr: string | Date) => {
    const start = new Date(startDateStr)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - start.getTime())
    return Math.floor(diffTime / (1000 * 60 * 60 * 24))
}

const _SiteStateBase = z.object({
    idSite: z.number(),
    siteName: z.string(),
    email: z.string(),
    trialStartDate: z.union([z.string(), z.coerce.date()]),
    status: z.string().default('trial'),
    full_name: z.string().default(''),
    userName: z.string().default(''),
    snippet: z.object({
        installed: z.boolean(),
        installDate: z.union([z.string(), z.coerce.date()]),
    }),
    tracking: z.object({
        sessions: z.object({
            active: z.boolean(),
            totalLast7Days: z.number().default(0),
        }),
        revenue: z.object({
            active: z.boolean(),
            totalLast7Days: z.number().default(0),
        }),
    }),
    login: z.object({
        lastLogin: z.union([z.string(), z.coerce.date()]),
        daysSinceLastLogin: z.number(),
    }),
    creditCard: z.object({
        entered: z.boolean(),
    }),
    platform: z.string(),
    notificationsSent: z.record(z.any()).default({}),
    onboardingSteps: z.array(z.number()).default([]),
    ipBlocking: z.object({
        implemented: z.boolean(),
    }),
    hasTeamMembers: z.boolean(),
    url: z.string().url(),
    verified: z.boolean().default(false),
    verified_date: z.coerce.date().optional(),
    website_type: z.string(),
    features: z.array(z.string()).optional(),
})

export const _SiteState = _SiteStateBase.transform((data) => ({
    ...data,
    trialDaysElapsed: calculateDaysElapsed(data.trialStartDate),
}))

export type SiteStateT = z.infer<typeof _SiteState>

export function validateSiteState(data: unknown): SiteStateT {
    const result = _SiteState.parse(data)
    return result as SiteStateT
}
