# Queue System Documentation

## Overview

The notification engine implements a sophisticated dual-queue architecture combining BullMQ (Redis-based) and RabbitMQ to handle different types of workloads with optimal performance and reliability.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   BullMQ        │    │   RabbitMQ      │    │   Consumers     │
│   (Redis)       │    │   (AMQP)        │    │                 │
│                 │    │                 │    │                 │
│ • Job Queues    │    │ • Exchanges     │    │ • Site Eval     │
│ • Scheduling    │    │ • Routing       │    │ • Triggers      │
│ • Retry Logic   │    │ • Pub/Sub       │    │ • Notifications │
│ • Monitoring    │    │ • Distribution  │    │ • Observations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## BullMQ Queues (Redis-based)

### Queue Types

#### 1. EvaluationQueue
**Purpose**: Site evaluation and trigger condition checking

```typescript
interface SiteEvaluationJob {
  sites: number[];           // Site IDs to evaluate
  triggerTypes?: string[];   // Specific trigger types
  immediate?: boolean;       // Skip scheduling delay
}
```

**Features**:
- Batch processing of multiple sites
- Configurable evaluation intervals
- Priority-based processing
- Automatic retry with exponential backoff

**Configuration**:
```typescript
const evaluationQueue = new EvaluationQueue({
  connection: { host: 'redis-host', port: 6379 },
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: { type: 'exponential', delay: 2000 }
  }
});
```

#### 2. TriggerQueue
**Purpose**: Process trigger conditions and determine notification eligibility

```typescript
interface TriggerJob {
  idSite: number;
  triggerName: string;
  triggerData: Record<string, any>;
  evaluationId?: string;
  metadata?: Record<string, any>;
}
```

**Processing Flow**:
1. Receive trigger evaluation request
2. Check trigger conditions against site data
3. Validate cooldown periods and limits
4. Generate notification jobs if conditions met
5. Update trigger statistics and logs

#### 3. NotificationQueue
**Purpose**: Handle notification delivery across all channels

```typescript
interface NotificationJob {
  idSite: number;
  triggerName: string;
  templateName: string;
  channelType: 'email' | 'slack' | 'hubspot';
  recipient: string;
  templateData: Record<string, any>;
  priority?: number;
  scheduledFor?: Date;
}
```

**Features**:
- Multi-channel delivery support
- Template rendering and validation
- Delivery status tracking
- Error handling and retry logic

#### 4. BotQueue
**Purpose**: Automated tasks and maintenance operations

```typescript
interface BotJob {
  taskType: 'cleanup' | 'maintenance' | 'sync' | 'report';
  parameters: Record<string, any>;
  scheduledTime?: Date;
}
```

**Common Tasks**:
- Database cleanup operations
- Cache warming and maintenance
- External service synchronization
- Periodic reporting and analytics

#### 5. ObservationQueue
**Purpose**: Analytics, monitoring, and data collection

```typescript
interface ObservationJob {
  observationType: 'site_metrics' | 'user_behavior' | 'system_health';
  dataPoints: Record<string, any>;
  aggregationLevel: 'hourly' | 'daily' | 'weekly';
}
```

## RabbitMQ Messaging (AMQP)

### Exchange Configuration

#### TriggerExchange
**Type**: Topic Exchange
**Purpose**: Route trigger events to appropriate consumers

```typescript
// Exchange configuration
await queueService.assertExchange('trigger-exchange', 'topic', {
  durable: true,
  autoDelete: false
});

// Routing patterns
const routingKeys = {
  trialSites: 'trigger.trial.*',
  paidSites: 'trigger.paid.*',
  urgentTriggers: 'trigger.*.urgent'
};
```

#### NotificationExchange  
**Type**: Topic Exchange
**Purpose**: Distribute notification jobs to channel-specific consumers

```typescript
// Routing by channel
const notificationRouting = {
  email: 'notification.email.*',
  slack: 'notification.slack.*',
  hubspot: 'notification.hubspot.*'
};
```

### Queue Binding Examples

```typescript
// Bind queues to exchanges with routing keys
await queueService.bindQueue('email-notifications', 'notification-exchange', 'notification.email.*');
await queueService.bindQueue('slack-notifications', 'notification-exchange', 'notification.slack.*');
await queueService.bindQueue('urgent-triggers', 'trigger-exchange', 'trigger.*.urgent');
```

## Queue Service Implementation

### Connection Management

```typescript
export class QueueService {
  private connection?: Connection;
  private channel?: Channel;
  private isInitialized = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  async initialize(): Promise<void> {
    try {
      this.connection = await amqp.connect({
        protocol: 'amqp',
        hostname: envConfig.rabbit.host,
        username: envConfig.rabbit.username,
        password: envConfig.rabbit.password,
        heartbeat: 60
      });

      this.channel = await this.connection.createChannel();
      this.setupEventListeners();
      this.isInitialized = true;

      logger.info('RabbitMQ connection established');
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ:', error);
      await this.handleReconnect();
    }
  }

  private setupEventListeners(): void {
    this.connection?.on('error', async (err) => {
      logger.error('RabbitMQ connection error:', err);
      this.isInitialized = false;
      await this.handleReconnect();
    });

    this.connection?.on('close', async () => {
      logger.warn('RabbitMQ connection closed');
      this.isInitialized = false;
      if (!this.isClosing) {
        await this.handleReconnect();
      }
    });
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.pow(2, this.reconnectAttempts) * 1000;
    
    logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.initialize();
    }, delay);
  }
}
```

### Message Publishing

```typescript
// Publish to exchange with routing
export async function publishToExchange<T>(
  exchange: string,
  routingKey: string,
  data: T,
  retries: number = 3
): Promise<void> {
  const queueService = QueueService.getInstance();
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await queueService.publishToExchange(
        exchange,
        routingKey,
        Buffer.from(JSON.stringify(data)),
        { persistent: true }
      );
      
      logger.info('Message published successfully', {
        exchange,
        routingKey,
        attempt
      });
      return;
    } catch (error) {
      logger.error(`Publish attempt ${attempt} failed:`, error);
      
      if (attempt === retries) {
        throw new Error(`Failed to publish after ${retries} attempts`);
      }
      
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, attempt) * 1000)
      );
    }
  }
}
```

## Consumer Implementation

### Site Evaluation Consumer

```typescript
export async function startSiteEvaluationConsumer(
  queue: EvaluationQueue
): Promise<void> {
  const queueService = QueueService.getInstance();
  
  await queueService.assertQueue('site-evaluation-queue', { durable: true });
  
  await queueService.consume('site-evaluation-queue', async (message) => {
    if (!message) return;
    
    try {
      const jobData: SiteEvaluationJob = JSON.parse(message.content.toString());
      
      logger.info('Processing site evaluation job', { 
        sites: jobData.sites.length,
        triggerTypes: jobData.triggerTypes 
      });
      
      // Process each site
      for (const siteId of jobData.sites) {
        await evaluateSiteConditions(siteId, jobData.triggerTypes);
      }
      
      // Acknowledge message
      queueService.ack(message);
      
    } catch (error) {
      logger.error('Site evaluation failed:', error);
      
      // Reject and requeue with limit
      queueService.nack(message, false, true);
    }
  });
}
```

### Notification Consumer

```typescript
export async function startNotificationConsumer(
  queue: NotificationQueue
): Promise<void> {
  const queueService = QueueService.getInstance();
  
  await queueService.assertQueue('notification-queue', { durable: true });
  
  await queueService.consume('notification-queue', async (message) => {
    if (!message) return;
    
    try {
      const jobData: NotificationJobData = JSON.parse(message.content.toString());
      
      // Render template
      const renderedContent = await renderTemplate(
        jobData.templateName,
        jobData.channelType,
        jobData.templateData
      );
      
      // Send notification
      const connector = ConnectorFactory.getConnector(jobData.channelType);
      await connector.send({
        recipient: jobData.recipient,
        subject: renderedContent.subject,
        content: renderedContent.body,
        metadata: jobData.templateData
      });
      
      // Log success
      await logNotificationStatus(jobData, 'sent');
      
      queueService.ack(message);
      
    } catch (error) {
      logger.error('Notification delivery failed:', error);
      
      // Log failure
      await logNotificationStatus(jobData, 'failed', error.message);
      
      queueService.nack(message, false, false); // Don't requeue
    }
  });
}
```

## Queue Monitoring

### Bull Board Integration

```typescript
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';

const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

export const setupBullBoard = (app: Express): void => {
  const { addQueue } = createBullBoard({
    queues: [
      new BullMQAdapter(evaluationQueue.getQueue()),
      new BullMQAdapter(triggerQueue.getQueue()),
      new BullMQAdapter(notificationQueue.getQueue()),
      new BullMQAdapter(botQueue.getQueue()),
      new BullMQAdapter(observationQueue.getQueue())
    ],
    serverAdapter: serverAdapter
  });

  app.use('/admin/queues', serverAdapter.getRouter());
};
```

**Dashboard Features**:
- Real-time queue status and metrics
- Job details and execution logs
- Retry and failure management
- Performance analytics
- Manual job triggering

### Custom Metrics

```typescript
// Queue health monitoring
export class QueueMetrics {
  static async getQueueStats(queueName: string) {
    const queue = getQueueInstance(queueName);
    
    return {
      waiting: await queue.getWaiting(),
      active: await queue.getActive(),
      completed: await queue.getCompleted(),
      failed: await queue.getFailed(),
      delayed: await queue.getDelayed(),
      
      // Performance metrics
      throughput: await this.calculateThroughput(queue),
      averageProcessingTime: await this.getAverageProcessingTime(queue),
      errorRate: await this.calculateErrorRate(queue)
    };
  }
  
  static async calculateThroughput(queue: Queue): Promise<number> {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const completedJobs = await queue.getJobs(['completed'], oneHourAgo);
    return completedJobs.length;
  }
}
```

## Error Handling and Recovery

### Retry Strategies

```typescript
// Exponential backoff with jitter
const retryConfig = {
  attempts: 5,
  backoff: {
    type: 'exponential',
    delay: 2000,
    settings: {
      jitter: true  // Add randomness to prevent thundering herd
    }
  }
};

// Custom retry logic
const customRetry = {
  attempts: 3,
  backoff: (attemptsMade: number) => {
    return Math.min(Math.pow(2, attemptsMade) * 1000, 30000);
  }
};
```

### Dead Letter Queues

```typescript
// Configure dead letter queue for failed messages
await queueService.assertQueue('notification-queue', {
  durable: true,
  arguments: {
    'x-dead-letter-exchange': 'dlx-exchange',
    'x-dead-letter-routing-key': 'failed-notifications',
    'x-message-ttl': 3600000  // 1 hour TTL
  }
});

// Dead letter exchange setup
await queueService.assertExchange('dlx-exchange', 'direct', { durable: true });
await queueService.assertQueue('failed-notifications', { durable: true });
await queueService.bindQueue('failed-notifications', 'dlx-exchange', 'failed-notifications');
```

### Circuit Breaker Pattern

```typescript
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold = 5,
    private timeout = 60000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime < this.timeout) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'HALF_OPEN';
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

## Performance Optimization

### Connection Pooling

```typescript
// Redis connection pool for BullMQ
const redisPool = {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxConnections: 20,
  connectionIdleTimeout: 30000
};

// RabbitMQ channel pooling
class ChannelPool {
  private channels: Channel[] = [];
  private maxChannels = 10;
  
  async getChannel(): Promise<Channel> {
    if (this.channels.length > 0) {
      return this.channels.pop()!;
    }
    
    if (this.channels.length < this.maxChannels) {
      return await this.connection.createChannel();
    }
    
    // Wait for available channel
    return new Promise((resolve) => {
      const checkForChannel = () => {
        if (this.channels.length > 0) {
          resolve(this.channels.pop()!);
        } else {
          setTimeout(checkForChannel, 10);
        }
      };
      checkForChannel();
    });
  }
  
  releaseChannel(channel: Channel): void {
    this.channels.push(channel);
  }
}
```

### Batch Processing

```typescript
// Batch job processing for efficiency
export class BatchProcessor {
  private batch: any[] = [];
  private batchSize = 100;
  private flushInterval = 5000; // 5 seconds
  
  constructor(private processor: (items: any[]) => Promise<void>) {
    setInterval(() => this.flush(), this.flushInterval);
  }
  
  add(item: any): void {
    this.batch.push(item);
    
    if (this.batch.length >= this.batchSize) {
      this.flush();
    }
  }
  
  private async flush(): Promise<void> {
    if (this.batch.length === 0) return;
    
    const itemsToProcess = [...this.batch];
    this.batch = [];
    
    try {
      await this.processor(itemsToProcess);
    } catch (error) {
      logger.error('Batch processing failed:', error);
      // Could implement retry logic here
    }
  }
}
```

## Configuration Best Practices

### Environment-Specific Settings

```typescript
// Development
const devConfig = {
  redis: {
    maxRetriesPerRequest: 1,
    connectTimeout: 5000
  },
  queues: {
    removeOnComplete: 10,
    removeOnFail: 10
  }
};

// Production
const prodConfig = {
  redis: {
    maxRetriesPerRequest: 3,
    connectTimeout: 10000,
    enableAutoPipelining: true
  },
  queues: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 5
  }
};
```

### Monitoring and Alerting

```typescript
// Queue health checks
export async function checkQueueHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: Record<string, any>;
}> {
  const queues = ['evaluation', 'trigger', 'notification', 'bot', 'observation'];
  const results = {};
  let overallStatus = 'healthy';
  
  for (const queueName of queues) {
    const stats = await QueueMetrics.getQueueStats(queueName);
    
    // Check for concerning metrics
    if (stats.failed > 100 || stats.waiting > 1000) {
      overallStatus = 'degraded';
    }
    
    if (stats.errorRate > 0.1) { // 10% error rate
      overallStatus = 'unhealthy';
    }
    
    results[queueName] = stats;
  }
  
  return { status: overallStatus, details: results };
}
```
