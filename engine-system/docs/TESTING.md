# Testing Guide

This document provides comprehensive guidance for testing the Notification Engine System, including unit tests, integration tests, and end-to-end testing strategies.

## Table of Contents

- [Testing Overview](#testing-overview)
- [Test Setup](#test-setup)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [End-to-End Testing](#end-to-end-testing)
- [Load Testing](#load-testing)
- [Testing Best Practices](#testing-best-practices)
- [Continuous Integration](#continuous-integration)

## Testing Overview

The Notification Engine System uses a comprehensive testing approach with multiple testing layers:

- **Unit Tests**: Test individual functions, classes, and components in isolation
- **Integration Tests**: Test interactions between different components and external services
- **End-to-End Tests**: Test complete notification workflows from trigger to delivery
- **Load Tests**: Test system performance under various load conditions

### Testing Stack

- **Jest**: Primary testing framework
- **Supertest**: HTTP integration testing
- **Testcontainers**: Docker-based integration testing
- **Mock Libraries**: Service mocking and stubbing
- **Factory Libraries**: Test data generation

## Test Setup

### Installation

```bash
# Install testing dependencies
npm install --save-dev jest @types/jest supertest @types/supertest
npm install --save-dev testcontainers mysql2
npm install --save-dev jest-mock-extended factory.ts
```

### Configuration

```javascript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/generated/**/*'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 30000
};
```

### Test Environment Setup

```typescript
// tests/setup.ts
import { GenericContainer } from 'testcontainers';
import { PrismaClient } from '@prisma/client';

let prisma: PrismaClient;
let mysqlContainer: any;
let redisContainer: any;

beforeAll(async () => {
  // Start test databases
  mysqlContainer = await new GenericContainer('mysql:8.0')
    .withEnvironment({
      MYSQL_ROOT_PASSWORD: 'testpass',
      MYSQL_DATABASE: 'test_notifications'
    })
    .withExposedPorts(3306)
    .start();

  redisContainer = await new GenericContainer('redis:7')
    .withExposedPorts(6379)
    .start();

  // Setup Prisma client
  const DATABASE_URL = `mysql://root:testpass@localhost:${mysqlContainer.getMappedPort(3306)}/test_notifications`;
  process.env.DATABASE_URL = DATABASE_URL;
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = redisContainer.getMappedPort(6379).toString();

  prisma = new PrismaClient();
  await prisma.$connect();
});

afterAll(async () => {
  await prisma.$disconnect();
  await mysqlContainer.stop();
  await redisContainer.stop();
});
```

## Unit Testing

### Testing Services

```typescript
// tests/unit/services/trigger-evaluator.test.ts
import { TriggerEvaluator } from '../../../src/services/trigger-evaluator';
import { SiteState } from '../../../src/types/site';

describe('TriggerEvaluator', () => {
  let evaluator: TriggerEvaluator;

  beforeEach(() => {
    evaluator = new TriggerEvaluator();
  });

  describe('evaluateCondition', () => {
    test('should evaluate simple numeric conditions', () => {
      const condition = {
        field: 'sessions',
        operator: 'greater_than',
        value: 100
      };
      
      const siteState: SiteState = {
        idSite: 123,
        metrics: { sessions: 150 }
      };

      const result = evaluator.evaluateCondition(condition, siteState);
      expect(result).toBe(true);
    });

    test('should evaluate date-based conditions', () => {
      const condition = {
        field: 'last_login_date',
        operator: 'days_since',
        value: 7
      };
      
      const siteState: SiteState = {
        idSite: 123,
        lastLoginDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)
      };

      const result = evaluator.evaluateCondition(condition, siteState);
      expect(result).toBe(true);
    });

    test('should handle missing data gracefully', () => {
      const condition = {
        field: 'sessions',
        operator: 'greater_than',
        value: 100
      };
      
      const siteState: SiteState = {
        idSite: 123,
        metrics: {}
      };

      const result = evaluator.evaluateCondition(condition, siteState);
      expect(result).toBe(false);
    });
  });
});
```

### Testing Template Engine

```typescript
// tests/unit/template-engine.test.ts
import { TemplateEngine } from '../../src/template-engine';
import { mockFs } from 'jest-mock-extended';

jest.mock('fs', () => mockFs);

describe('TemplateEngine', () => {
  let templateEngine: TemplateEngine;

  beforeEach(() => {
    templateEngine = new TemplateEngine();
  });

  test('should render email template with data', async () => {
    const templateContent = `
      <h1>Hello {{userName}}</h1>
      <p>Your site {{siteName}} has {{sessions}} sessions</p>
    `;
    
    mockFs.readFileSync.mockReturnValue(templateContent);

    const result = await templateEngine.render('weekly-summary', 'email', {
      userName: 'John Doe',
      siteName: 'Test Site',
      sessions: 1234
    });

    expect(result.body).toContain('Hello John Doe');
    expect(result.body).toContain('Test Site has 1,234 sessions');
  });

  test('should use custom helpers', async () => {
    const templateContent = `
      <p>Revenue: {{formatCurrency revenue}}</p>
      <p>Date: {{formatDate date}}</p>
    `;
    
    mockFs.readFileSync.mockReturnValue(templateContent);

    const result = await templateEngine.render('template', 'email', {
      revenue: 1234.56,
      date: new Date('2023-01-01')
    });

    expect(result.body).toContain('Revenue: $1,234.56');
    expect(result.body).toContain('Date: Jan 1, 2023');
  });
});
```

### Testing Queue Jobs

```typescript
// tests/unit/jobs/notification-job.test.ts
import { NotificationJob } from '../../../src/jobs/notification/notification-job';
import { createMockContext } from '../../helpers/mock-context';

describe('NotificationJob', () => {
  let notificationJob: NotificationJob;
  let mockContext: any;

  beforeEach(() => {
    mockContext = createMockContext();
    notificationJob = new NotificationJob();
  });

  test('should process email notification', async () => {
    const jobData = {
      notificationId: 'test-123',
      channel: 'email',
      recipient: '<EMAIL>',
      templateId: 'weekly-summary',
      templateData: {
        siteName: 'Test Site',
        userName: 'John Doe'
      }
    };

    await notificationJob.process(jobData, mockContext);

    expect(mockContext.emailConnector.send).toHaveBeenCalledWith({
      to: '<EMAIL>',
      subject: expect.stringContaining('Weekly Summary'),
      html: expect.stringContaining('Test Site')
    });
  });

  test('should handle job failures gracefully', async () => {
    const jobData = {
      notificationId: 'test-123',
      channel: 'email',
      recipient: 'invalid-email',
      templateId: 'weekly-summary',
      templateData: {}
    };

    mockContext.emailConnector.send.mockRejectedValue(new Error('Invalid email'));

    await expect(notificationJob.process(jobData, mockContext)).rejects.toThrow('Invalid email');
    
    // Verify error was logged
    expect(mockContext.logger.error).toHaveBeenCalled();
  });
});
```

## Integration Testing

### API Integration Tests

```typescript
// tests/integration/api/trigger.test.ts
import request from 'supertest';
import app from '../../../src/app';
import { PrismaClient } from '@prisma/client';

describe('Trigger API', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.notification.deleteMany();
    await prisma.trigger.deleteMany();
  });

  describe('POST /trigger/evaluation', () => {
    test('should trigger evaluation for specified sites', async () => {
      const response = await request(app)
        .post('/trigger/evaluation')
        .send({
          sites: [123, 456],
          immediate: true
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.evaluationId).toBeDefined();
      expect(response.body.sitesQueued).toBe(2);
    });

    test('should validate request body', async () => {
      const response = await request(app)
        .post('/trigger/evaluation')
        .send({
          // Missing required fields
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.details).toContain('sites');
    });
  });

  describe('POST /real-time', () => {
    test('should process real-time notification', async () => {
      // Create test trigger
      const trigger = await prisma.trigger.create({
        data: {
          name: 'test-trigger',
          templateId: 'weekly-summary',
          channels: ['email'],
          conditions: [],
          isActive: true
        }
      });

      const response = await request(app)
        .post('/real-time')
        .send({
          idSite: 123,
          triggerName: 'test-trigger',
          templateData: {
            siteName: 'Test Site',
            userName: 'John Doe'
          },
          channels: ['email']
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.notificationId).toBeDefined();
    });
  });
});
```

### Database Integration Tests

```typescript
// tests/integration/database/models.test.ts
import { PrismaClient } from '@prisma/client';
import { createTestNotification, createTestTrigger } from '../../helpers/factories';

describe('Database Models', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('Notification Model', () => {
    test('should create notification with all relationships', async () => {
      const trigger = await createTestTrigger(prisma);
      const notification = await createTestNotification(prisma, {
        triggerId: trigger.id
      });

      const retrieved = await prisma.notification.findUnique({
        where: { id: notification.id },
        include: {
          trigger: true,
          attempts: true,
          auditLogs: true
        }
      });

      expect(retrieved).toBeDefined();
      expect(retrieved?.trigger.id).toBe(trigger.id);
      expect(retrieved?.status).toBe('PENDING');
    });

    test('should handle soft deletes', async () => {
      const notification = await createTestNotification(prisma);
      
      await prisma.notification.update({
        where: { id: notification.id },
        data: { deletedAt: new Date() }
      });

      const activeNotifications = await prisma.notification.findMany({
        where: { deletedAt: null }
      });

      expect(activeNotifications).not.toContainEqual(
        expect.objectContaining({ id: notification.id })
      );
    });
  });
});
```

## End-to-End Testing

### Complete Notification Flow

```typescript
// tests/e2e/notification-flow.test.ts
import { startTestServer } from '../helpers/test-server';
import { EmailTestHelper } from '../helpers/email-test-helper';
import { QueueTestHelper } from '../helpers/queue-test-helper';

describe('Complete Notification Flow', () => {
  let server: any;
  let emailHelper: EmailTestHelper;
  let queueHelper: QueueTestHelper;

  beforeAll(async () => {
    server = await startTestServer();
    emailHelper = new EmailTestHelper();
    queueHelper = new QueueTestHelper();
  });

  afterAll(async () => {
    await server.close();
  });

  test('should send weekly summary email', async () => {
    // Setup test data
    const siteId = 123;
    const userEmail = '<EMAIL>';
    
    // Trigger evaluation
    const response = await fetch(`${server.url}/trigger/evaluation`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sites: [siteId],
        immediate: true,
        triggerName: 'weekly-summary'
      })
    });

    expect(response.ok).toBe(true);

    // Wait for queue processing
    await queueHelper.waitForJobCompletion('evaluation-queue');
    await queueHelper.waitForJobCompletion('notification-queue');

    // Verify email was sent
    const sentEmails = await emailHelper.getSentEmails();
    const weeklyEmail = sentEmails.find(email => 
      email.to === userEmail && 
      email.subject.includes('Weekly Summary')
    );

    expect(weeklyEmail).toBeDefined();
    expect(weeklyEmail?.html).toContain('Test Site');
    expect(weeklyEmail?.html).toContain('metrics');
  });

  test('should handle Slack notifications', async () => {
    const slackHelper = new SlackTestHelper();
    
    // Trigger Slack notification
    const response = await fetch(`${server.url}/real-time`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        idSite: 123,
        triggerName: 'installation-complete',
        channels: ['slack'],
        templateData: {
          siteName: 'Test Site',
          userName: 'John Doe'
        }
      })
    });

    expect(response.ok).toBe(true);

    // Wait for processing
    await queueHelper.waitForJobCompletion('notification-queue');

    // Verify Slack message
    const slackMessages = await slackHelper.getPostedMessages();
    expect(slackMessages).toHaveLength(1);
    expect(slackMessages[0].text).toContain('Installation Complete');
  });
});
```

## Load Testing

### Queue Performance Tests

```typescript
// tests/load/queue-performance.test.ts
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { EvaluationQueue } from '../../src/jobs/evaluation/evaluation-queue';

describe('Queue Load Testing', () => {
  let evaluationQueue: EvaluationQueue;

  beforeAll(async () => {
    evaluationQueue = EvaluationQueue.getInstance();
  });

  test('should handle high volume of jobs', async () => {
    const jobCount = 1000;
    const jobs = [];

    // Add many jobs simultaneously
    for (let i = 0; i < jobCount; i++) {
      jobs.push(evaluationQueue.add(`test-job-${i}`, {
        siteId: i,
        triggerName: 'test-trigger'
      }));
    }

    const startTime = Date.now();
    await Promise.all(jobs);
    const addTime = Date.now() - startTime;

    // Wait for processing
    const processStartTime = Date.now();
    await evaluationQueue.whenCurrentJobsFinished();
    const processTime = Date.now() - processStartTime;

    // Performance assertions
    expect(addTime).toBeLessThan(5000); // Should add 1000 jobs in under 5 seconds
    expect(processTime).toBeLessThan(30000); // Should process in under 30 seconds

    console.log(`Added ${jobCount} jobs in ${addTime}ms`);
    console.log(`Processed ${jobCount} jobs in ${processTime}ms`);
  });
});
```

### API Load Tests

```typescript
// tests/load/api-performance.test.ts
import autocannon from 'autocannon';
import { startTestServer } from '../helpers/test-server';

describe('API Load Testing', () => {
  let server: any;

  beforeAll(async () => {
    server = await startTestServer();
  });

  afterAll(async () => {
    await server.close();
  });

  test('should handle concurrent trigger evaluations', async () => {
    const result = await autocannon({
      url: `${server.url}/trigger/evaluation`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sites: [123],
        immediate: true
      }),
      connections: 10,
      duration: 30 // 30 seconds
    });

    // Performance assertions
    expect(result.errors).toBe(0);
    expect(result.timeouts).toBe(0);
    expect(result.non2xx).toBe(0);
    expect(result.latency.average).toBeLessThan(1000); // Average response under 1 second

    console.log(`Requests per second: ${result.requests.average}`);
    console.log(`Average latency: ${result.latency.average}ms`);
  });
});
```

## Testing Best Practices

### Test Organization

```typescript
// tests/helpers/factories.ts
import { PrismaClient } from '@prisma/client';

export const createTestTrigger = async (prisma: PrismaClient, overrides = {}) => {
  return prisma.trigger.create({
    data: {
      name: 'test-trigger',
      templateId: 'weekly-summary',
      channels: ['email'],
      conditions: [],
      isActive: true,
      ...overrides
    }
  });
};

export const createTestNotification = async (prisma: PrismaClient, overrides = {}) => {
  return prisma.notification.create({
    data: {
      idSite: 123,
      templateId: 'weekly-summary',
      channel: 'email',
      recipient: '<EMAIL>',
      status: 'PENDING',
      templateData: {},
      ...overrides
    }
  });
};
```

### Mock Helpers

```typescript
// tests/helpers/mock-context.ts
export const createMockContext = () => ({
  emailConnector: {
    send: jest.fn().mockResolvedValue({ messageId: 'test-123' })
  },
  slackConnector: {
    postMessage: jest.fn().mockResolvedValue({ ok: true })
  },
  templateEngine: {
    render: jest.fn().mockResolvedValue({
      subject: 'Test Subject',
      body: 'Test Body'
    })
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
});
```

### Test Utilities

```typescript
// tests/helpers/test-server.ts
import app from '../../src/app';
import { Server } from 'http';

export const startTestServer = async (port = 0): Promise<{
  server: Server;
  url: string;
  close: () => Promise<void>;
}> => {
  return new Promise((resolve) => {
    const server = app.listen(port, () => {
      const address = server.address();
      const actualPort = typeof address === 'object' ? address?.port : port;
      
      resolve({
        server,
        url: `http://localhost:${actualPort}`,
        close: () => new Promise(resolve => server.close(resolve))
      });
    });
  });
};
```

## Continuous Integration

### GitHub Actions Configuration

```yaml
# .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test_notifications
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Generate Prisma Client
      run: npx prisma generate
    
    - name: Run database migrations
      run: npx prisma migrate deploy
      env:
        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications
    
    - name: Run unit tests
      run: npm run test:unit
      env:
        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications
        REDIS_HOST: localhost
        REDIS_PORT: 6379
    
    - name: Run integration tests
      run: npm run test:integration
      env:
        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications
        REDIS_HOST: localhost
        REDIS_PORT: 6379
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

### Test Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest tests/unit",
    "test:integration": "jest tests/integration",
    "test:e2e": "jest tests/e2e",
    "test:load": "jest tests/load",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

### Coverage Requirements

```javascript
// jest.config.js
module.exports = {
  // ... other configuration
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/services/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

This comprehensive testing guide provides the foundation for maintaining high code quality and system reliability in the Notification Engine System. Follow these patterns and adapt them to your specific testing needs.
