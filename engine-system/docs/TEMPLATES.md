# Template System Documentation

## Overview

The notification engine's template system provides a powerful, flexible framework for creating and managing multi-channel notification templates using Handlebars.js with custom layouts and helpers.

## Architecture

```
Template Engine
├── Template Registry      # Schema definitions and validation
├── Template Loader       # File system template loading
├── Layout System        # Base layouts for consistent branding
├── Custom Helpers       # Handlebars helper functions
├── Validation Layer     # Zod schema validation
└── Rendering Engine     # Template compilation and rendering
```

## Template Registry

The template registry defines available templates, their schemas, and validation rules for each channel.

### Registry Structure

```typescript
// src/template-registry.ts
export const templateRegistry = {
  'template-name': {
    email: {
      schema: z.object({
        // Email-specific validation schema
        subject: z.string().min(1),
        recipientName: z.string(),
        // ... other properties
      })
    },
    slack: {
      schema: z.object({
        // Slack-specific validation schema
        channelId: z.string(),
        blocks: z.array(z.object({
          type: z.string(),
          // ... block kit schema
        }))
      })
    },
    hubspot: {
      schema: z.object({
        // HubSpot CRM schema
        contactId: z.string(),
        properties: z.record(z.any())
      })
    }
  }
}
```

### Template Examples

#### Weekly Summary Template
```typescript
'weekly-summary-recurring': {
  email: {
    schema: z.object({
      siteName: z.string(),
      startDate: z.string(),
      endDate: z.string(),
      metrics: z.object({
        sessions: z.number(),
        revenue: z.number().optional(),
        pageviews: z.number(),
        conversionRate: z.number().optional()
      }),
      topPages: z.array(z.object({
        url: z.string(),
        views: z.number(),
        conversions: z.number().optional()
      })),
      userName: z.string()
    })
  },
  slack: {
    schema: z.object({
      siteName: z.string(),
      metrics: z.object({
        sessions: z.number(),
        revenue: z.number().optional()
      }),
      channelId: z.string()
    })
  }
}
```

## Template Engine Implementation

### Core Engine Class

```typescript
export class TemplateEngine {
  registry: typeof templateRegistry;
  templates: Record<string, string> = {};
  layouts: Record<string, string> = {};

  constructor(
    private readonly templatesPath: string,
    private readonly layoutsPath?: string,
    registry = templateRegistry
  ) {
    this.registry = registry;
  }

  async init(): Promise<void> {
    // Register custom helpers for layout functionality
    this.registerLayoutHelpers();

    // Load layout templates first
    if (this.layoutsPath) {
      await this.loadLayouts();
    }

    // Load regular templates
    await this.loadTemplates();
    
    // Validate registry completeness
    await this.checkTemplateRegistryCompleteness();
  }

  /**
   * Render a template with provided data
   */
  render(templateName: string, channel: string, data: any): string {
    const template = this.getCompiledTemplate(templateName, channel);
    return template(data);
  }

  /**
   * Validate data against template schema
   */
  validateData(templateName: string, channel: string, data: any): boolean {
    const schema = this.getSchema(templateName, channel);
    const result = schema.safeParse(data);
    
    if (!result.success) {
      throw new Error(`Template validation failed: ${result.error.message}`);
    }
    
    return true;
  }
}
```

### Layout System

The template engine supports layout inheritance using custom Handlebars helpers that mimic `handlebars-layouts` functionality.

#### Layout Helper Implementation

```typescript
private registerLayoutHelpers(): void {
  const blocks: Record<string, Record<string, string>> = {};

  // Helper to define content blocks
  Handlebars.registerHelper('content', function(this: any, name, options) {
    const data = Handlebars.createFrame(options.data);
    const blockName = name.trim();

    if (!blocks[data.layoutName]) {
      blocks[data.layoutName] = {};
    }

    // Store the block content
    blocks[data.layoutName][blockName] = options.fn(this);
    return null;
  });

  // Helper to retrieve content blocks
  Handlebars.registerHelper('block', function(this: any, name, options) {
    const data = options.data;
    const layoutName = data.layoutName;
    const blockName = name.trim();

    // Get block content or use default
    let content = '';
    if (blocks[layoutName] && blocks[layoutName][blockName]) {
      content = blocks[layoutName][blockName];
    } else if (options.fn) {
      content = options.fn(this);
    }

    return new Handlebars.SafeString(content);
  });

  // Helper to extend layouts
  Handlebars.registerHelper('extend', function(this: any, layoutName, options) {
    const data = Handlebars.createFrame(options.data);
    data.layoutName = layoutName;

    // Execute the child template to populate blocks
    options.fn(this, { data: data });

    // Render the layout with populated blocks
    const layout = this.layouts[layoutName];
    if (!layout) {
      throw new Error(`Layout '${layoutName}' not found`);
    }

    const template = Handlebars.compile(layout);
    return template(this, { data: data });
  });
}
```

## Template Structure

### Directory Organization

```
templates/
├── layouts/                    # Base layouts
│   ├── email.hbs              # Email HTML layout
│   ├── email-plain.hbs        # Plain text email layout
│   └── slack.hbs              # Slack message layout
├── new-templates/             # Current email templates
│   ├── credit-card.hbs
│   ├── weekly-summary-recurring.hbs
│   ├── login-case-study.hbs
│   └── ...
├── slack-templates/           # Slack-specific templates
│   ├── credit-card.hbs
│   ├── weekly-summary-recurring.hbs
│   └── ...
└── templating/               # Legacy templates
    └── ...
```

### Template File Examples

#### Email Layout (layouts/email.hbs)
```handlebars
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{#block "title"}}Notification{{/block}}</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            border-bottom: 3px solid #007bff;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .footer {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        {{#block "header"}}
            <h1>{{siteName}} Notification</h1>
        {{/block}}
    </div>
    
    <div class="content">
        {{#block "content"}}
            <p>Default content</p>
        {{/block}}
    </div>
    
    <div class="footer">
        {{#block "footer"}}
            <p>© 2025 Your Company. All rights reserved.</p>
            <p><a href="{{unsubscribeUrl}}">Unsubscribe</a></p>
        {{/block}}
    </div>
</body>
</html>
```

#### Email Template (new-templates/weekly-summary-recurring.hbs)
```handlebars
{{#extend "email"}}
  {{#content "title"}}Weekly Summary - {{siteName}}{{/content}}
  
  {{#content "header"}}
    <h1>📊 Weekly Summary for {{siteName}}</h1>
    <p>{{startDate}} - {{endDate}}</p>
  {{/content}}
  
  {{#content "content"}}
    <h2>Hey {{userName}}! 👋</h2>
    
    <p>Here's how {{siteName}} performed this week:</p>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>📈 Key Metrics</h3>
      <ul style="list-style: none; padding: 0;">
        <li><strong>Sessions:</strong> {{formatNumber metrics.sessions}}</li>
        <li><strong>Page Views:</strong> {{formatNumber metrics.pageviews}}</li>
        {{#if metrics.revenue}}
        <li><strong>Revenue:</strong> ${{formatCurrency metrics.revenue}}</li>
        {{/if}}
        {{#if metrics.conversionRate}}
        <li><strong>Conversion Rate:</strong> {{formatPercent metrics.conversionRate}}</li>
        {{/if}}
      </ul>
    </div>
    
    {{#if topPages}}
    <h3>🔥 Top Performing Pages</h3>
    <table style="width: 100%; border-collapse: collapse;">
      <thead>
        <tr style="background: #f8f9fa;">
          <th style="padding: 10px; text-align: left;">Page</th>
          <th style="padding: 10px; text-align: right;">Views</th>
          {{#if (hasProperty (first topPages) 'conversions')}}
          <th style="padding: 10px; text-align: right;">Conversions</th>
          {{/if}}
        </tr>
      </thead>
      <tbody>
        {{#each topPages}}
        <tr>
          <td style="padding: 10px; border-bottom: 1px solid #eee;">
            <a href="{{url}}" style="color: #007bff;">{{truncate url 50}}</a>
          </td>
          <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">
            {{formatNumber views}}
          </td>
          {{#if conversions}}
          <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">
            {{formatNumber conversions}}
          </td>
          {{/if}}
        </tr>
        {{/each}}
      </tbody>
    </table>
    {{/if}}
    
    <div style="margin: 30px 0; text-align: center;">
      <a href="{{dashboardUrl}}" class="btn">View Full Dashboard</a>
    </div>
    
    <p>Keep up the great work! 🚀</p>
  {{/content}}
{{/extend}}
```

#### Slack Template (slack-templates/weekly-summary-recurring.hbs)
```handlebars
{
  "blocks": [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "📊 Weekly Summary: {{siteName}}"
      }
    },
    {
      "type": "context",
      "elements": [
        {
          "type": "mrkdwn",
          "text": "{{startDate}} - {{endDate}}"
        }
      ]
    },
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "Hey {{userName}}! Here's how *{{siteName}}* performed this week:"
      }
    },
    {
      "type": "section",
      "fields": [
        {
          "type": "mrkdwn",
          "text": "*Sessions:*\n{{formatNumber metrics.sessions}}"
        },
        {
          "type": "mrkdwn",
          "text": "*Page Views:*\n{{formatNumber metrics.pageviews}}"
        }
        {{#if metrics.revenue}},
        {
          "type": "mrkdwn",
          "text": "*Revenue:*\n${{formatCurrency metrics.revenue}}"
        }
        {{/if}}
        {{#if metrics.conversionRate}},
        {
          "type": "mrkdwn",
          "text": "*Conversion Rate:*\n{{formatPercent metrics.conversionRate}}"
        }
        {{/if}}
      ]
    },
    {{#if topPages}}
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*🔥 Top Pages:*\n{{#each topPages}}• <{{url}}|{{truncate url 40}}> ({{formatNumber views}} views)\n{{/each}}"
      }
    },
    {{/if}}
    {
      "type": "actions",
      "elements": [
        {
          "type": "button",
          "text": {
            "type": "plain_text",
            "text": "View Dashboard"
          },
          "url": "{{dashboardUrl}}",
          "style": "primary"
        }
      ]
    }
  ]
}
```

## Custom Handlebars Helpers

### Formatting Helpers

```typescript
// Number formatting
Handlebars.registerHelper('formatNumber', function(value: number): string {
  if (typeof value !== 'number') return '0';
  return value.toLocaleString('en-US');
});

// Currency formatting
Handlebars.registerHelper('formatCurrency', function(value: number): string {
  if (typeof value !== 'number') return '0.00';
  return value.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
});

// Percentage formatting
Handlebars.registerHelper('formatPercent', function(value: number): string {
  if (typeof value !== 'number') return '0%';
  return (value * 100).toFixed(1) + '%';
});

// Date formatting
Handlebars.registerHelper('formatDate', function(date: string, format?: string): string {
  const d = new Date(date);
  if (format === 'short') {
    return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
  return d.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
});
```

### Utility Helpers

```typescript
// String truncation
Handlebars.registerHelper('truncate', function(str: string, length: number): string {
  if (!str || str.length <= length) return str;
  return str.substring(0, length) + '...';
});

// Conditional helpers
Handlebars.registerHelper('gt', function(a: number, b: number): boolean {
  return a > b;
});

Handlebars.registerHelper('lt', function(a: number, b: number): boolean {
  return a < b;
});

Handlebars.registerHelper('eq', function(a: any, b: any): boolean {
  return a === b;
});

// Array helpers
Handlebars.registerHelper('first', function(array: any[]): any {
  return array && array.length > 0 ? array[0] : null;
});

Handlebars.registerHelper('last', function(array: any[]): any {
  return array && array.length > 0 ? array[array.length - 1] : null;
});

Handlebars.registerHelper('hasProperty', function(obj: any, property: string): boolean {
  return obj && obj.hasOwnProperty(property);
});
```

### Business Logic Helpers

```typescript
// Site-specific helpers
Handlebars.registerHelper('getDashboardUrl', function(siteId: number): string {
  return `https://dashboard.yourapp.com/sites/${siteId}`;
});

Handlebars.registerHelper('getUnsubscribeUrl', function(siteId: number, email: string): string {
  const token = generateUnsubscribeToken(siteId, email);
  return `https://yourapp.com/unsubscribe?token=${token}`;
});

// Metric analysis helpers
Handlebars.registerHelper('getTrend', function(current: number, previous: number): string {
  if (!previous) return 'new';
  const change = ((current - previous) / previous) * 100;
  
  if (change > 10) return 'up-significant';
  if (change > 0) return 'up';
  if (change < -10) return 'down-significant';
  if (change < 0) return 'down';
  return 'stable';
});

Handlebars.registerHelper('getTrendIcon', function(trend: string): string {
  const icons = {
    'up-significant': '📈',
    'up': '⬆️',
    'down-significant': '📉',
    'down': '⬇️',
    'stable': '➡️',
    'new': '✨'
  };
  return icons[trend] || '➡️';
});
```

## Template Validation

### Schema Validation

```typescript
export function validateTemplateData(
  templateName: string,
  channel: string,
  data: any
): { valid: boolean; errors?: string[] } {
  const templateConfig = templateRegistry[templateName];
  
  if (!templateConfig) {
    return { valid: false, errors: [`Template '${templateName}' not found`] };
  }
  
  const channelConfig = templateConfig[channel];
  if (!channelConfig) {
    return { valid: false, errors: [`Channel '${channel}' not supported for template '${templateName}'`] };
  }
  
  const result = channelConfig.schema.safeParse(data);
  
  if (!result.success) {
    return {
      valid: false,
      errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
    };
  }
  
  return { valid: true };
}
```

### Template Completeness Check

```typescript
async checkTemplateRegistryCompleteness(): Promise<void> {
  const registryTemplates = Object.keys(this.registry);
  const missingTemplates: string[] = [];
  
  for (const templateName of registryTemplates) {
    const templateConfig = this.registry[templateName];
    
    for (const channel of Object.keys(templateConfig)) {
      const expectedFileName = `${templateName}.hbs`;
      const channelPath = channel === 'email' ? this.templatesPath : 
                         channel === 'slack' ? this.templatesPath.replace('new-templates', 'slack-templates') :
                         this.templatesPath;
      
      const templatePath = path.join(channelPath, expectedFileName);
      
      try {
        await fs.access(templatePath);
      } catch (error) {
        missingTemplates.push(`${templateName}/${channel} (${templatePath})`);
      }
    }
  }
  
  if (missingTemplates.length > 0) {
    console.warn('Missing template files:', missingTemplates);
  } else {
    console.log('✅ All registry templates have corresponding files');
  }
}
```

## Template Rendering Pipeline

### Rendering Process

```typescript
export async function renderTemplate(
  templateName: string,
  channel: string,
  data: any
): Promise<{ subject?: string; body: string }> {
  // 1. Validate input data
  const validation = validateTemplateData(templateName, channel, data);
  if (!validation.valid) {
    throw new Error(`Template validation failed: ${validation.errors?.join(', ')}`);
  }
  
  // 2. Get template engine instance
  const engine = await getTemplateEngine();
  
  // 3. Prepare template data with helpers
  const templateData = {
    ...data,
    // Add global helpers/data
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    // Add any computed values
    ...computeAdditionalData(data)
  };
  
  // 4. Render template
  try {
    const rendered = engine.render(templateName, channel, templateData);
    
    // 5. Parse rendered content for subject/body
    if (channel === 'email') {
      return parseEmailTemplate(rendered);
    } else {
      return { body: rendered };
    }
  } catch (error) {
    logger.error('Template rendering failed', {
      templateName,
      channel,
      error: error.message
    });
    throw error;
  }
}

function parseEmailTemplate(rendered: string): { subject?: string; body: string } {
  // Extract subject from title tag if present
  const titleMatch = rendered.match(/<title>(.*?)<\/title>/);
  const subject = titleMatch ? titleMatch[1] : undefined;
  
  return { subject, body: rendered };
}
```

### Template Caching

```typescript
class TemplateCache {
  private cache = new Map<string, HandlebarsTemplateDelegate>();
  private maxAge = 5 * 60 * 1000; // 5 minutes
  private timestamps = new Map<string, number>();
  
  get(key: string): HandlebarsTemplateDelegate | null {
    const template = this.cache.get(key);
    const timestamp = this.timestamps.get(key);
    
    if (!template || !timestamp) return null;
    
    // Check if cache entry is expired
    if (Date.now() - timestamp > this.maxAge) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      return null;
    }
    
    return template;
  }
  
  set(key: string, template: HandlebarsTemplateDelegate): void {
    this.cache.set(key, template);
    this.timestamps.set(key, Date.now());
  }
  
  clear(): void {
    this.cache.clear();
    this.timestamps.clear();
  }
}
```

## Template Development Workflow

### Development Setup

1. **Create template files**
   ```bash
   # Email template
   touch src/new-templates/my-new-template.hbs
   
   # Slack template
   touch src/slack-templates/my-new-template.hbs
   ```

2. **Register in template registry**
   ```typescript
   // Add to src/template-registry.ts
   'my-new-template': {
     email: {
       schema: z.object({
         userName: z.string(),
         // ... other properties
       })
     },
     slack: {
       schema: z.object({
         // ... slack properties
       })
     }
   }
   ```

3. **Test template rendering**
   ```typescript
   // Development testing
   const testData = {
     userName: 'John Doe',
     // ... test data
   };
   
   const rendered = await renderTemplate('my-new-template', 'email', testData);
   console.log(rendered);
   ```

### Template Testing

```typescript
// Template test suite
describe('Template Rendering', () => {
  test('should render weekly summary email', async () => {
    const testData = {
      siteName: 'Test Site',
      userName: 'John Doe',
      startDate: '2025-06-01',
      endDate: '2025-06-08',
      metrics: {
        sessions: 1234,
        pageviews: 5678,
        revenue: 1234.56
      },
      topPages: [
        { url: '/home', views: 100, conversions: 5 },
        { url: '/product', views: 80, conversions: 8 }
      ]
    };
    
    const result = await renderTemplate('weekly-summary-recurring', 'email', testData);
    
    expect(result.body).toContain('Test Site');
    expect(result.body).toContain('John Doe');
    expect(result.body).toContain('1,234');
    expect(result.subject).toContain('Weekly Summary');
  });
  
  test('should validate template data', () => {
    const invalidData = { /* missing required fields */ };
    
    const validation = validateTemplateData('weekly-summary-recurring', 'email', invalidData);
    
    expect(validation.valid).toBe(false);
    expect(validation.errors).toBeDefined();
  });
});
```

## Performance Considerations

### Template Compilation

- Templates are compiled once and cached in memory
- Hot reloading in development mode
- Precompilation for production deployments

### Memory Management

- Template cache with TTL to prevent memory leaks
- Lazy loading of templates
- Cleanup of unused templates

### Optimization Tips

1. **Minimize template complexity** - Keep logic simple in templates
2. **Use helpers** - Move complex logic to Handlebars helpers
3. **Cache compiled templates** - Avoid recompilation
4. **Optimize images** - Use CDN for template assets
5. **Minify output** - Remove unnecessary whitespace in production
