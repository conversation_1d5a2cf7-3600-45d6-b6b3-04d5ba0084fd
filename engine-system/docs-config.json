{"title": "Notification Engine Documentation", "description": "Comprehensive documentation for the Notification Engine System", "version": "1.0.0", "baseUrl": "/docs", "structure": {"sections": [{"id": "overview", "title": "Overview", "description": "Introduction and getting started guide", "order": 1, "files": [{"id": "readme", "title": "Introduction", "file": "README.md", "description": "System overview, architecture, and getting started", "order": 1}]}, {"id": "api", "title": "API Reference", "description": "Complete API documentation with examples", "order": 2, "files": [{"id": "api", "title": "REST API", "file": "docs/API.md", "description": "Complete REST API documentation with endpoints and examples", "order": 1}]}, {"id": "architecture", "title": "Architecture & Design", "description": "System architecture and design documentation", "order": 3, "files": [{"id": "database", "title": "Database Schema", "file": "docs/DATABASE.md", "description": "Complete database schema documentation with models and relationships", "order": 1}, {"id": "queues", "title": "Queue System", "file": "docs/QUEUES.md", "description": "Dual-queue architecture with BullMQ and RabbitMQ implementation", "order": 2}, {"id": "templates", "title": "Template Engine", "file": "docs/TEMPLATES.md", "description": "Handlebars templating system with custom helpers and multi-channel support", "order": 3}]}, {"id": "development", "title": "Development Guide", "description": "Development, testing, and deployment guides", "order": 4, "files": [{"id": "testing", "title": "Testing Guide", "file": "docs/TESTING.md", "description": "Comprehensive testing strategies including unit, integration, and load testing", "order": 1}, {"id": "deployment", "title": "Deployment Guide", "file": "docs/DEPLOYMENT.md", "description": "Production deployment instructions for Docker, AWS, and Kubernetes", "order": 2}]}, {"id": "troubleshooting", "title": "Troubleshooting", "description": "Common issues and solutions", "order": 5, "files": [{"id": "troubleshooting", "title": "Troubleshooting Guide", "file": "docs/TROUBLESHOOTING.md", "description": "Common issues, debugging tips, and solutions", "order": 1}]}]}, "navigation": {"showSectionNumbers": true, "showFileNumbers": true, "generateToc": true, "maxTocDepth": 3}, "output": {"formats": ["merged", "site"], "mergedFile": "DOCUMENTATION.md", "siteDir": "docs-site", "includeSourceLinks": true, "includeLastModified": true}, "styling": {"theme": "default", "codeHighlighting": true, "responsiveDesign": true}}