FROM node:latest as builder

WORKDIR /build

COPY package*.json  .

RUN npm  install

COPY . .

COPY tsconfig.json .

# Generate Prisma Client
COPY prisma ./prisma

RUN npx prisma generate

RUN npm run build


FROM builder as dev

CMD ["npm", "run" ,"dev"]



FROM node:23-bullseye-slim as prod

#ENV NODE_ENV production
WORKDIR /app

COPY   --from=builder /build/package*.json  .
COPY  --from=builder /build/dist dist/
COPY --from=builder /build/prisma prisma/
COPY  --from=builder /build/.env  .

RUN npm install -g pm2

RUN npm install --only=prod
RUN npx prisma generate

RUN mv src/generated dist/generated

CMD ["pm2-runtime" , "dist/server.js"]