
/* Simple Documentation Styles */
* { margin: 0; padding: 0; box-sizing: border-box; }

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

header {
  background: #2c3e50;
  color: white;
  padding: 2rem;
  text-align: center;
}

header h1 { margin: 0; font-size: 2rem; }
header h1 a { color: white; text-decoration: none; }
header p { margin: 1rem 0 0 0; opacity: 0.9; }

.breadcrumb {
  margin-top: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.breadcrumb a { color: #ecf0f1; text-decoration: none; }

main { padding: 2rem; }

.welcome { text-align: center; max-width: 800px; margin: 0 auto; }
.welcome h2 { color: #2c3e50; margin-bottom: 1rem; }

.sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.section-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: left;
}

.section-card h3 { margin: 0 0 0.5rem 0; color: #2c3e50; }
.section-card h3 a { color: #2c3e50; text-decoration: none; }
.section-card h3 a:hover { color: #3498db; }
.file-count { font-size: 0.8rem; color: #7f8c8d; margin-top: 0.5rem; }

article { max-width: 800px; }
h1, h2, h3, h4, h5, h6 { margin-top: 2rem; margin-bottom: 1rem; color: #2c3e50; }
h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; border-bottom: 2px solid #ecf0f1; padding-bottom: 0.5rem; }
h3 { font-size: 1.5rem; }
p { margin-bottom: 1rem; }

code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
}

pre {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1rem 0;
}

pre code { background: none; padding: 0; color: inherit; }

@media (max-width: 768px) {
  .sections { grid-template-columns: 1fr; }
  header { padding: 1rem; }
  main { padding: 1rem; }
}
