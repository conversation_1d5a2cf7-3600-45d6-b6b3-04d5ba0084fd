<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>REST API - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › API Reference › REST API
            </nav>
        </header>

        <main>
            <article>
                <h1>REST API</h1>
                <h1>API Documentation</h1></p><p><h2>Overview</h2></p><p>The Notification Engine provides a RESTful API for managing notifications, triggers, templates, and integrations. The API is built with Express.js and includes comprehensive middleware for validation, authentication, and error handling.</p><p><h2>Base Configuration</h2></p><p>- <strong>Base URL</strong>: <code>http://localhost:3000</code> (development)<br>- <strong>Content-Type</strong>: <code>application/json</code><br>- <strong>Authentication</strong>: Bearer token (when implemented)</p><p><h2>Core Endpoints</h2></p><p><h3>Health Check</h3></p><p>#### GET /up<br>Check the health status of the application.</p><p><strong>Response:</strong><br><pre><code>{<br>  "status": "ok",<br>  "timestamp": "2025-06-09T10:30:00.000Z",<br>  "uptime": 1800,<br>  "version": "1.0.0"<br>}<br></code></pre></p><p><strong>Status Codes:</strong><br>- <code>200</code> - Service is healthy<br>- <code>503</code> - Service is unhealthy</p><p>---</p><p><h3>Trigger Management</h3></p><p>#### POST /trigger/evaluation<br>Trigger evaluation of sites for notification conditions.</p><p><strong>Request Body:</strong><br><pre><code>{<br>  "sites": [123, 456, 789],<br>  "triggerTypes": ["trial", "engagement", "revenue"],<br>  "immediate": true,<br>  "metadata": {<br>    "source": "manual",<br>    "requestedBy": "admin"<br>  }<br>}<br></code></pre></p><p><strong>Parameters:</strong><br>- <code>sites</code> (required): Array of site IDs to evaluate<br>- <code>triggerTypes</code> (optional): Specific trigger types to check<br>- <code>immediate</code> (optional): Skip scheduling and process immediately<br>- <code>metadata</code> (optional): Additional context information</p><p><strong>Response:</strong><br><pre><code>{<br>  "success": true,<br>  "evaluationId": "eval_123456789",<br>  "sitesQueued": 3,<br>  "estimatedProcessingTime": "2-5 minutes"<br>}<br></code></pre></p><p><strong>Status Codes:</strong><br>- <code>200</code> - Evaluation queued successfully<br>- <code>400</code> - Invalid request parameters<br>- <code>500</code> - Internal server error</p><p>---</p><p><h3>Real-time Notifications</h3></p><p>#### POST /real-time<br>Send immediate notifications without trigger evaluation.</p><p><strong>Request Body:</strong><br><pre><code>{<br>  "idSite": 123,<br>  "triggerName": "login-reminder",<br>  "templateData": {<br>    "userName": "John Doe",<br>    "daysSinceLogin": 7,<br>    "loginUrl": "https://app.example.com/login"<br>  },<br>  "channels": ["email", "slack"],<br>  "priority": "high",<br>  "scheduledFor": "2025-06-09T15:30:00.000Z"<br>}<br></code></pre></p><p><strong>Parameters:</strong><br>- <code>idSite</code> (required): Site identifier<br>- <code>triggerName</code> (required): Name of trigger/template to use<br>- <code>templateData</code> (required): Data for template rendering<br>- <code>channels</code> (optional): Specific channels to send to (default: all enabled)<br>- <code>priority</code> (optional): Message priority (low, normal, high)<br>- <code>scheduledFor</code> (optional): Schedule for future delivery</p><p><strong>Response:</strong><br><pre><code>{<br>  "success": true,<br>  "notificationId": "notif_987654321",<br>  "status": "queued",<br>  "channels": {<br>    "email": {<br>      "status": "queued",<br>      "estimatedDelivery": "2025-06-09T10:35:00.000Z"<br>    },<br>    "slack": {<br>      "status": "queued",<br>      "estimatedDelivery": "2025-06-09T10:31:00.000Z"<br>    }<br>  }<br>}<br></code></pre></p><p><strong>Status Codes:</strong><br>- <code>200</code> - Notification queued successfully<br>- <code>400</code> - Invalid request parameters<br>- <code>422</code> - Template validation failed<br>- <code>500</code> - Internal server error</p><p>---</p><p><h3>Slack Integration</h3></p><p>#### POST /slack/auth<br>Handle Slack OAuth authentication flow.</p><p><strong>Request Body:</strong><br><pre><code>{<br>  "code": "oauth_code_from_slack",<br>  "state": "random_state_string",<br>  "idSite": 123<br>}<br></code></pre></p><p><strong>Response:</strong><br><pre><code>{<br>  "success": true,<br>  "teamName": "Example Team",<br>  "channelId": "C1234567890",<br>  "channelName": "#notifications",<br>  "accessToken": "encrypted_token_reference"<br>}<br></code></pre></p><p>#### POST /slack/message<br>Send a direct Slack message (for testing/admin use).</p><p><strong>Request Body:</strong><br><pre><code>{<br>  "idSite": 123,<br>  "message": "Test notification message",<br>  "subject": "Test Subject",<br>  "channel": "#general"<br>}<br></code></pre></p><p>---</p><p><h2>Admin Endpoints</h2></p><p><h3>Queue Monitoring</h3></p><p>#### GET /admin/queues<br>Access the Bull Board dashboard for queue monitoring.</p><p><strong>Response:</strong> HTML dashboard interface</p><p><strong>Features:</strong><br>- Real-time queue statistics<br>- Job details and logs<br>- Manual job management<br>- Performance metrics</p><p>---</p><p><h2>Error Handling</h2></p><p><h3>Standard Error Response</h3></p><p><pre><code>{<br>  "error": {<br>    "code": "VALIDATION_ERROR",<br>    "message": "Request validation failed",<br>    "details": {<br>      "field": "sites",<br>      "issue": "Array must contain at least one site ID"<br>    },<br>    "timestamp": "2025-06-09T10:30:00.000Z",<br>    "requestId": "req_123456789"<br>  }<br>}<br></code></pre></p><p><h3>Error Codes</h3></p><p>| Code                        | Description                     | HTTP Status |<br>| --------------------------- | ------------------------------- | ----------- |<br>| <code>VALIDATION_ERROR</code>          | Request validation failed       | 400         |<br>| <code>TEMPLATE_NOT_FOUND</code>        | Template does not exist         | 404         |<br>| <code>TEMPLATE_VALIDATION_ERROR</code> | Template data validation failed | 422         |<br>| <code>SITE_NOT_FOUND</code>            | Site ID not found               | 404         |<br>| <code>RATE_LIMIT_EXCEEDED</code>       | Too many requests               | 429         |<br>| <code>QUEUE_UNAVAILABLE</code>         | Queue service unavailable       | 503         |<br>| <code>INTERNAL_ERROR</code>            | Unexpected server error         | 500         |</p><p>---</p><p><h2>Request/Response Examples</h2></p><p><h3>Trigger Weekly Summary</h3></p><p><pre><code>POST /trigger/evaluation<br>Content-Type: application/json</p><p>{<br>  "sites": [123],<br>  "triggerTypes": ["weekly-summary"],<br>  "immediate": true<br>}<br></code></pre></p><p><pre><code>{<br>  "success": true,<br>  "evaluationId": "eval_202506091030_123",<br>  "sitesQueued": 1,<br>  "estimatedProcessingTime": "1-2 minutes"<br>}<br></code></pre></p><p><h3>Send Login Reminder</h3></p><p><pre><code>POST /real-time<br>Content-Type: application/json</p><p>{<br>  "idSite": 123,<br>  "triggerName": "login-case-study",<br>  "templateData": {<br>    "userName": "Sarah Johnson",<br>    "siteName": "E-commerce Store",<br>    "daysSinceLogin": 14,<br>    "caseStudyUrl": "https://example.com/case-study",<br>    "loginUrl": "https://app.example.com/login"<br>  },<br>  "channels": ["email"]<br>}<br></code></pre></p><p><pre><code>{<br>  "success": true,<br>  "notificationId": "notif_20250609103045_123",<br>  "status": "queued",<br>  "channels": {<br>    "email": {<br>      "status": "queued",<br>      "recipient": "<EMAIL>",<br>      "estimatedDelivery": "2025-06-09T10:32:00.000Z"<br>    }<br>  }<br>}<br></code></pre></p><p><h3>Send Team Invitation Reminder</h3></p><p><pre><code>POST /real-time<br>Content-Type: application/json</p><p>{<br>  "idSite": 456,<br>  "triggerName": "team-not-invited",<br>  "templateData": {<br>    "userName": "Mike Chen",<br>    "siteName": "SaaS Dashboard",<br>    "inviteTeamUrl": "https://app.example.com/team/invite",<br>    "benefitsText": "Collaborating with your team can increase productivity by 40%"<br>  },<br>  "channels": ["email", "slack"]<br>}<br></code></pre></p><p>---</p><p><h2>Middleware</h2></p><p><h3>Request Validation</h3></p><p>All endpoints include request validation middleware using Zod schemas:</p><p><pre><code>// Example validation schema<br>const triggerEvaluationSchema = z.object({<br>  sites: z.array(z.number().positive()).min(1),<br>  triggerTypes: z.array(z.string()).optional(),<br>  immediate: z.boolean().optional(),<br>  metadata: z.record(z.any()).optional()<br>});<br></code></pre></p><p><h3>Error Handling</h3></p><p>Global error handling middleware catches and formats all errors:</p><p><pre><code>app.use((error: Error, req: Request, res: Response, next: NextFunction) => {<br>  const errorResponse = {<br>    error: {<br>      code: error.name || 'INTERNAL_ERROR',<br>      message: error.message,<br>      timestamp: new Date().toISOString(),<br>      requestId: req.headers['x-request-id'] || generateRequestId()<br>    }<br>  };</p><p>  if (error instanceof ValidationError) {<br>    res.status(400).json(errorResponse);<br>  } else if (error instanceof NotFoundError) {<br>    res.status(404).json(errorResponse);<br>  } else {<br>    res.status(500).json(errorResponse);<br>  }<br>});<br></code></pre></p><p><h3>Database Connection Management</h3></p><p>Automatic Prisma connection management:</p><p><pre><code>export const disconnectPrismaV1 = async (<br>  req: Request,<br>  res: Response,<br>  next: NextFunction<br>) => {<br>  // Skip for monitoring endpoints<br>  if (EXCLUDED_PATHS.some(path => req.originalUrl.startsWith(path))) {<br>    return next();<br>  }</p><p>  let isCleanUp = false;<br>  const cleanup = async (event: string) => {<br>    if (isCleanUp) return;</p><p>    try {<br>      isCleanUp = true;<br>      await prisma.$disconnect();<br>      console.log(<code>Prisma disconnect activated in ${event}</code>);<br>    } catch (err) {<br>      console.error(<code>Prisma disconnect failed in ${event}</code>);<br>    }<br>  };</p><p>  res.on('close', () => cleanup('close'));<br>  res.on('finish', () => cleanup('finish'));</p><p>  next();<br>};<br></code></pre></p><p>---</p><p><h2>Authentication & Security</h2></p><p><h3>Security Headers</h3></p><p><pre><code>app.use(helmet({<br>  contentSecurityPolicy: {<br>    directives: {<br>      defaultSrc: ["'self'"],<br>      styleSrc: ["'self'", "'unsafe-inline'"],<br>      scriptSrc: ["'self'"],<br>      imgSrc: ["'self'", "data:", "https:"]<br>    }<br>  },<br>  hsts: {<br>    maxAge: 31536000,<br>    includeSubDomains: true,<br>    preload: true<br>  }<br>}));<br></code></pre></p><p><h3>CORS Configuration</h3></p><p><pre><code>app.use(cors({<br>  origin: process.env.NODE_ENV === 'production'<br>    ? ['https://yourapp.com', 'https://dashboard.yourapp.com']<br>    : true,<br>  credentials: true,<br>  methods: ['GET', 'POST', 'PUT', 'DELETE'],<br>  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']<br>}));<br></code></pre></p><p><h3>Rate Limiting (Recommended)</h3></p><p><pre><code>import rateLimit from 'express-rate-limit';</p><p>const limiter = rateLimit({<br>  windowMs: 15 <em> 60 </em> 1000, // 15 minutes<br>  max: 100, // Limit each IP to 100 requests per windowMs<br>  message: {<br>    error: {<br>      code: 'RATE_LIMIT_EXCEEDED',<br>      message: 'Too many requests from this IP'<br>    }<br>  }<br>});</p><p>app.use('/api/', limiter);<br></code></pre></p><p>---</p><p><h2>WebSocket Events (Future Enhancement)</h2></p><p><h3>Real-time Updates</h3></p><p>For future implementation of real-time notifications:</p><p><pre><code>// WebSocket event types<br>interface NotificationEvent {<br>  type: 'notification.sent' | 'notification.failed' | 'notification.delivered';<br>  data: {<br>    notificationId: string;<br>    idSite: number;<br>    channel: string;<br>    status: string;<br>    timestamp: string;<br>  };<br>}</p><p>// Client subscription<br>ws.send(JSON.stringify({<br>  type: 'subscribe',<br>  channels: ['notifications', 'triggers'],<br>  siteId: 123<br>}));<br></code></pre></p><p>---</p><p><h2>API Client Examples</h2></p><p><h3>JavaScript/Node.js</h3></p><p><pre><code>class NotificationAPI {<br>  constructor(baseUrl, apiKey) {<br>    this.baseUrl = baseUrl;<br>    this.apiKey = apiKey;<br>  }</p><p>  async triggerEvaluation(sites, options = {}) {<br>    const response = await fetch(<code>${this.baseUrl}/trigger/evaluation</code>, {<br>      method: 'POST',<br>      headers: {<br>        'Content-Type': 'application/json',<br>        'Authorization': <code>Bearer ${this.apiKey}</code><br>      },<br>      body: JSON.stringify({<br>        sites,<br>        ...options<br>      })<br>    });</p><p>    if (!response.ok) {<br>      throw new Error(<code>API error: ${response.status}</code>);<br>    }</p><p>    return response.json();<br>  }</p><p>  async sendRealTimeNotification(notification) {<br>    const response = await fetch(<code>${this.baseUrl}/real-time</code>, {<br>      method: 'POST',<br>      headers: {<br>        'Content-Type': 'application/json',<br>        'Authorization': <code>Bearer ${this.apiKey}</code><br>      },<br>      body: JSON.stringify(notification)<br>    });</p><p>    return response.json();<br>  }<br>}</p><p>// Usage<br>const api = new NotificationAPI('http://localhost:3000', 'your-api-key');</p><p>await api.triggerEvaluation([123, 456], {<br>  triggerTypes: ['weekly-summary'],<br>  immediate: true<br>});</p><p>await api.sendRealTimeNotification({<br>  idSite: 123,<br>  triggerName: 'login-reminder',<br>  templateData: {<br>    userName: 'John Doe',<br>    daysSinceLogin: 7<br>  }<br>});<br></code></pre></p><p><h3>Python</h3></p><p><pre><code>import requests<br>import json</p><p>class NotificationAPI:<br>    def __init__(self, base_url, api_key):<br>        self.base_url = base_url<br>        self.api_key = api_key<br>        self.headers = {<br>            'Content-Type': 'application/json',<br>            'Authorization': f'Bearer {api_key}'<br>        }</p><p>    def trigger_evaluation(self, sites, <em></em>options):<br>        data = {'sites': sites, <em></em>options}<br>        response = requests.post(<br>            f'{self.base_url}/trigger/evaluation',<br>            headers=self.headers,<br>            json=data<br>        )<br>        response.raise_for_status()<br>        return response.json()</p><p>    def send_real_time_notification(self, notification):<br>        response = requests.post(<br>            f'{self.base_url}/real-time',<br>            headers=self.headers,<br>            json=notification<br>        )<br>        response.raise_for_status()<br>        return response.json()</p><p><h1>Usage</h1><br>api = NotificationAPI('http://localhost:3000', 'your-api-key')</p><p>api.trigger_evaluation([123, 456], trigger_types=['weekly-summary'])</p><p>api.send_real_time_notification({<br>    'idSite': 123,<br>    'triggerName': 'login-reminder',<br>    'templateData': {<br>        'userName': 'John Doe',<br>        'daysSinceLogin': 7<br>    }<br>})<br></code></pre></p><p>---</p><p><h2>Testing</h2></p><p><h3>Unit Tests</h3></p><p><pre><code>describe('API Endpoints', () => {<br>  test('POST /trigger/evaluation', async () => {<br>    const response = await request(app)<br>      .post('/trigger/evaluation')<br>      .send({<br>        sites: [123],<br>        immediate: true<br>      })<br>      .expect(200);</p><p>    expect(response.body.success).toBe(true);<br>    expect(response.body.evaluationId).toBeDefined();<br>  });</p><p>  test('POST /real-time validation', async () => {<br>    const response = await request(app)<br>      .post('/real-time')<br>      .send({<br>        // Missing required fields<br>      })<br>      .expect(400);</p><p>    expect(response.body.error.code).toBe('VALIDATION_ERROR');<br>  });<br>});<br></code></pre></p><p><h3>Integration Tests</h3></p><p><pre><code>describe('Notification Flow', () => {<br>  test('should send email notification', async () => {<br>    const mockEmailSend = jest.fn();<br>    jest.spyOn(emailConnector, 'send').mockImplementation(mockEmailSend);</p><p>    await request(app)<br>      .post('/real-time')<br>      .send({<br>        idSite: 123,<br>        triggerName: 'weekly-summary',<br>        templateData: { /<em> test data </em>/ },<br>        channels: ['email']<br>      })<br>      .expect(200);</p><p>    // Verify queue processing<br>    await new Promise(resolve => setTimeout(resolve, 1000));</p><p>    expect(mockEmailSend).toHaveBeenCalledWith({<br>      recipient: '<EMAIL>',<br>      subject: expect.stringContaining('Weekly Summary'),<br>      content: expect.stringContaining('Test Site')<br>    });<br>  });<br>});<br></code></pre></p><p>---</p><p><h2>Performance Considerations</h2></p><p><h3>Response Time Targets</h3></p><p>- <strong>Health checks</strong>: < 100ms<br>- <strong>Trigger evaluation</strong>: < 500ms (queueing)<br>- <strong>Real-time notifications</strong>: < 1s (queueing)<br>- <strong>Slack auth</strong>: < 2s</p><p><h3>Caching Strategy</h3></p><p><pre><code>// Redis caching for frequent lookups<br>const cache = new Redis(process.env.REDIS_URL);</p><p>app.use('/api/sites/:id', async (req, res, next) => {<br>  const cached = await cache.get(<code>site:${req.params.id}</code>);<br>  if (cached) {<br>    res.json(JSON.parse(cached));<br>    return;<br>  }<br>  next();<br>});<br></code></pre></p><p><h3>Monitoring</h3></p><p>- Request/response time tracking<br>- Error rate monitoring<br>- Queue depth alerts<br>- Database connection pool monitoring<br>
            </article>
        </main>
    </div>
</body>
</html>