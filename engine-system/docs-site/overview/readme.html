<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Introduction - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Overview › Introduction
            </nav>
        </header>

        <main>
            <article>
                <h1>Introduction</h1>
                <h1>Notification Engine System</h1></p><p>A comprehensive TypeScript-based notification engine designed to handle multi-channel notifications, trigger evaluations, and template management for SaaS applications.</p><p><h2>Table of Contents</h2></p><p>- <a href="#overview">Overview</a><br>- <a href="#architecture">Architecture</a><br>- <a href="#technology-stack">Technology Stack</a><br>- <a href="#getting-started">Getting Started</a><br>- <a href="#database-schema">Database Schema</a><br>- <a href="#queue-system">Queue System</a><br>- <a href="#template-engine">Template Engine</a><br>- <a href="#api-documentation">API Documentation</a><br>- <a href="#environment-configuration">Environment Configuration</a><br>- <a href="#development-guide">Development Guide</a><br>- <a href="#deployment">Deployment</a><br>- <a href="#documentation">Documentation</a></p><p><h2>Overview</h2></p><p>The Notification Engine System is a robust, scalable solution for managing notifications across multiple channels including email, Slack, and HubSpot. It features:</p><p>- <strong>Multi-channel notifications</strong> (Email, Slack, HubSpot CRM)<br>- <strong>Template-based messaging</strong> with Handlebars templating<br>- <strong>Trigger-based automation</strong> with configurable conditions<br>- <strong>Queue-based processing</strong> using BullMQ and RabbitMQ<br>- <strong>Comprehensive audit logging</strong> and notification tracking<br>- <strong>Site-specific customization</strong> and preferences<br>- <strong>Real-time and scheduled notifications</strong></p><p><h2>Architecture</h2></p><p><h3>High-Level Architecture</h3></p><p><pre><code>┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐<br>│   API Gateway   │    │  Queue System   │    │   Connectors    │<br>│                 │    │                 │    │                 │<br>│ • REST APIs     │───▶│ • BullMQ/Redis  │───▶│ • Email (SMTP)  │<br>│ • Webhooks      │    │ • RabbitMQ      │    │ • Slack API     │<br>│ • Admin Panel   │    │ • Job Queues    │    │ • HubSpot CRM   │<br>└─────────────────┘    └─────────────────┘    └─────────────────┘<br>         │                       │                       │<br>         ▼                       ▼                       ▼<br>┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐<br>│   Database      │    │ Template Engine │    │   External      │<br>│                 │    │                 │    │   Services      │<br>│ • MySQL/Prisma  │    │ • Handlebars    │    │                 │<br>│ • Notifications │    │ • Email/Slack   │    │ • Site APIs     │<br>│ • Templates     │    │ • Validation    │    │ • Analytics     │<br>│ • Triggers      │    │ • Rendering     │    │ • Integrations  │<br>└─────────────────┘    └─────────────────┘    └─────────────────┘<br></code></pre></p><p><h3>Core Components</h3></p><p>1. <strong>API Layer</strong> - Express.js REST API with middleware for authentication and validation<br>2. <strong>Queue System</strong> - Multi-queue architecture for job processing and message routing<br>3. <strong>Template Engine</strong> - Handlebars-based templating with layout support<br>4. <strong>Database Layer</strong> - Prisma ORM with MySQL for data persistence<br>5. <strong>Connectors</strong> - Channel-specific integrations for message delivery<br>6. <strong>Job Processors</strong> - Background workers for notification processing</p><p><h2>Technology Stack</h2></p><p><h3>Core Technologies</h3><br>- <strong>Runtime</strong>: Node.js with TypeScript<br>- <strong>Framework</strong>: Express.js<br>- <strong>Database</strong>: MySQL with Prisma ORM<br>- <strong>Queue System</strong>: BullMQ (Redis) + RabbitMQ<br>- <strong>Template Engine</strong>: Handlebars.js<br>- <strong>Validation</strong>: Zod schemas</p><p><h3>Key Dependencies</h3><br>- <strong>@bull-board/express</strong> - Queue monitoring dashboard<br>- <strong>@slack/web-api</strong> - Slack integration<br>- <strong>nodemailer</strong> - Email sending<br>- <strong>axios</strong> - HTTP client for external APIs<br>- <strong>helmet</strong> - Security middleware<br>- <strong>ioredis</strong> - Redis client<br>- <strong>amqplib</strong> - RabbitMQ client</p><p><h3>Development Tools</h3><br>- <strong>tsx</strong> - TypeScript execution<br>- <strong>copyfiles</strong> - Build asset copying<br>- <strong>pino</strong> - Structured logging<br>- <strong>dotenv</strong> - Environment configuration</p><p><h2>Getting Started</h2></p><p><h3>Prerequisites</h3></p><p>- Node.js 18+<br>- MySQL 8.0+<br>- Redis 6.0+<br>- RabbitMQ 3.8+</p><p><h3>Installation</h3></p><p>1. <strong>Clone the repository</strong><br>   <pre><code>   git clone <repository-url><br>   cd notification-engine/engine-system<br>   </code></pre></p><p>2. <strong>Install dependencies</strong><br>   <pre><code>   npm install<br>   # or<br>   pnpm install<br>   </code></pre></p><p>3. <strong>Environment setup</strong><br>   <pre><code>   cp env.sample .env<br>   # Edit .env with your configuration<br>   </code></pre></p><p>4. <strong>Database setup</strong><br>   <pre><code>   npx prisma generate<br>   npx prisma db push<br>   npx prisma db seed<br>   </code></pre></p><p>5. <strong>Start the application</strong><br>   <pre><code>   npm run dev<br>   </code></pre></p><p><h3>Docker Setup</h3></p><p><pre><code>docker-compose up -d<br></code></pre></p><p><h2>Database Schema</h2></p><p>The system uses a comprehensive database schema with the following core entities:</p><p><h3>Core Models</h3></p><p>#### Notifications & Logging<br>- <strong>NotificationLog</strong> - Tracks all notification attempts and statuses<br>- <strong>TemplateAuditLog</strong> - Maintains template change history</p><p>#### Templates & Content<br>- <strong>Template</strong> - Stores notification templates with versioning<br>- <strong>ContentType</strong> - Defines template content formats (HTML, JSON, Block Kit, etc.)<br>- <strong>Parameter</strong> - Template and trigger parameter definitions</p><p>#### Triggers & Automation<br>- <strong>Trigger</strong> - Defines automation rules and conditions<br>- <strong>TriggerType</strong> - Categorizes trigger types<br>- <strong>SiteTriggerSetting</strong> - Site-specific trigger configurations<br>- <strong>SiteNotificationPreference</strong> - User notification preferences</p><p>#### Channel Management<br>- <strong>ChannelType</strong> - Supported notification channels (email, slack, hubspot)<br>- <strong>SiteToken</strong> - Encrypted access tokens for external services</p><p><h3>Key Relationships</h3></p><p><pre><code>NotificationLog ──▶ Trigger<br>NotificationLog ──▶ Template<br>NotificationLog ──▶ ChannelType</p><p>Template ──▶ Trigger<br>Template ──▶ ChannelType<br>Template ──▶ ContentType</p><p>Trigger ──▶ TriggerType<br>Trigger ──▶ Parameter[]</p><p>SiteTriggerSetting ──▶ Trigger<br>SiteNotificationPreference ──▶ ChannelType<br>SiteToken ──▶ ChannelType<br></code></pre></p><p><h3>Database Features</h3><br>- <strong>Soft deletes</strong> with <code>deletedAt</code> timestamps<br>- <strong>Comprehensive indexing</strong> for performance<br>- <strong>UUID public IDs</strong> for external references<br>- <strong>JSON metadata</strong> columns for flexibility<br>- <strong>Audit trails</strong> for compliance and debugging</p><p><h2>Queue System</h2></p><p>The notification engine uses a dual-queue architecture combining BullMQ (Redis-based) and RabbitMQ for different use cases.</p><p><h3>Queue Architecture</h3></p><p>#### BullMQ Queues (Redis-based)<br><pre><code>// High-performance job queues with built-in retry logic<br>EvaluationQueue    // Site evaluation and trigger checking<br>TriggerQueue       // Trigger processing and condition evaluation<br>NotificationQueue  // Notification delivery processing<br>BotQueue          // Bot and automation tasks<br>ObservationQueue  // Analytics and monitoring<br></code></pre></p><p>#### RabbitMQ Queues (Message broker)<br><pre><code>// Distributed messaging with exchange routing<br>TriggerExchange    // Routes trigger events<br>NotificationExchange // Routes notification jobs<br></code></pre></p><p><h3>Queue Configuration</h3></p><p>Each queue supports:<br>- <strong>Retry mechanisms</strong> with exponential backoff<br>- <strong>Dead letter queues</strong> for failed jobs<br>- <strong>Job prioritization</strong> and scheduling<br>- <strong>Concurrency control</strong> and rate limiting<br>- <strong>Monitoring and metrics</strong> via Bull Board</p><p><h3>Job Processing Flow</h3></p><p><pre><code>Site Event ──▶ EvaluationQueue ──▶ TriggerQueue ──▶ NotificationQueue ──▶ Delivery<br>     │               │                  │                   │              │<br>     ▼               ▼                  ▼                   ▼              ▼<br>  Analytics    Condition Check    Template Render    Channel Send    Audit Log<br></code></pre></p><p><h2>Template Engine</h2></p><p>The template engine provides a powerful, flexible system for creating and managing notification templates across multiple channels.</p><p><h3>Features</h3></p><p>- <strong>Handlebars templating</strong> with custom helpers<br>- <strong>Layout inheritance</strong> for consistent branding<br>- <strong>Multi-channel support</strong> (Email HTML, Slack Block Kit, etc.)<br>- <strong>Parameter validation</strong> with Zod schemas<br>- <strong>Template versioning</strong> and rollback capabilities<br>- <strong>Hot reloading</strong> in development mode</p><p><h3>Template Structure</h3></p><p><pre><code>templates/<br>├── layouts/           # Base layouts<br>│   ├── email.hbs     # Email HTML layout<br>│   └── slack.hbs     # Slack message layout<br>├── email/            # Email templates<br>│   ├── welcome.hbs<br>│   ├── reminder.hbs<br>│   └── summary.hbs<br>└── slack/            # Slack templates<br>    ├── alert.hbs<br>    └── notification.hbs<br></code></pre></p><p><h3>Template Registry</h3></p><p>Templates are registered with validation schemas:</p><p><pre><code>export const templateRegistry = {<br>  'weekly-summary': {<br>    email: {<br>      schema: z.object({<br>        siteName: z.string(),<br>        startDate: z.string(),<br>        endDate: z.string(),<br>        metrics: z.object({<br>          sessions: z.number(),<br>          revenue: z.number()<br>        })<br>      })<br>    },<br>    slack: {<br>      schema: z.object({<br>        // Slack-specific schema<br>      })<br>    }<br>  }<br>}<br></code></pre></p><p><h3>Template Examples</h3></p><p>#### Email Template (Handlebars)<br><pre><code>{{#> email-layout}}<br>  {{#content "header"}}<br>    <h1>Weekly Summary for {{siteName}}</h1><br>  {{/content}}</p><p>  {{#content "body"}}<br>    <p>Here's your weekly summary from {{startDate}} to {{endDate}}:</p><br>    <ul><br>      <li>Sessions: {{metrics.sessions}}</li><br>      <li>Revenue: ${{metrics.revenue}}</li><br>    </ul><br>  {{/content}}<br>{{/email-layout}}<br></code></pre></p><p>#### Slack Template (Block Kit)<br><pre><code>{<br>  "blocks": [<br>    {<br>      "type": "header",<br>      "text": {<br>        "type": "plain_text",<br>        "text": "Weekly Summary: {{siteName}}"<br>      }<br>    },<br>    {<br>      "type": "section",<br>      "text": {<br>        "type": "mrkdwn",<br>        "text": "<em>Sessions:</em> {{metrics.sessions}}\n<em>Revenue:</em> ${{metrics.revenue}}"<br>      }<br>    }<br>  ]<br>}<br></code></pre></p><p><h2>API Documentation</h2></p><p><h3>Core Endpoints</h3></p><p>#### Trigger Management<br><pre><code>POST   /trigger/evaluation    # Trigger site evaluation<br>GET    /trigger/:id          # Get trigger details<br>PUT    /trigger/:id          # Update trigger configuration<br></code></pre></p><p>#### Real-time Notifications<br><pre><code>POST   /real-time            # Send immediate notification<br></code></pre></p><p>#### Slack Integration<br><pre><code>POST   /slack/auth           # OAuth authentication<br>POST   /slack/message        # Send Slack message<br></code></pre></p><p>#### Admin & Monitoring<br><pre><code>GET    /up                   # Health check<br>GET    /admin/queues         # Queue monitoring dashboard<br></code></pre></p><p><h3>Request/Response Examples</h3></p><p>#### Send Real-time Notification<br><pre><code>POST /real-time<br>Content-Type: application/json</p><p>{<br>  "idSite": 123,<br>  "triggerName": "login-reminder",<br>  "templateData": {<br>    "userName": "John Doe",<br>    "daysSinceLogin": 7<br>  },<br>  "channels": ["email", "slack"]<br>}<br></code></pre></p><p>#### Trigger Site Evaluation<br><pre><code>POST /trigger/evaluation<br>Content-Type: application/json</p><p>{<br>  "sites": [123, 456, 789],<br>  "triggerTypes": ["trial", "engagement"],<br>  "immediate": true<br>}<br></code></pre></p><p><h2>Environment Configuration</h2></p><p><h3>Required Environment Variables</h3></p><p><pre><code><h1>Application</h1><br>NODE_ENV=development|production<br>PORT=3000<br>BUILD_TARGET=dev|prod</p><p><h1>Database</h1><br>DATABASE_URL=mysql://user:password@localhost:3306/notifications</p><p><h1>Redis (BullMQ)</h1><br>REDIS_HOST=localhost<br>REDIS_PORT=6379</p><p><h1>RabbitMQ</h1><br>RABBITMQ_HOST=localhost<br>RABBITMQ_USERNAME=guest<br>RABBITMQ_PASSWORD=guest</p><p><h1>Email (SMTP)</h1><br>SMTP_HOST=smtp.gmail.com<br>SMTP_PORT=587<br>SMTP_SECURE=true<br>SMTP_USERNAME=<EMAIL><br>SMTP_PASSWORD=your-password<br>DEFAULT_EMAIL_FROM=<EMAIL></p><p><h1>Slack Integration</h1><br>SLACK_CLIENT_ID=your-slack-client-id<br>SLACK_CLIENT_SECRET=your-slack-client-secret<br>SLACK_REDIRECT_BASE_URL=https://yourapp.com</p><p><h1>Scheduling (Cron expressions)</h1><br>TRIAL_SITES_SCHEDULE=0 8 <em> </em> *      # Daily at 8 AM<br>ACTIVE_SITES_SCHEDULE=0 0 <em> </em> 1     # Weekly on Monday<br>WEEK_METRICS_SCHEDULE=0 6 <em> </em> 1     # Weekly on Monday at 6 AM</p><p><h1>External Services</h1><br>HEATMAP_CONTEXT_BASE_URL=https://api.yourservice.com<br>OBSERVATION_URL=https://observation.yourservice.com</p><p><h1>Security</h1><br>ENCRYPTION_KEY=your-32-character-encryption-key</p><p><h1>Feature Flags</h1><br>EMAIL_ON=true<br>LOGGER_TOGGLE=true<br>RUN_SEED=false<br></code></pre></p><p><h3>Environment Validation</h3></p><p>The application includes comprehensive environment validation:</p><p><pre><code>// Validates required variables, cron expressions, and data types<br>export const envConfig = {<br>  nodeEnv: process.env.NODE_ENV,     // Validated against allowed values<br>  port: Number(process.env.PORT),    // Parsed and validated<br>  redis: {<br>    host: process.env.REDIS_HOST,    // Required<br>    port: Number(process.env.REDIS_PORT)<br>  },<br>  // ... other validated configs<br>}<br></code></pre></p><p><h2>Development Guide</h2></p><p><h3>Project Structure</h3></p><p><pre><code>src/<br>├── server.ts              # Application entry point<br>├── app.ts                 # Express app configuration<br>├── db.ts                  # Database connection<br>├── env.constant.ts        # Environment validation<br>├── template-engine.ts     # Template processing<br>├── template-registry.ts   # Template definitions<br>├── api/                   # REST API layer<br>│   ├── controllers/       # Request handlers<br>│   ├── routes/           # Route definitions<br>│   ├── middlewares.ts    # Express middleware<br>│   ├── schemas/          # Validation schemas<br>│   └── utils/            # API utilities<br>├── jobs/                 # Queue job definitions<br>│   ├── evaluation/       # Site evaluation jobs<br>│   ├── trigger/          # Trigger processing jobs<br>│   ├── notification/     # Notification jobs<br>│   ├── bot/             # Bot automation jobs<br>│   └── observation/     # Analytics jobs<br>├── messaging/           # Queue and messaging<br>│   ├── queue-service.ts # RabbitMQ service<br>│   ├── consumers/       # Message consumers<br>│   └── producers/       # Message producers<br>├── connectors/          # External service integrations<br>│   ├── smtp.ts         # Email connector<br>│   ├── slack.ts        # Slack connector<br>│   └── hubspot.ts      # HubSpot connector<br>├── services/           # Business logic services<br>├── types/              # TypeScript type definitions<br>└── utils/              # Shared utilities<br></code></pre></p><p><h3>Development Commands</h3></p><p><pre><code><h1>Development</h1><br>npm run dev                # Start with hot reload<br>npm run build             # Build for production<br>npm run seed              # Run database seed</p><p><h1>Database</h1><br>npx prisma generate       # Generate Prisma client<br>npx prisma db push        # Push schema changes<br>npx prisma studio        # Database GUI</p><p><h1>Queue Monitoring</h1><br><h1>Visit http://localhost:3000/admin/queues</h1><br></code></pre></p><p><h3>Adding New Templates</h3></p><p>1. <strong>Create template files</strong><br>   <pre><code>   # Email template<br>   touch src/new-templates/my-template.hbs</p><p>   # Slack template<br>   touch src/slack-templates/my-template.hbs<br>   </code></pre></p><p>2. <strong>Register in template registry</strong><br>   <pre><code>   // src/template-registry.ts<br>   export const templateRegistry = {<br>     'my-template': {<br>       email: {<br>         schema: z.object({<br>           // Define validation schema<br>         })<br>       },<br>       slack: {<br>         schema: z.object({<br>           // Define validation schema<br>         })<br>       }<br>     }<br>   }<br>   </code></pre></p><p>3. <strong>Create database entries</strong><br>   <pre><code>   -- Add to triggers table<br>   INSERT INTO triggers (name, description) VALUES ('my-template', 'My template description');</p><p>   -- Add templates for each channel<br>   INSERT INTO templates (trigger_id, channel_id, name, body, content_type_id)<br>   VALUES (trigger_id, 1, 'my-template-email', 'template-content', 1);<br>   </code></pre></p><p><h3>Adding New Job Types</h3></p><p>1. <strong>Create job definition</strong><br>   <pre><code>   // src/jobs/my-job/jobs.ts<br>   export interface MyJobData {<br>     siteId: number;<br>     // ... other properties<br>   }</p><p>   export async function processMyJob(job: Job<MyJobData>) {<br>     // Job processing logic<br>   }<br>   </code></pre></p><p>2. <strong>Add to queue</strong><br>   <pre><code>   // src/jobs/my-job/my-job-queue.ts<br>   export class MyJobQueue extends BaseQueue<MyJobData> {<br>     // Queue implementation<br>   }<br>   </code></pre></p><p>3. <strong>Register consumer</strong><br>   <pre><code>   // src/messaging/consumers/my-job.ts<br>   export async function startMyJobConsumer(queue: MyJobQueue) {<br>     // Consumer implementation<br>   }<br>   </code></pre></p><p><h2>Deployment</h2></p><p><h3>Docker Deployment</h3></p><p>The application includes Docker configuration for containerized deployment:</p><p><pre><code><h1>docker-compose.yml</h1><br>version: '3.8'<br>services:<br>  app:<br>    build: .<br>    ports:<br>      - "3000:3000"<br>    environment:<br>      - NODE_ENV=production<br>    depends_on:<br>      - mysql<br>      - redis<br>      - rabbitmq</p><p>  mysql:<br>    image: mysql:8.0<br>    environment:<br>      MYSQL_ROOT_PASSWORD: password<br>      MYSQL_DATABASE: notifications</p><p>  redis:<br>    image: redis:6-alpine</p><p>  rabbitmq:<br>    image: rabbitmq:3-management<br></code></pre></p><p><h3>Production Considerations</h3></p><p>1. <strong>Environment Variables</strong><br>   - Use secure secret management<br>   - Enable SSL/TLS for external connections<br>   - Configure proper CORS settings</p><p>2. <strong>Database</strong><br>   - Set up read replicas for scaling<br>   - Configure regular backups<br>   - Monitor query performance</p><p>3. <strong>Queue System</strong><br>   - Configure Redis persistence<br>   - Set up RabbitMQ clustering<br>   - Monitor queue depths and processing times</p><p>4. <strong>Monitoring</strong><br>   - Set up application performance monitoring<br>   - Configure log aggregation<br>   - Set up alerts for critical failures</p><p>5. <strong>Security</strong><br>   - Enable rate limiting<br>   - Configure proper authentication<br>   - Regular security updates</p><p><h3>Health Checks</h3></p><p>The application provides health check endpoints:</p><p><pre><code>GET /up                    # Basic health check<br>GET /admin/queues         # Queue status monitoring<br></code></pre></p><p><h3>Logging</h3></p><p>Structured logging with multiple levels:</p><p><pre><code>// Log levels: trace, debug, info, warn, error, fatal<br>logger.info('Application started', { port: 3000 });<br>logger.error('Database connection failed', { error: err.message });<br></code></pre></p><p>---</p><p><h2>Documentation</h2></p><p><h3>Comprehensive Guides</h3></p><p>The system includes detailed documentation for all aspects:</p><p>- <strong><a href="docs/DATABASE.md">Database Schema</a></strong> - Complete database documentation with models, relationships, and optimization<br>- <strong><a href="docs/QUEUES.md">Queue System</a></strong> - Dual-queue architecture with BullMQ and RabbitMQ implementation details<br>- <strong><a href="docs/TEMPLATES.md">Template Engine</a></strong> - Handlebars templating system with custom helpers and multi-channel support<br>- <strong><a href="docs/API.md">API Reference</a></strong> - Complete REST API documentation with examples and schemas<br>- <strong><a href="docs/TESTING.md">Testing Guide</a></strong> - Comprehensive testing strategies including unit, integration, and load testing<br>- <strong><a href="docs/DEPLOYMENT.md">Deployment Guide</a></strong> - Production deployment instructions for Docker, AWS, and Kubernetes<br>- <strong><a href="docs/TROUBLESHOOTING.md">Troubleshooting Guide</a></strong> - Common issues, debugging techniques, and FAQ</p><p><h3>Quick Links</h3></p><p>- <strong>Setup & Installation</strong>: See <a href="#getting-started">Getting Started</a> section above<br>- <strong>API Usage</strong>: Check <a href="docs/API.md">API Documentation</a> for endpoint details<br>- <strong>Template Development</strong>: Review <a href="docs/TEMPLATES.md">Template System</a> guide<br>- <strong>Queue Monitoring</strong>: Use <a href="docs/QUEUES.md#monitoring">Queue Dashboard</a> instructions<br>- <strong>Performance Tuning</strong>: Follow <a href="docs/DEPLOYMENT.md#scaling-considerations">Deployment Guide</a><br>- <strong>Issue Resolution</strong>: Consult <a href="docs/TROUBLESHOOTING.md">Troubleshooting Guide</a></p><p>---</p><p><h2>Support</h2></p><p>For questions, issues, or contributions:</p><p>1. Check the <a href="repository-url/issues">Issues</a> section<br>2. Review existing documentation in the <code>docs/</code> folder<br>3. Consult the <a href="docs/TROUBLESHOOTING.md">Troubleshooting Guide</a><br>4. Contact the development team</p><p><h2>License</h2></p><p>[License Type] - See LICENSE file for details<br>
            </article>
        </main>
    </div>
</body>
</html>