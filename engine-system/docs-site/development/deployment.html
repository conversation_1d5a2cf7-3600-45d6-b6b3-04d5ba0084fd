<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Guide - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Development Guide › Deployment Guide
            </nav>
        </header>

        <main>
            <article>
                <h1>Deployment Guide</h1>
                <h1>Deployment Guide</h1></p><p>This document provides comprehensive deployment instructions for the Notification Engine System across different environments and platforms.</p><p><h2>Table of Contents</h2></p><p>- <a href="#deployment-overview">Deployment Overview</a><br>- <a href="#prerequisites">Prerequisites</a><br>- <a href="#environment-setup">Environment Setup</a><br>- <a href="#docker-deployment">Docker Deployment</a><br>- <a href="#production-deployment">Production Deployment</a><br>- <a href="#aws-deployment">AWS Deployment</a><br>- <a href="#monitoring-and-health-checks">Monitoring and Health Checks</a><br>- <a href="#scaling-considerations">Scaling Considerations</a><br>- <a href="#troubleshooting">Troubleshooting</a></p><p><h2>Deployment Overview</h2></p><p>The Notification Engine System can be deployed in several ways:</p><p>- <strong>Local Development</strong>: Using Docker Compose for development and testing<br>- <strong>Staging Environment</strong>: Containerized deployment with external databases<br>- <strong>Production</strong>: Multi-instance deployment with load balancing and monitoring<br>- <strong>Cloud Deployment</strong>: AWS, Google Cloud, or Azure with managed services</p><p><h3>Architecture Components</h3></p><p><pre><code>┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐<br>│   Load Balancer │    │   App Instances │    │   Databases     │<br>│                 │    │                 │    │                 │<br>│ • Nginx/ALB     │───▶│ • Node.js Apps  │───▶│ • MySQL RDS     │<br>│ • SSL/TLS       │    │ • Docker        │    │ • Redis Cluster │<br>│ • Health Checks │    │ • Auto Scaling  │    │ • RabbitMQ      │<br>└─────────────────┘    └─────────────────┘    └─────────────────┘<br></code></pre></p><p><h2>Prerequisites</h2></p><p><h3>System Requirements</h3></p><p>- <strong>Node.js</strong>: Version 18+ LTS<br>- <strong>Docker</strong>: Version 20.10+<br>- <strong>Docker Compose</strong>: Version 2.0+<br>- <strong>MySQL</strong>: Version 8.0+<br>- <strong>Redis</strong>: Version 6.0+<br>- <strong>RabbitMQ</strong>: Version 3.8+</p><p><h3>Minimum Hardware</h3></p><p><strong>Development:</strong><br>- CPU: 2 cores<br>- RAM: 4GB<br>- Storage: 10GB</p><p><strong>Production:</strong><br>- CPU: 4+ cores<br>- RAM: 8GB+<br>- Storage: 50GB+ (with monitoring)</p><p><h2>Environment Setup</h2></p><p><h3>Environment Variables</h3></p><p>Create environment files for each deployment stage:</p><p><pre><code><h1>.env.development</h1><br>NODE_ENV=development<br>PORT=3000<br>BUILD_TARGET=dev<br>LOGGER_TOGGLE=true</p><p><h1>Database</h1><br>DATABASE_URL=mysql://user:password@localhost:3306/notifications_dev</p><p><h1>Redis</h1><br>REDIS_HOST=localhost<br>REDIS_PORT=6379</p><p><h1>RabbitMQ</h1><br>RABBITMQ_HOST=localhost<br>RABBITMQ_USERNAME=guest<br>RABBITMQ_PASSWORD=guest</p><p><h1>Email</h1><br>EMAIL_ON=false<br>SMTP_HOST=smtp.gmail.com<br>SMTP_PORT=587<br>SMTP_SECURE=true<br></code></pre></p><p><pre><code><h1>.env.staging</h1><br>NODE_ENV=staging<br>PORT=3000<br>BUILD_TARGET=prod<br>LOGGER_TOGGLE=true</p><p><h1>Database (use staging database)</h1><br>DATABASE_URL=mysql://user:password@staging-db:3306/notifications_staging</p><p><h1>External Redis/RabbitMQ</h1><br>REDIS_HOST=staging-redis.cache.amazonaws.com<br>RABBITMQ_HOST=staging-rabbitmq.domain.com</p><p><h1>Email (use test SMTP)</h1><br>EMAIL_ON=true<br>SMTP_HOST=smtp.mailtrap.io<br></code></pre></p><p><pre><code><h1>.env.production</h1><br>NODE_ENV=production<br>PORT=3000<br>BUILD_TARGET=prod<br>LOGGER_TOGGLE=false</p><p><h1>Production databases</h1><br>DATABASE_URL=mysql://user:password@prod-db-cluster:3306/notifications<br>REDIS_HOST=prod-redis-cluster.cache.amazonaws.com<br>RABBITMQ_HOST=prod-rabbitmq-cluster.domain.com</p><p><h1>Production email</h1><br>EMAIL_ON=true<br>SMTP_HOST=smtp.sendgrid.net<br>SMTP_PORT=587<br>SMTP_USERNAME=apikey<br>SMTP_PASSWORD=${SENDGRID_API_KEY}</p><p><h1>Security</h1><br>ENCRYPTION_KEY=${ENCRYPTION_KEY}<br>JWT_SECRET=${JWT_SECRET}<br></code></pre></p><p><h3>Secrets Management</h3></p><p><pre><code><h1>Use AWS Secrets Manager, Azure Key Vault, or similar</h1><br>aws secretsmanager create-secret \<br>  --name "notification-engine/production" \<br>  --description "Production secrets for Notification Engine" \<br>  --secret-string '{<br>    "DATABASE_PASSWORD": "secure-password",<br>    "SMTP_PASSWORD": "sendgrid-api-key",<br>    "ENCRYPTION_KEY": "32-char-encryption-key",<br>    "SLACK_CLIENT_SECRET": "slack-client-secret"<br>  }'<br></code></pre></p><p><h2>Docker Deployment</h2></p><p><h3>Single Container Deployment</h3></p><p><pre><code><h1>Dockerfile</h1><br>FROM node:18-alpine as builder</p><p>WORKDIR /app<br>COPY package*.json ./<br>RUN npm ci --only=production</p><p>COPY . .<br>RUN npx prisma generate<br>RUN npm run build</p><p>FROM node:18-alpine as production</p><p>WORKDIR /app<br>COPY --from=builder /app/dist ./dist<br>COPY --from=builder /app/node_modules ./node_modules<br>COPY --from=builder /app/prisma ./prisma<br>COPY --from=builder /app/package*.json ./</p><p><h1>Create non-root user</h1><br>RUN addgroup -g 1001 -S nodejs<br>RUN adduser -S nextjs -u 1001<br>USER nextjs</p><p>EXPOSE 3000<br>CMD ["node", "dist/server.js"]<br></code></pre></p><p><h3>Docker Compose Development</h3></p><p><pre><code><h1>docker-compose.dev.yml</h1><br>version: '3.8'</p><p>services:<br>  app:<br>    build:<br>      context: .<br>      target: development<br>    ports:<br>      - "3000:3000"<br>    environment:<br>      - NODE_ENV=development<br>    volumes:<br>      - .:/app<br>      - /app/node_modules<br>    depends_on:<br>      - mysql<br>      - redis<br>      - rabbitmq<br>    restart: unless-stopped</p><p>  mysql:<br>    image: mysql:8.0<br>    environment:<br>      MYSQL_ROOT_PASSWORD: password<br>      MYSQL_DATABASE: notifications<br>      MYSQL_USER: notif_user<br>      MYSQL_PASSWORD: notif_pass<br>    ports:<br>      - "3306:3306"<br>    volumes:<br>      - mysql_data:/var/lib/mysql<br>    restart: unless-stopped</p><p>  redis:<br>    image: redis:7-alpine<br>    ports:<br>      - "6379:6379"<br>    volumes:<br>      - redis_data:/data<br>    restart: unless-stopped</p><p>  rabbitmq:<br>    image: rabbitmq:3-management-alpine<br>    environment:<br>      RABBITMQ_DEFAULT_USER: guest<br>      RABBITMQ_DEFAULT_PASS: guest<br>    ports:<br>      - "5672:5672"<br>      - "15672:15672"<br>    volumes:<br>      - rabbitmq_data:/var/lib/rabbitmq<br>    restart: unless-stopped</p><p>volumes:<br>  mysql_data:<br>  redis_data:<br>  rabbitmq_data:<br></code></pre></p><p><h3>Docker Compose Production</h3></p><p><pre><code><h1>docker-compose.prod.yml</h1><br>version: '3.8'</p><p>services:<br>  app:<br>    image: notification-engine:latest<br>    deploy:<br>      replicas: 3<br>      restart_policy:<br>        condition: on-failure<br>        max_attempts: 3<br>    environment:<br>      - NODE_ENV=production<br>      - DATABASE_URL=${DATABASE_URL}<br>      - REDIS_HOST=${REDIS_HOST}<br>    ports:<br>      - "3000-3002:3000"<br>    healthcheck:<br>      test: ["CMD", "curl", "-f", "http://localhost:3000/up"]<br>      interval: 30s<br>      timeout: 10s<br>      retries: 3<br>      start_period: 40s</p><p>  nginx:<br>    image: nginx:alpine<br>    ports:<br>      - "80:80"<br>      - "443:443"<br>    volumes:<br>      - ./nginx.conf:/etc/nginx/nginx.conf<br>      - ./ssl:/etc/ssl/certs<br>    depends_on:<br>      - app<br>    restart: unless-stopped<br></code></pre></p><p><h2>Production Deployment</h2></p><p><h3>Build Process</h3></p><p><pre><code>#!/bin/bash<br><h1>build.sh</h1></p><p>set -e</p><p>echo "Building Notification Engine..."</p><p><h1>Install dependencies</h1><br>npm ci --only=production</p><p><h1>Generate Prisma client</h1><br>npx prisma generate</p><p><h1>Build TypeScript</h1><br>npm run build</p><p><h1>Create production image</h1><br>docker build -t notification-engine:${BUILD_VERSION} .<br>docker tag notification-engine:${BUILD_VERSION} notification-engine:latest</p><p>echo "Build completed successfully"<br></code></pre></p><p><h3>Database Migration</h3></p><p><pre><code>#!/bin/bash<br><h1>migrate.sh</h1></p><p>set -e</p><p>echo "Running database migrations..."</p><p><h1>Backup database</h1><br>mysqldump -h ${DB_HOST} -u ${DB_USER} -p${DB_PASSWORD} notifications > backup_$(date +%Y%m%d_%H%M%S).sql</p><p><h1>Run migrations</h1><br>npx prisma migrate deploy</p><p><h1>Verify migration</h1><br>npx prisma migrate status</p><p>echo "Migrations completed successfully"<br></code></pre></p><p><h3>Deployment Script</h3></p><p><pre><code>#!/bin/bash<br><h1>deploy.sh</h1></p><p>set -e</p><p>BUILD_VERSION=${1:-latest}<br>ENVIRONMENT=${2:-production}</p><p>echo "Deploying Notification Engine v${BUILD_VERSION} to ${ENVIRONMENT}..."</p><p><h1>Pull latest code</h1><br>git pull origin main</p><p><h1>Build application</h1><br>./scripts/build.sh</p><p><h1>Run database migrations</h1><br>./scripts/migrate.sh</p><p><h1>Stop existing containers</h1><br>docker-compose -f docker-compose.${ENVIRONMENT}.yml down</p><p><h1>Start new deployment</h1><br>docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d</p><p><h1>Health check</h1><br>sleep 30<br>./scripts/health-check.sh</p><p>echo "Deployment completed successfully"<br></code></pre></p><p><h3>Health Check Script</h3></p><p><pre><code>#!/bin/bash<br><h1>health-check.sh</h1></p><p>set -e</p><p>BASE_URL=${1:-http://localhost:3000}<br>MAX_ATTEMPTS=10<br>ATTEMPT=1</p><p>echo "Performing health checks..."</p><p>while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do<br>  echo "Attempt $ATTEMPT/$MAX_ATTEMPTS"<br>  <br>  # Basic health check<br>  if curl -f -s "${BASE_URL}/up" > /dev/null; then<br>    echo "✓ Basic health check passed"<br>  else<br>    echo "✗ Basic health check failed"<br>    exit 1<br>  fi<br>  <br>  # Queue health check<br>  if curl -f -s "${BASE_URL}/admin/queues" > /dev/null; then<br>    echo "✓ Queue health check passed"<br>  else<br>    echo "✗ Queue health check failed"<br>    exit 1<br>  fi<br>  <br>  # Database connectivity<br>  if curl -f -s "${BASE_URL}/health/database" > /dev/null; then<br>    echo "✓ Database connectivity check passed"<br>    break<br>  else<br>    echo "✗ Database connectivity check failed"<br>    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then<br>      exit 1<br>    fi<br>  fi<br>  <br>  ATTEMPT=$((ATTEMPT + 1))<br>  sleep 10<br>done</p><p>echo "All health checks passed!"<br></code></pre></p><p><h2>AWS Deployment</h2></p><p><h3>ECS Deployment</h3></p><p><pre><code>// task-definition.json<br>{<br>  "family": "notification-engine",<br>  "networkMode": "awsvpc",<br>  "requiresCompatibilities": ["FARGATE"],<br>  "cpu": "1024",<br>  "memory": "2048",<br>  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",<br>  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/notificationEngineTaskRole",<br>  "containerDefinitions": [<br>    {<br>      "name": "notification-engine",<br>      "image": "ACCOUNT.dkr.ecr.REGION.amazonaws.com/notification-engine:latest",<br>      "portMappings": [<br>        {<br>          "containerPort": 3000,<br>          "protocol": "tcp"<br>        }<br>      ],<br>      "environment": [<br>        {<br>          "name": "NODE_ENV",<br>          "value": "production"<br>        },<br>        {<br>          "name": "PORT",<br>          "value": "3000"<br>        }<br>      ],<br>      "secrets": [<br>        {<br>          "name": "DATABASE_URL",<br>          "valueFrom": "arn:aws:secretsmanager:REGION:ACCOUNT:secret:notification-engine/production:DATABASE_URL::"<br>        }<br>      ],<br>      "logConfiguration": {<br>        "logDriver": "awslogs",<br>        "options": {<br>          "awslogs-group": "/ecs/notification-engine",<br>          "awslogs-region": "us-east-1",<br>          "awslogs-stream-prefix": "ecs"<br>        }<br>      },<br>      "healthCheck": {<br>        "command": ["CMD-SHELL", "curl -f http://localhost:3000/up || exit 1"],<br>        "interval": 30,<br>        "timeout": 5,<br>        "retries": 3,<br>        "startPeriod": 60<br>      }<br>    }<br>  ]<br>}<br></code></pre></p><p><h3>CloudFormation Template</h3></p><p><pre><code><h1>infrastructure.yml</h1><br>AWSTemplateFormatVersion: '2010-09-09'<br>Description: 'Notification Engine Infrastructure'</p><p>Parameters:<br>  Environment:<br>    Type: String<br>    Default: production<br>    AllowedValues: [development, staging, production]</p><p>Resources:<br>  # VPC and Networking<br>  VPC:<br>    Type: AWS::EC2::VPC<br>    Properties:<br>      CidrBlock: 10.0.0.0/16<br>      EnableDnsHostnames: true<br>      EnableDnsSupport: true<br>      Tags:<br>        - Key: Name<br>          Value: !Sub ${Environment}-notification-engine-vpc</p><p>  # RDS MySQL Instance<br>  DatabaseSubnetGroup:<br>    Type: AWS::RDS::DBSubnetGroup<br>    Properties:<br>      DBSubnetGroupDescription: Subnet group for RDS database<br>      SubnetIds:<br>        - !Ref PrivateSubnet1<br>        - !Ref PrivateSubnet2</p><p>  Database:<br>    Type: AWS::RDS::DBInstance<br>    Properties:<br>      DBInstanceIdentifier: !Sub ${Environment}-notification-engine-db<br>      DBInstanceClass: db.t3.medium<br>      Engine: mysql<br>      EngineVersion: '8.0'<br>      AllocatedStorage: 100<br>      StorageType: gp2<br>      DBName: notifications<br>      MasterUsername: admin<br>      MasterUserPassword: !Ref DatabasePassword<br>      VPCSecurityGroups:<br>        - !Ref DatabaseSecurityGroup<br>      DBSubnetGroupName: !Ref DatabaseSubnetGroup<br>      BackupRetentionPeriod: 7<br>      MultiAZ: !If [IsProduction, true, false]<br>      StorageEncrypted: true</p><p>  # ElastiCache Redis<br>  RedisSubnetGroup:<br>    Type: AWS::ElastiCache::SubnetGroup<br>    Properties:<br>      Description: Subnet group for Redis cluster<br>      SubnetIds:<br>        - !Ref PrivateSubnet1<br>        - !Ref PrivateSubnet2</p><p>  RedisCluster:<br>    Type: AWS::ElastiCache::ReplicationGroup<br>    Properties:<br>      ReplicationGroupId: !Sub ${Environment}-notification-engine-redis<br>      Description: Redis cluster for notification engine<br>      NumCacheClusters: 2<br>      Engine: redis<br>      CacheNodeType: cache.t3.medium<br>      CacheSubnetGroupName: !Ref RedisSubnetGroup<br>      SecurityGroupIds:<br>        - !Ref RedisSecurityGroup<br>      AtRestEncryptionEnabled: true<br>      TransitEncryptionEnabled: true</p><p>  # ECS Cluster<br>  ECSCluster:<br>    Type: AWS::ECS::Cluster<br>    Properties:<br>      ClusterName: !Sub ${Environment}-notification-engine</p><p>  # Application Load Balancer<br>  LoadBalancer:<br>    Type: AWS::ElasticLoadBalancingV2::LoadBalancer<br>    Properties:<br>      Name: !Sub ${Environment}-notification-engine-alb<br>      Scheme: internet-facing<br>      SecurityGroups:<br>        - !Ref LoadBalancerSecurityGroup<br>      Subnets:<br>        - !Ref PublicSubnet1<br>        - !Ref PublicSubnet2</p><p>Conditions:<br>  IsProduction: !Equals [!Ref Environment, production]</p><p>Outputs:<br>  DatabaseEndpoint:<br>    Description: RDS instance endpoint<br>    Value: !GetAtt Database.Endpoint.Address<br>    Export:<br>      Name: !Sub ${Environment}-db-endpoint</p><p>  RedisEndpoint:<br>    Description: Redis cluster endpoint<br>    Value: !GetAtt RedisCluster.RedisEndpoint.Address<br>    Export:<br>      Name: !Sub ${Environment}-redis-endpoint<br></code></pre></p><p><h3>Terraform Configuration</h3></p><p><pre><code><h1>main.tf</h1><br>terraform {<br>  required_version = ">= 1.0"<br>  required_providers {<br>    aws = {<br>      source  = "hashicorp/aws"<br>      version = "~> 5.0"<br>    }<br>  }<br>}</p><p>provider "aws" {<br>  region = var.aws_region<br>}</p><p><h1>VPC</h1><br>module "vpc" {<br>  source = "terraform-aws-modules/vpc/aws"</p><p>  name = "${var.environment}-notification-engine"<br>  cidr = "10.0.0.0/16"</p><p>  azs             = ["${var.aws_region}a", "${var.aws_region}b"]<br>  private_subnets = ["********/24", "********/24"]<br>  public_subnets  = ["**********/24", "**********/24"]</p><p>  enable_nat_gateway = true<br>  enable_vpn_gateway = true</p><p>  tags = {<br>    Environment = var.environment<br>    Project     = "notification-engine"<br>  }<br>}</p><p><h1>RDS MySQL</h1><br>resource "aws_db_instance" "mysql" {<br>  identifier     = "${var.environment}-notification-engine"<br>  engine         = "mysql"<br>  engine_version = "8.0"<br>  instance_class = var.db_instance_class<br>  <br>  allocated_storage     = 100<br>  max_allocated_storage = 1000<br>  storage_encrypted     = true<br>  <br>  db_name  = "notifications"<br>  username = var.db_username<br>  password = var.db_password<br>  <br>  vpc_security_group_ids = [aws_security_group.rds.id]<br>  db_subnet_group_name   = aws_db_subnet_group.default.name<br>  <br>  backup_retention_period = 7<br>  backup_window          = "03:00-04:00"<br>  maintenance_window     = "sun:04:00-sun:05:00"<br>  <br>  skip_final_snapshot = var.environment != "production"<br>  <br>  tags = {<br>    Environment = var.environment<br>    Project     = "notification-engine"<br>  }<br>}</p><p><h1>ElastiCache Redis</h1><br>resource "aws_elasticache_replication_group" "redis" {<br>  replication_group_id       = "${var.environment}-notification-engine"<br>  description                = "Redis cluster for notification engine"<br>  <br>  port               = 6379<br>  parameter_group_name = "default.redis7"<br>  node_type          = var.redis_node_type<br>  num_cache_clusters = 2<br>  <br>  subnet_group_name  = aws_elasticache_subnet_group.default.name<br>  security_group_ids = [aws_security_group.redis.id]<br>  <br>  at_rest_encryption_enabled = true<br>  transit_encryption_enabled = true<br>  <br>  tags = {<br>    Environment = var.environment<br>    Project     = "notification-engine"<br>  }<br>}</p><p><h1>ECS Service</h1><br>resource "aws_ecs_service" "notification_engine" {<br>  name            = "notification-engine"<br>  cluster         = aws_ecs_cluster.main.id<br>  task_definition = aws_ecs_task_definition.notification_engine.arn<br>  desired_count   = var.app_count</p><p>  launch_type = "FARGATE"</p><p>  network_configuration {<br>    security_groups  = [aws_security_group.ecs_tasks.id]<br>    subnets          = module.vpc.private_subnets<br>    assign_public_ip = false<br>  }</p><p>  load_balancer {<br>    target_group_arn = aws_lb_target_group.app.arn<br>    container_name   = "notification-engine"<br>    container_port   = 3000<br>  }</p><p>  depends_on = [aws_lb_listener.app]<br>}<br></code></pre></p><p><h2>Monitoring and Health Checks</h2></p><p><h3>Application Health Endpoints</h3></p><p><pre><code>// src/routes/health.ts<br>import { Router } from 'express';<br>import { PrismaClient } from '@prisma/client';<br>import RedisClient from '../services/redis';<br>import { QueueService } from '../messaging/queue-service';</p><p>const router = Router();<br>const prisma = new PrismaClient();</p><p>// Basic health check<br>router.get('/up', (req, res) => {<br>  res.status(200).json({<br>    status: 'ok',<br>    timestamp: new Date().toISOString(),<br>    uptime: process.uptime()<br>  });<br>});</p><p>// Detailed health check<br>router.get('/health', async (req, res) => {<br>  const health = {<br>    status: 'ok',<br>    timestamp: new Date().toISOString(),<br>    services: {<br>      database: 'unknown',<br>      redis: 'unknown',<br>      rabbitmq: 'unknown'<br>    }<br>  };</p><p>  try {<br>    // Database check<br>    await prisma.$queryRaw<code>SELECT 1</code>;<br>    health.services.database = 'ok';<br>  } catch (error) {<br>    health.services.database = 'error';<br>    health.status = 'error';<br>  }</p><p>  try {<br>    // Redis check<br>    const redis = RedisClient.getInstance();<br>    await redis.ping();<br>    health.services.redis = 'ok';<br>  } catch (error) {<br>    health.services.redis = 'error';<br>    health.status = 'error';<br>  }</p><p>  try {<br>    // RabbitMQ check<br>    const queueService = QueueService.getInstance();<br>    await queueService.checkConnection();<br>    health.services.rabbitmq = 'ok';<br>  } catch (error) {<br>    health.services.rabbitmq = 'error';<br>    health.status = 'error';<br>  }</p><p>  const statusCode = health.status === 'ok' ? 200 : 503;<br>  res.status(statusCode).json(health);<br>});</p><p>// Queue metrics<br>router.get('/metrics/queues', async (req, res) => {<br>  try {<br>    const queueService = QueueService.getInstance();<br>    const metrics = await queueService.getMetrics();<br>    <br>    res.json({<br>      timestamp: new Date().toISOString(),<br>      queues: metrics<br>    });<br>  } catch (error) {<br>    res.status(500).json({ error: 'Failed to get queue metrics' });<br>  }<br>});</p><p>export default router;<br></code></pre></p><p><h3>Prometheus Metrics</h3></p><p><pre><code>// src/middleware/metrics.ts<br>import { Request, Response, NextFunction } from 'express';<br>import client from 'prom-client';</p><p>// Create metrics<br>const httpRequestDuration = new client.Histogram({<br>  name: 'http_request_duration_seconds',<br>  help: 'Duration of HTTP requests in seconds',<br>  labelNames: ['method', 'route', 'status_code'],<br>  buckets: [0.1, 0.5, 1, 2, 5]<br>});</p><p>const httpRequestTotal = new client.Counter({<br>  name: 'http_requests_total',<br>  help: 'Total number of HTTP requests',<br>  labelNames: ['method', 'route', 'status_code']<br>});</p><p>const queueJobsProcessed = new client.Counter({<br>  name: 'queue_jobs_processed_total',<br>  help: 'Total number of queue jobs processed',<br>  labelNames: ['queue_name', 'status']<br>});</p><p>// Middleware<br>export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {<br>  const start = Date.now();<br>  <br>  res.on('finish', () => {<br>    const duration = (Date.now() - start) / 1000;<br>    const route = req.route?.path || req.path;<br>    <br>    httpRequestDuration<br>      .labels(req.method, route, res.statusCode.toString())<br>      .observe(duration);<br>    <br>    httpRequestTotal<br>      .labels(req.method, route, res.statusCode.toString())<br>      .inc();<br>  });<br>  <br>  next();<br>};</p><p>// Metrics endpoint<br>export const metricsHandler = async (req: Request, res: Response) => {<br>  res.set('Content-Type', client.register.contentType);<br>  res.end(await client.register.metrics());<br>};</p><p>export { queueJobsProcessed };<br></code></pre></p><p><h3>Monitoring Stack</h3></p><p><pre><code><h1>monitoring/docker-compose.yml</h1><br>version: '3.8'</p><p>services:<br>  prometheus:<br>    image: prom/prometheus:latest<br>    ports:<br>      - "9090:9090"<br>    volumes:<br>      - ./prometheus.yml:/etc/prometheus/prometheus.yml<br>      - prometheus_data:/prometheus<br>    command:<br>      - '--config.file=/etc/prometheus/prometheus.yml'<br>      - '--storage.tsdb.path=/prometheus'<br>      - '--web.console.libraries=/etc/prometheus/console_libraries'<br>      - '--web.console.templates=/etc/prometheus/consoles'</p><p>  grafana:<br>    image: grafana/grafana:latest<br>    ports:<br>      - "3001:3000"<br>    environment:<br>      - GF_SECURITY_ADMIN_PASSWORD=admin<br>    volumes:<br>      - grafana_data:/var/lib/grafana<br>      - ./grafana/provisioning:/etc/grafana/provisioning</p><p>  alertmanager:<br>    image: prom/alertmanager:latest<br>    ports:<br>      - "9093:9093"<br>    volumes:<br>      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml</p><p>volumes:<br>  prometheus_data:<br>  grafana_data:<br></code></pre></p><p><h2>Scaling Considerations</h2></p><p><h3>Horizontal Scaling</h3></p><p><pre><code><h1>kubernetes/deployment.yml</h1><br>apiVersion: apps/v1<br>kind: Deployment<br>metadata:<br>  name: notification-engine<br>  labels:<br>    app: notification-engine<br>spec:<br>  replicas: 3<br>  selector:<br>    matchLabels:<br>      app: notification-engine<br>  template:<br>    metadata:<br>      labels:<br>        app: notification-engine<br>    spec:<br>      containers:<br>      - name: notification-engine<br>        image: notification-engine:latest<br>        ports:<br>        - containerPort: 3000<br>        env:<br>        - name: NODE_ENV<br>          value: "production"<br>        resources:<br>          requests:<br>            memory: "512Mi"<br>            cpu: "500m"<br>          limits:<br>            memory: "1Gi"<br>            cpu: "1000m"<br>        readinessProbe:<br>          httpGet:<br>            path: /up<br>            port: 3000<br>          initialDelaySeconds: 10<br>          periodSeconds: 5<br>        livenessProbe:<br>          httpGet:<br>            path: /health<br>            port: 3000<br>          initialDelaySeconds: 30<br>          periodSeconds: 10</p><p>---<br>apiVersion: v1<br>kind: Service<br>metadata:<br>  name: notification-engine-service<br>spec:<br>  selector:<br>    app: notification-engine<br>  ports:<br>  - protocol: TCP<br>    port: 80<br>    targetPort: 3000<br>  type: LoadBalancer</p><p>---<br>apiVersion: autoscaling/v2<br>kind: HorizontalPodAutoscaler<br>metadata:<br>  name: notification-engine-hpa<br>spec:<br>  scaleTargetRef:<br>    apiVersion: apps/v1<br>    kind: Deployment<br>    name: notification-engine<br>  minReplicas: 2<br>  maxReplicas: 10<br>  metrics:<br>  - type: Resource<br>    resource:<br>      name: cpu<br>      target:<br>        type: Utilization<br>        averageUtilization: 70<br>  - type: Resource<br>    resource:<br>      name: memory<br>      target:<br>        type: Utilization<br>        averageUtilization: 80<br></code></pre></p><p><h3>Database Scaling</h3></p><p><pre><code>-- Read replicas setup<br>CREATE USER 'readonly'@'%' IDENTIFIED BY 'secure_password';<br>GRANT SELECT ON notifications.* TO 'readonly'@'%';</p><p>-- Connection pooling configuration<br>-- In application code:<br>const readOnlyPool = mysql.createPool({<br>  host: 'read-replica-endpoint',<br>  user: 'readonly',<br>  password: 'secure_password',<br>  database: 'notifications',<br>  connectionLimit: 10<br>});<br></code></pre></p><p><h2>Troubleshooting</h2></p><p><h3>Common Issues</h3></p><p><strong>1. Database Connection Issues</strong><br><pre><code><h1>Check database connectivity</h1><br>mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SELECT 1;"</p><p><h1>Check connection pool</h1><br>curl http://localhost:3000/health/database<br></code></pre></p><p><strong>2. Queue Processing Issues</strong><br><pre><code><h1>Check queue status</h1><br>curl http://localhost:3000/admin/queues</p><p><h1>Check Redis connectivity</h1><br>redis-cli -h $REDIS_HOST ping</p><p><h1>Check RabbitMQ</h1><br>rabbitmqctl status<br></code></pre></p><p><strong>3. Memory Issues</strong><br><pre><code><h1>Monitor memory usage</h1><br>docker stats notification-engine</p><p><h1>Check for memory leaks</h1><br>curl http://localhost:3000/metrics | grep nodejs_heap<br></code></pre></p><p><h3>Log Analysis</h3></p><p><pre><code><h1>Application logs</h1><br>docker logs notification-engine --follow</p><p><h1>Structured log queries</h1><br>docker logs notification-engine 2>&1 | jq '.level == "error"'</p><p><h1>Performance logs</h1><br>docker logs notification-engine 2>&1 | jq '.responseTime > 1000'<br></code></pre></p><p><h3>Emergency Procedures</h3></p><p><strong>1. Immediate Scaling</strong><br><pre><code><h1>Scale up immediately</h1><br>docker-compose up --scale app=5</p><p><h1>Kubernetes scaling</h1><br>kubectl scale deployment notification-engine --replicas=10<br></code></pre></p><p><strong>2. Circuit Breaker</strong><br><pre><code><h1>Disable email sending</h1><br>curl -X POST http://localhost:3000/admin/toggle-email -d '{"enabled": false}'</p><p><h1>Pause queue processing</h1><br>curl -X POST http://localhost:3000/admin/pause-queues<br></code></pre></p><p>This deployment guide provides comprehensive instructions for deploying the Notification Engine System across various environments with proper monitoring, scaling, and troubleshooting procedures.<br>
            </article>
        </main>
    </div>
</body>
</html>