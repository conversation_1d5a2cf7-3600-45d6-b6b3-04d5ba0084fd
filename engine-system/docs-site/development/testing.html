<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Guide - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Development Guide › Testing Guide
            </nav>
        </header>

        <main>
            <article>
                <h1>Testing Guide</h1>
                <h1>Testing Guide</h1></p><p>This document provides comprehensive guidance for testing the Notification Engine System, including unit tests, integration tests, and end-to-end testing strategies.</p><p><h2>Table of Contents</h2></p><p>- <a href="#testing-overview">Testing Overview</a><br>- <a href="#test-setup">Test Setup</a><br>- <a href="#unit-testing">Unit Testing</a><br>- <a href="#integration-testing">Integration Testing</a><br>- <a href="#end-to-end-testing">End-to-End Testing</a><br>- <a href="#load-testing">Load Testing</a><br>- <a href="#testing-best-practices">Testing Best Practices</a><br>- <a href="#continuous-integration">Continuous Integration</a></p><p><h2>Testing Overview</h2></p><p>The Notification Engine System uses a comprehensive testing approach with multiple testing layers:</p><p>- <strong>Unit Tests</strong>: Test individual functions, classes, and components in isolation<br>- <strong>Integration Tests</strong>: Test interactions between different components and external services<br>- <strong>End-to-End Tests</strong>: Test complete notification workflows from trigger to delivery<br>- <strong>Load Tests</strong>: Test system performance under various load conditions</p><p><h3>Testing Stack</h3></p><p>- <strong>Jest</strong>: Primary testing framework<br>- <strong>Supertest</strong>: HTTP integration testing<br>- <strong>Testcontainers</strong>: Docker-based integration testing<br>- <strong>Mock Libraries</strong>: Service mocking and stubbing<br>- <strong>Factory Libraries</strong>: Test data generation</p><p><h2>Test Setup</h2></p><p><h3>Installation</h3></p><p><pre><code><h1>Install testing dependencies</h1><br>npm install --save-dev jest @types/jest supertest @types/supertest<br>npm install --save-dev testcontainers mysql2<br>npm install --save-dev jest-mock-extended factory.ts<br></code></pre></p><p><h3>Configuration</h3></p><p><pre><code>// jest.config.js<br>module.exports = {<br>  preset: 'ts-jest',<br>  testEnvironment: 'node',<br>  roots: ['<rootDir>/src', '<rootDir>/tests'],<br>  testMatch: [<br>    '<strong>/__tests__/</strong>/*.test.ts',<br>    '<em></em>/?(*.)+(spec|test).ts'<br>  ],<br>  collectCoverageFrom: [<br>    'src/<em></em>/*.ts',<br>    '!src/<em></em>/*.d.ts',<br>    '!src/generated/<em></em>/*'<br>  ],<br>  coverageDirectory: 'coverage',<br>  coverageReporters: ['text', 'lcov', 'html'],<br>  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],<br>  testTimeout: 30000<br>};<br></code></pre></p><p><h3>Test Environment Setup</h3></p><p><pre><code>// tests/setup.ts<br>import { GenericContainer } from 'testcontainers';<br>import { PrismaClient } from '@prisma/client';</p><p>let prisma: PrismaClient;<br>let mysqlContainer: any;<br>let redisContainer: any;</p><p>beforeAll(async () => {<br>  // Start test databases<br>  mysqlContainer = await new GenericContainer('mysql:8.0')<br>    .withEnvironment({<br>      MYSQL_ROOT_PASSWORD: 'testpass',<br>      MYSQL_DATABASE: 'test_notifications'<br>    })<br>    .withExposedPorts(3306)<br>    .start();</p><p>  redisContainer = await new GenericContainer('redis:7')<br>    .withExposedPorts(6379)<br>    .start();</p><p>  // Setup Prisma client<br>  const DATABASE_URL = <code>mysql://root:testpass@localhost:${mysqlContainer.getMappedPort(3306)}/test_notifications</code>;<br>  process.env.DATABASE_URL = DATABASE_URL;<br>  process.env.REDIS_HOST = 'localhost';<br>  process.env.REDIS_PORT = redisContainer.getMappedPort(6379).toString();</p><p>  prisma = new PrismaClient();<br>  await prisma.$connect();<br>});</p><p>afterAll(async () => {<br>  await prisma.$disconnect();<br>  await mysqlContainer.stop();<br>  await redisContainer.stop();<br>});<br></code></pre></p><p><h2>Unit Testing</h2></p><p><h3>Testing Services</h3></p><p><pre><code>// tests/unit/services/trigger-evaluator.test.ts<br>import { TriggerEvaluator } from '../../../src/services/trigger-evaluator';<br>import { SiteState } from '../../../src/types/site';</p><p>describe('TriggerEvaluator', () => {<br>  let evaluator: TriggerEvaluator;</p><p>  beforeEach(() => {<br>    evaluator = new TriggerEvaluator();<br>  });</p><p>  describe('evaluateCondition', () => {<br>    test('should evaluate simple numeric conditions', () => {<br>      const condition = {<br>        field: 'sessions',<br>        operator: 'greater_than',<br>        value: 100<br>      };<br>      <br>      const siteState: SiteState = {<br>        idSite: 123,<br>        metrics: { sessions: 150 }<br>      };</p><p>      const result = evaluator.evaluateCondition(condition, siteState);<br>      expect(result).toBe(true);<br>    });</p><p>    test('should evaluate date-based conditions', () => {<br>      const condition = {<br>        field: 'last_login_date',<br>        operator: 'days_since',<br>        value: 7<br>      };<br>      <br>      const siteState: SiteState = {<br>        idSite: 123,<br>        lastLoginDate: new Date(Date.now() - 8 <em> 24 </em> 60 <em> 60 </em> 1000)<br>      };</p><p>      const result = evaluator.evaluateCondition(condition, siteState);<br>      expect(result).toBe(true);<br>    });</p><p>    test('should handle missing data gracefully', () => {<br>      const condition = {<br>        field: 'sessions',<br>        operator: 'greater_than',<br>        value: 100<br>      };<br>      <br>      const siteState: SiteState = {<br>        idSite: 123,<br>        metrics: {}<br>      };</p><p>      const result = evaluator.evaluateCondition(condition, siteState);<br>      expect(result).toBe(false);<br>    });<br>  });<br>});<br></code></pre></p><p><h3>Testing Template Engine</h3></p><p><pre><code>// tests/unit/template-engine.test.ts<br>import { TemplateEngine } from '../../src/template-engine';<br>import { mockFs } from 'jest-mock-extended';</p><p>jest.mock('fs', () => mockFs);</p><p>describe('TemplateEngine', () => {<br>  let templateEngine: TemplateEngine;</p><p>  beforeEach(() => {<br>    templateEngine = new TemplateEngine();<br>  });</p><p>  test('should render email template with data', async () => {<br>    const templateContent = <code><br>      <h1>Hello {{userName}}</h1><br>      <p>Your site {{siteName}} has {{sessions}} sessions</p><br>    </code>;<br>    <br>    mockFs.readFileSync.mockReturnValue(templateContent);</p><p>    const result = await templateEngine.render('weekly-summary', 'email', {<br>      userName: 'John Doe',<br>      siteName: 'Test Site',<br>      sessions: 1234<br>    });</p><p>    expect(result.body).toContain('Hello John Doe');<br>    expect(result.body).toContain('Test Site has 1,234 sessions');<br>  });</p><p>  test('should use custom helpers', async () => {<br>    const templateContent = <code><br>      <p>Revenue: {{formatCurrency revenue}}</p><br>      <p>Date: {{formatDate date}}</p><br>    </code>;<br>    <br>    mockFs.readFileSync.mockReturnValue(templateContent);</p><p>    const result = await templateEngine.render('template', 'email', {<br>      revenue: 1234.56,<br>      date: new Date('2023-01-01')<br>    });</p><p>    expect(result.body).toContain('Revenue: $1,234.56');<br>    expect(result.body).toContain('Date: Jan 1, 2023');<br>  });<br>});<br></code></pre></p><p><h3>Testing Queue Jobs</h3></p><p><pre><code>// tests/unit/jobs/notification-job.test.ts<br>import { NotificationJob } from '../../../src/jobs/notification/notification-job';<br>import { createMockContext } from '../../helpers/mock-context';</p><p>describe('NotificationJob', () => {<br>  let notificationJob: NotificationJob;<br>  let mockContext: any;</p><p>  beforeEach(() => {<br>    mockContext = createMockContext();<br>    notificationJob = new NotificationJob();<br>  });</p><p>  test('should process email notification', async () => {<br>    const jobData = {<br>      notificationId: 'test-123',<br>      channel: 'email',<br>      recipient: '<EMAIL>',<br>      templateId: 'weekly-summary',<br>      templateData: {<br>        siteName: 'Test Site',<br>        userName: 'John Doe'<br>      }<br>    };</p><p>    await notificationJob.process(jobData, mockContext);</p><p>    expect(mockContext.emailConnector.send).toHaveBeenCalledWith({<br>      to: '<EMAIL>',<br>      subject: expect.stringContaining('Weekly Summary'),<br>      html: expect.stringContaining('Test Site')<br>    });<br>  });</p><p>  test('should handle job failures gracefully', async () => {<br>    const jobData = {<br>      notificationId: 'test-123',<br>      channel: 'email',<br>      recipient: 'invalid-email',<br>      templateId: 'weekly-summary',<br>      templateData: {}<br>    };</p><p>    mockContext.emailConnector.send.mockRejectedValue(new Error('Invalid email'));</p><p>    await expect(notificationJob.process(jobData, mockContext)).rejects.toThrow('Invalid email');<br>    <br>    // Verify error was logged<br>    expect(mockContext.logger.error).toHaveBeenCalled();<br>  });<br>});<br></code></pre></p><p><h2>Integration Testing</h2></p><p><h3>API Integration Tests</h3></p><p><pre><code>// tests/integration/api/trigger.test.ts<br>import request from 'supertest';<br>import app from '../../../src/app';<br>import { PrismaClient } from '@prisma/client';</p><p>describe('Trigger API', () => {<br>  let prisma: PrismaClient;</p><p>  beforeAll(async () => {<br>    prisma = new PrismaClient();<br>    await prisma.$connect();<br>  });</p><p>  afterAll(async () => {<br>    await prisma.$disconnect();<br>  });</p><p>  beforeEach(async () => {<br>    // Clean up test data<br>    await prisma.notification.deleteMany();<br>    await prisma.trigger.deleteMany();<br>  });</p><p>  describe('POST /trigger/evaluation', () => {<br>    test('should trigger evaluation for specified sites', async () => {<br>      const response = await request(app)<br>        .post('/trigger/evaluation')<br>        .send({<br>          sites: [123, 456],<br>          immediate: true<br>        })<br>        .expect(200);</p><p>      expect(response.body.success).toBe(true);<br>      expect(response.body.evaluationId).toBeDefined();<br>      expect(response.body.sitesQueued).toBe(2);<br>    });</p><p>    test('should validate request body', async () => {<br>      const response = await request(app)<br>        .post('/trigger/evaluation')<br>        .send({<br>          // Missing required fields<br>        })<br>        .expect(400);</p><p>      expect(response.body.error.code).toBe('VALIDATION_ERROR');<br>      expect(response.body.error.details).toContain('sites');<br>    });<br>  });</p><p>  describe('POST /real-time', () => {<br>    test('should process real-time notification', async () => {<br>      // Create test trigger<br>      const trigger = await prisma.trigger.create({<br>        data: {<br>          name: 'test-trigger',<br>          templateId: 'weekly-summary',<br>          channels: ['email'],<br>          conditions: [],<br>          isActive: true<br>        }<br>      });</p><p>      const response = await request(app)<br>        .post('/real-time')<br>        .send({<br>          idSite: 123,<br>          triggerName: 'test-trigger',<br>          templateData: {<br>            siteName: 'Test Site',<br>            userName: 'John Doe'<br>          },<br>          channels: ['email']<br>        })<br>        .expect(200);</p><p>      expect(response.body.success).toBe(true);<br>      expect(response.body.notificationId).toBeDefined();<br>    });<br>  });<br>});<br></code></pre></p><p><h3>Database Integration Tests</h3></p><p><pre><code>// tests/integration/database/models.test.ts<br>import { PrismaClient } from '@prisma/client';<br>import { createTestNotification, createTestTrigger } from '../../helpers/factories';</p><p>describe('Database Models', () => {<br>  let prisma: PrismaClient;</p><p>  beforeAll(async () => {<br>    prisma = new PrismaClient();<br>    await prisma.$connect();<br>  });</p><p>  afterAll(async () => {<br>    await prisma.$disconnect();<br>  });</p><p>  describe('Notification Model', () => {<br>    test('should create notification with all relationships', async () => {<br>      const trigger = await createTestTrigger(prisma);<br>      const notification = await createTestNotification(prisma, {<br>        triggerId: trigger.id<br>      });</p><p>      const retrieved = await prisma.notification.findUnique({<br>        where: { id: notification.id },<br>        include: {<br>          trigger: true,<br>          attempts: true,<br>          auditLogs: true<br>        }<br>      });</p><p>      expect(retrieved).toBeDefined();<br>      expect(retrieved?.trigger.id).toBe(trigger.id);<br>      expect(retrieved?.status).toBe('PENDING');<br>    });</p><p>    test('should handle soft deletes', async () => {<br>      const notification = await createTestNotification(prisma);<br>      <br>      await prisma.notification.update({<br>        where: { id: notification.id },<br>        data: { deletedAt: new Date() }<br>      });</p><p>      const activeNotifications = await prisma.notification.findMany({<br>        where: { deletedAt: null }<br>      });</p><p>      expect(activeNotifications).not.toContainEqual(<br>        expect.objectContaining({ id: notification.id })<br>      );<br>    });<br>  });<br>});<br></code></pre></p><p><h2>End-to-End Testing</h2></p><p><h3>Complete Notification Flow</h3></p><p><pre><code>// tests/e2e/notification-flow.test.ts<br>import { startTestServer } from '../helpers/test-server';<br>import { EmailTestHelper } from '../helpers/email-test-helper';<br>import { QueueTestHelper } from '../helpers/queue-test-helper';</p><p>describe('Complete Notification Flow', () => {<br>  let server: any;<br>  let emailHelper: EmailTestHelper;<br>  let queueHelper: QueueTestHelper;</p><p>  beforeAll(async () => {<br>    server = await startTestServer();<br>    emailHelper = new EmailTestHelper();<br>    queueHelper = new QueueTestHelper();<br>  });</p><p>  afterAll(async () => {<br>    await server.close();<br>  });</p><p>  test('should send weekly summary email', async () => {<br>    // Setup test data<br>    const siteId = 123;<br>    const userEmail = '<EMAIL>';<br>    <br>    // Trigger evaluation<br>    const response = await fetch(<code>${server.url}/trigger/evaluation</code>, {<br>      method: 'POST',<br>      headers: { 'Content-Type': 'application/json' },<br>      body: JSON.stringify({<br>        sites: [siteId],<br>        immediate: true,<br>        triggerName: 'weekly-summary'<br>      })<br>    });</p><p>    expect(response.ok).toBe(true);</p><p>    // Wait for queue processing<br>    await queueHelper.waitForJobCompletion('evaluation-queue');<br>    await queueHelper.waitForJobCompletion('notification-queue');</p><p>    // Verify email was sent<br>    const sentEmails = await emailHelper.getSentEmails();<br>    const weeklyEmail = sentEmails.find(email => <br>      email.to === userEmail && <br>      email.subject.includes('Weekly Summary')<br>    );</p><p>    expect(weeklyEmail).toBeDefined();<br>    expect(weeklyEmail?.html).toContain('Test Site');<br>    expect(weeklyEmail?.html).toContain('metrics');<br>  });</p><p>  test('should handle Slack notifications', async () => {<br>    const slackHelper = new SlackTestHelper();<br>    <br>    // Trigger Slack notification<br>    const response = await fetch(<code>${server.url}/real-time</code>, {<br>      method: 'POST',<br>      headers: { 'Content-Type': 'application/json' },<br>      body: JSON.stringify({<br>        idSite: 123,<br>        triggerName: 'installation-complete',<br>        channels: ['slack'],<br>        templateData: {<br>          siteName: 'Test Site',<br>          userName: 'John Doe'<br>        }<br>      })<br>    });</p><p>    expect(response.ok).toBe(true);</p><p>    // Wait for processing<br>    await queueHelper.waitForJobCompletion('notification-queue');</p><p>    // Verify Slack message<br>    const slackMessages = await slackHelper.getPostedMessages();<br>    expect(slackMessages).toHaveLength(1);<br>    expect(slackMessages[0].text).toContain('Installation Complete');<br>  });<br>});<br></code></pre></p><p><h2>Load Testing</h2></p><p><h3>Queue Performance Tests</h3></p><p><pre><code>// tests/load/queue-performance.test.ts<br>import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';<br>import { EvaluationQueue } from '../../src/jobs/evaluation/evaluation-queue';</p><p>describe('Queue Load Testing', () => {<br>  let evaluationQueue: EvaluationQueue;</p><p>  beforeAll(async () => {<br>    evaluationQueue = EvaluationQueue.getInstance();<br>  });</p><p>  test('should handle high volume of jobs', async () => {<br>    const jobCount = 1000;<br>    const jobs = [];</p><p>    // Add many jobs simultaneously<br>    for (let i = 0; i < jobCount; i++) {<br>      jobs.push(evaluationQueue.add(<code>test-job-${i}</code>, {<br>        siteId: i,<br>        triggerName: 'test-trigger'<br>      }));<br>    }</p><p>    const startTime = Date.now();<br>    await Promise.all(jobs);<br>    const addTime = Date.now() - startTime;</p><p>    // Wait for processing<br>    const processStartTime = Date.now();<br>    await evaluationQueue.whenCurrentJobsFinished();<br>    const processTime = Date.now() - processStartTime;</p><p>    // Performance assertions<br>    expect(addTime).toBeLessThan(5000); // Should add 1000 jobs in under 5 seconds<br>    expect(processTime).toBeLessThan(30000); // Should process in under 30 seconds</p><p>    console.log(<code>Added ${jobCount} jobs in ${addTime}ms</code>);<br>    console.log(<code>Processed ${jobCount} jobs in ${processTime}ms</code>);<br>  });<br>});<br></code></pre></p><p><h3>API Load Tests</h3></p><p><pre><code>// tests/load/api-performance.test.ts<br>import autocannon from 'autocannon';<br>import { startTestServer } from '../helpers/test-server';</p><p>describe('API Load Testing', () => {<br>  let server: any;</p><p>  beforeAll(async () => {<br>    server = await startTestServer();<br>  });</p><p>  afterAll(async () => {<br>    await server.close();<br>  });</p><p>  test('should handle concurrent trigger evaluations', async () => {<br>    const result = await autocannon({<br>      url: <code>${server.url}/trigger/evaluation</code>,<br>      method: 'POST',<br>      headers: {<br>        'Content-Type': 'application/json'<br>      },<br>      body: JSON.stringify({<br>        sites: [123],<br>        immediate: true<br>      }),<br>      connections: 10,<br>      duration: 30 // 30 seconds<br>    });</p><p>    // Performance assertions<br>    expect(result.errors).toBe(0);<br>    expect(result.timeouts).toBe(0);<br>    expect(result.non2xx).toBe(0);<br>    expect(result.latency.average).toBeLessThan(1000); // Average response under 1 second</p><p>    console.log(<code>Requests per second: ${result.requests.average}</code>);<br>    console.log(<code>Average latency: ${result.latency.average}ms</code>);<br>  });<br>});<br></code></pre></p><p><h2>Testing Best Practices</h2></p><p><h3>Test Organization</h3></p><p><pre><code>// tests/helpers/factories.ts<br>import { PrismaClient } from '@prisma/client';</p><p>export const createTestTrigger = async (prisma: PrismaClient, overrides = {}) => {<br>  return prisma.trigger.create({<br>    data: {<br>      name: 'test-trigger',<br>      templateId: 'weekly-summary',<br>      channels: ['email'],<br>      conditions: [],<br>      isActive: true,<br>      ...overrides<br>    }<br>  });<br>};</p><p>export const createTestNotification = async (prisma: PrismaClient, overrides = {}) => {<br>  return prisma.notification.create({<br>    data: {<br>      idSite: 123,<br>      templateId: 'weekly-summary',<br>      channel: 'email',<br>      recipient: '<EMAIL>',<br>      status: 'PENDING',<br>      templateData: {},<br>      ...overrides<br>    }<br>  });<br>};<br></code></pre></p><p><h3>Mock Helpers</h3></p><p><pre><code>// tests/helpers/mock-context.ts<br>export const createMockContext = () => ({<br>  emailConnector: {<br>    send: jest.fn().mockResolvedValue({ messageId: 'test-123' })<br>  },<br>  slackConnector: {<br>    postMessage: jest.fn().mockResolvedValue({ ok: true })<br>  },<br>  templateEngine: {<br>    render: jest.fn().mockResolvedValue({<br>      subject: 'Test Subject',<br>      body: 'Test Body'<br>    })<br>  },<br>  logger: {<br>    info: jest.fn(),<br>    error: jest.fn(),<br>    warn: jest.fn(),<br>    debug: jest.fn()<br>  }<br>});<br></code></pre></p><p><h3>Test Utilities</h3></p><p><pre><code>// tests/helpers/test-server.ts<br>import app from '../../src/app';<br>import { Server } from 'http';</p><p>export const startTestServer = async (port = 0): Promise<{<br>  server: Server;<br>  url: string;<br>  close: () => Promise<void>;<br>}> => {<br>  return new Promise((resolve) => {<br>    const server = app.listen(port, () => {<br>      const address = server.address();<br>      const actualPort = typeof address === 'object' ? address?.port : port;<br>      <br>      resolve({<br>        server,<br>        url: <code>http://localhost:${actualPort}</code>,<br>        close: () => new Promise(resolve => server.close(resolve))<br>      });<br>    });<br>  });<br>};<br></code></pre></p><p><h2>Continuous Integration</h2></p><p><h3>GitHub Actions Configuration</h3></p><p><pre><code><h1>.github/workflows/test.yml</h1><br>name: Tests</p><p>on:<br>  push:<br>    branches: [ main, develop ]<br>  pull_request:<br>    branches: [ main ]</p><p>jobs:<br>  test:<br>    runs-on: ubuntu-latest<br>    <br>    services:<br>      mysql:<br>        image: mysql:8.0<br>        env:<br>          MYSQL_ROOT_PASSWORD: password<br>          MYSQL_DATABASE: test_notifications<br>        ports:<br>          - 3306:3306<br>        options: >-<br>          --health-cmd="mysqladmin ping"<br>          --health-interval=10s<br>          --health-timeout=5s<br>          --health-retries=3<br>      <br>      redis:<br>        image: redis:7<br>        ports:<br>          - 6379:6379<br>        options: >-<br>          --health-cmd="redis-cli ping"<br>          --health-interval=10s<br>          --health-timeout=5s<br>          --health-retries=3</p><p>    steps:<br>    - uses: actions/checkout@v3<br>    <br>    - name: Setup Node.js<br>      uses: actions/setup-node@v3<br>      with:<br>        node-version: '18'<br>        cache: 'npm'<br>    <br>    - name: Install dependencies<br>      run: npm ci<br>    <br>    - name: Generate Prisma Client<br>      run: npx prisma generate<br>    <br>    - name: Run database migrations<br>      run: npx prisma migrate deploy<br>      env:<br>        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications<br>    <br>    - name: Run unit tests<br>      run: npm run test:unit<br>      env:<br>        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications<br>        REDIS_HOST: localhost<br>        REDIS_PORT: 6379<br>    <br>    - name: Run integration tests<br>      run: npm run test:integration<br>      env:<br>        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications<br>        REDIS_HOST: localhost<br>        REDIS_PORT: 6379<br>    <br>    - name: Upload coverage reports<br>      uses: codecov/codecov-action@v3<br>      with:<br>        file: ./coverage/lcov.info<br></code></pre></p><p><h3>Test Scripts</h3></p><p><pre><code>{<br>  "scripts": {<br>    "test": "jest",<br>    "test:unit": "jest tests/unit",<br>    "test:integration": "jest tests/integration",<br>    "test:e2e": "jest tests/e2e",<br>    "test:load": "jest tests/load",<br>    "test:watch": "jest --watch",<br>    "test:coverage": "jest --coverage",<br>    "test:ci": "jest --ci --coverage --watchAll=false"<br>  }<br>}<br></code></pre></p><p><h3>Coverage Requirements</h3></p><p><pre><code>// jest.config.js<br>module.exports = {<br>  // ... other configuration<br>  coverageThreshold: {<br>    global: {<br>      branches: 80,<br>      functions: 80,<br>      lines: 80,<br>      statements: 80<br>    },<br>    './src/services/': {<br>      branches: 90,<br>      functions: 90,<br>      lines: 90,<br>      statements: 90<br>    }<br>  }<br>};<br></code></pre></p><p>This comprehensive testing guide provides the foundation for maintaining high code quality and system reliability in the Notification Engine System. Follow these patterns and adapt them to your specific testing needs.<br>
            </article>
        </main>
    </div>
</body>
</html>