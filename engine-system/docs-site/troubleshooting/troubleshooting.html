<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Troubleshooting Guide - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Troubleshooting › Troubleshooting Guide
            </nav>
        </header>

        <main>
            <article>
                <h1>Troubleshooting Guide</h1>
                <h1>Troubleshooting Guide & FAQ</h1></p><p>This document provides solutions to common issues, debugging techniques, and frequently asked questions for the Notification Engine System.</p><p><h2>Table of Contents</h2></p><p>- <a href="#quick-diagnostics">Quick Diagnostics</a><br>- <a href="#common-issues">Common Issues</a><br>- <a href="#database-issues">Database Issues</a><br>- <a href="#queue-system-issues">Queue System Issues</a><br>- <a href="#template-issues">Template Issues</a><br>- <a href="#api-issues">API Issues</a><br>- <a href="#performance-issues">Performance Issues</a><br>- <a href="#security-issues">Security Issues</a><br>- <a href="#monitoring--debugging">Monitoring & Debugging</a><br>- <a href="#frequently-asked-questions">FAQ</a></p><p><h2>Quick Diagnostics</h2></p><p><h3>Health Check Commands</h3></p><p><pre><code><h1>Basic application health</h1><br>curl http://localhost:3000/up</p><p><h1>Detailed health check</h1><br>curl http://localhost:3000/health</p><p><h1>Queue status</h1><br>curl http://localhost:3000/admin/queues</p><p><h1>Database connectivity</h1><br>curl http://localhost:3000/health/database<br></code></pre></p><p><h3>Log Analysis</h3></p><p><pre><code><h1>Real-time application logs</h1><br>docker logs notification-engine --follow</p><p><h1>Filter error logs</h1><br>docker logs notification-engine 2>&1 | grep "ERROR"</p><p><h1>Structured log queries with jq</h1><br>docker logs notification-engine 2>&1 | jq '.level == "error"'</p><p><h1>Performance monitoring</h1><br>docker logs notification-engine 2>&1 | jq '.responseTime > 1000'<br></code></pre></p><p><h3>Resource Monitoring</h3></p><p><pre><code><h1>Container resource usage</h1><br>docker stats notification-engine</p><p><h1>Memory usage breakdown</h1><br>docker exec notification-engine node -e "console.log(process.memoryUsage())"</p><p><h1>CPU and memory limits</h1><br>docker inspect notification-engine | jq '.[0].HostConfig'<br></code></pre></p><p><h2>Common Issues</h2></p><p><h3>1. Application Won't Start</h3></p><p><strong>Symptoms:</strong><br>- Container exits immediately<br>- "Connection refused" errors<br>- Environment validation failures</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check container logs</h1><br>docker logs notification-engine</p><p><h1>Verify environment variables</h1><br>docker exec notification-engine env | grep -E "(DATABASE_URL|REDIS_HOST|RABBITMQ_HOST)"</p><p><h1>Test database connection</h1><br>docker exec notification-engine npx prisma db push --dry-run<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code><h1>Fix environment variables</h1><br>cp env.sample .env<br><h1>Edit .env with proper values</h1></p><p><h1>Test database connectivity</h1><br>mysql -h localhost -u root -p -e "SELECT 1;"</p><p><h1>Regenerate Prisma client</h1><br>docker exec notification-engine npx prisma generate</p><p><h1>Reset database if needed</h1><br>docker exec notification-engine npx prisma migrate reset<br></code></pre></p><p><h3>2. High Memory Usage</h3></p><p><strong>Symptoms:</strong><br>- Container getting killed (OOMKilled)<br>- Slow response times<br>- Memory warnings in logs</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check memory usage patterns</h1><br>docker stats notification-engine --no-stream</p><p><h1>Analyze heap dump (if available)</h1><br>docker exec notification-engine node --inspect-brk=0.0.0.0:9229 dist/server.js</p><p><h1>Memory leak detection</h1><br>curl http://localhost:3000/metrics | grep nodejs_heap<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Add to server.ts - Memory monitoring<br>setInterval(() => {<br>  const usage = process.memoryUsage();<br>  if (usage.heapUsed > 500 <em> 1024 </em> 1024) { // 500MB threshold<br>    console.warn('High memory usage:', usage);<br>  }<br>}, 30000);</p><p>// Force garbage collection periodically<br>if (global.gc) {<br>  setInterval(() => {<br>    global.gc();<br>  }, 60000);<br>}<br></code></pre></p><p><pre><code><h1>Docker memory limits</h1><br>services:<br>  app:<br>    deploy:<br>      resources:<br>        limits:<br>          memory: 1G<br>        reservations:<br>          memory: 512M<br></code></pre></p><p><h3>3. Slow API Response Times</h3></p><p><strong>Symptoms:</strong><br>- API calls taking > 5 seconds<br>- Timeout errors<br>- Queue processing delays</p><p><strong>Diagnostics:</strong><br><pre><code><h1>API response time testing</h1><br>curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3000/trigger/evaluation</p><p><h1>Database query analysis</h1><br>docker exec mysql mysql -u root -p -e "SHOW PROCESSLIST;"<br>docker exec mysql mysql -u root -p -e "SHOW ENGINE INNODB STATUS\G"</p><p><h1>Queue processing times</h1><br>curl http://localhost:3000/admin/queues | jq '.queues[].processing_time'<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>-- Add database indexes<br>CREATE INDEX idx_notifications_created_at ON notifications(created_at);<br>CREATE INDEX idx_notifications_status ON notifications(status);<br>CREATE INDEX idx_triggers_active ON triggers(is_active);</p><p>-- Optimize queries<br>ANALYZE TABLE notifications;<br>OPTIMIZE TABLE notifications;<br></code></pre></p><p><pre><code>// Add query optimization<br>const notifications = await prisma.notification.findMany({<br>  where: { status: 'PENDING' },<br>  select: { id: true, recipient: true }, // Only select needed fields<br>  take: 100, // Limit results<br>  orderBy: { createdAt: 'asc' }<br>});<br></code></pre></p><p><h2>Database Issues</h2></p><p><h3>Connection Pool Exhaustion</h3></p><p><strong>Symptoms:</strong><br>- "Too many connections" errors<br>- Slow database queries<br>- Connection timeout errors</p><p><strong>Diagnostics:</strong><br><pre><code>-- Check current connections<br>SHOW STATUS LIKE 'Threads_connected';<br>SHOW STATUS LIKE 'Max_used_connections';</p><p>-- Check connection pool settings<br>SHOW VARIABLES LIKE 'max_connections';<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Optimize Prisma connection pool<br>const prisma = new PrismaClient({<br>  datasources: {<br>    db: {<br>      url: process.env.DATABASE_URL<br>    }<br>  },<br>  log: ['query', 'info', 'warn', 'error'],<br>});</p><p>// Connection pool configuration<br>DATABASE_URL="mysql://user:password@localhost:3306/db?connection_limit=10&pool_timeout=20"<br></code></pre></p><p><pre><code>-- Increase MySQL connection limit<br>SET GLOBAL max_connections = 500;</p><p>-- Add to my.cnf<br>[mysqld]<br>max_connections = 500<br>innodb_buffer_pool_size = 1G<br></code></pre></p><p><h3>Migration Issues</h3></p><p><strong>Symptoms:</strong><br>- Schema drift warnings<br>- Migration conflicts<br>- Data loss during migrations</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check migration status</h1><br>npx prisma migrate status</p><p><h1>View pending migrations</h1><br>npx prisma migrate diff --from-schema-datamodel prisma/schema.prisma</p><p><h1>Database schema inspection</h1><br>npx prisma db pull<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code><h1>Resolve migration conflicts</h1><br>npx prisma migrate resolve --applied "migration-name"</p><p><h1>Reset database (development only)</h1><br>npx prisma migrate reset</p><p><h1>Create and apply migration</h1><br>npx prisma migrate dev --name "fix-schema"</p><p><h1>Deploy to production</h1><br>npx prisma migrate deploy<br></code></pre></p><p><h3>Data Consistency Issues</h3></p><p><strong>Symptoms:</strong><br>- Duplicate notifications<br>- Missing foreign key relationships<br>- Orphaned records</p><p><strong>Diagnostics:</strong><br><pre><code>-- Find orphaned notifications<br>SELECT n.id, n.trigger_id<br>FROM notifications n<br>LEFT JOIN triggers t ON n.trigger_id = t.id<br>WHERE t.id IS NULL;</p><p>-- Check for duplicates<br>SELECT recipient, template_id, COUNT(*)<br>FROM notifications<br>GROUP BY recipient, template_id<br>HAVING COUNT(*) > 1;<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>-- Clean up orphaned records<br>DELETE FROM notifications<br>WHERE trigger_id NOT IN (SELECT id FROM triggers);</p><p>-- Add constraints<br>ALTER TABLE notifications<br>ADD CONSTRAINT fk_notification_trigger<br>FOREIGN KEY (trigger_id) REFERENCES triggers(id) ON DELETE CASCADE;</p><p>-- Fix duplicate prevention<br>CREATE UNIQUE INDEX idx_unique_notification<br>ON notifications(recipient, template_id, site_id, created_at);<br></code></pre></p><p><h2>Queue System Issues</h2></p><p><h3>Redis Connection Issues</h3></p><p><strong>Symptoms:</strong><br>- Queue jobs not processing<br>- Redis connection errors<br>- BullMQ dashboard not loading</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Test Redis connectivity</h1><br>redis-cli -h localhost -p 6379 ping</p><p><h1>Check Redis memory usage</h1><br>redis-cli info memory</p><p><h1>Monitor Redis commands</h1><br>redis-cli monitor<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code><h1>Redis configuration tuning</h1><br><h1>In redis.conf</h1><br>maxmemory 2gb<br>maxmemory-policy allkeys-lru<br>timeout 300</p><p><h1>Restart Redis</h1><br>docker restart redis</p><p><h1>Clear Redis cache if needed</h1><br>redis-cli flushall<br></code></pre></p><p><pre><code>// Redis connection with retry logic<br>const redis = new Redis({<br>  host: process.env.REDIS_HOST,<br>  port: Number(process.env.REDIS_PORT),<br>  retryDelayOnFailover: 100,<br>  maxRetriesPerRequest: 3,<br>  connectTimeout: 10000,<br>  commandTimeout: 5000<br>});<br></code></pre></p><p><h3>RabbitMQ Connection Issues</h3></p><p><strong>Symptoms:</strong><br>- Messages not being consumed<br>- Connection timeout errors<br>- Queue backing up</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check RabbitMQ status</h1><br>rabbitmqctl status</p><p><h1>List queues and their status</h1><br>rabbitmqctl list_queues name messages consumers</p><p><h1>Check connections</h1><br>rabbitmqctl list_connections<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code><h1>Restart RabbitMQ</h1><br>docker restart rabbitmq</p><p><h1>Purge stuck queues</h1><br>rabbitmqctl purge_queue evaluation_queue</p><p><h1>Reset RabbitMQ (development only)</h1><br>rabbitmqctl reset<br></code></pre></p><p><pre><code>// RabbitMQ connection with retry<br>const connection = await amqp.connect(RABBITMQ_URL, {<br>  heartbeat: 60,<br>  connection_timeout: 10000,<br>  clientProperties: {<br>    connection_name: 'notification-engine'<br>  }<br>});</p><p>connection.on('error', (err) => {<br>  console.error('RabbitMQ connection error:', err);<br>  // Implement reconnection logic<br>});<br></code></pre></p><p><h3>Queue Job Failures</h3></p><p><strong>Symptoms:</strong><br>- Jobs stuck in "failed" state<br>- High retry counts<br>- Processing timeouts</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check failed jobs</h1><br>curl http://localhost:3000/admin/queues | jq '.queues[].failed'</p><p><h1>Get job details</h1><br>curl http://localhost:3000/admin/queues/evaluation/failed<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Improve job error handling<br>export class NotificationJob {<br>  async process(data: any) {<br>    try {<br>      await this.sendNotification(data);<br>    } catch (error) {<br>      if (error.code === 'RATE_LIMIT') {<br>        // Retry after delay<br>        throw new Error('Rate limited, retry later');<br>      } else if (error.code === 'INVALID_EMAIL') {<br>        // Don't retry for permanent failures<br>        throw new Error(<code>Permanent failure: ${error.message}</code>);<br>      }<br>      throw error;<br>    }<br>  }<br>}</p><p>// Configure job options<br>const jobOptions = {<br>  attempts: 3,<br>  backoff: {<br>    type: 'exponential',<br>    delay: 5000,<br>  },<br>  removeOnComplete: 10,<br>  removeOnFail: 5<br>};<br></code></pre></p><p><h2>Template Issues</h2></p><p><h3>Template Rendering Errors</h3></p><p><strong>Symptoms:</strong><br>- Email/Slack notifications not rendering<br>- Handlebars compilation errors<br>- Missing template data</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Test template rendering</h1><br>curl -X POST http://localhost:3000/admin/test-template \<br>  -H "Content-Type: application/json" \<br>  -d '{<br>    "templateId": "weekly-summary",<br>    "channel": "email",<br>    "data": {"siteName": "Test"}<br>  }'<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Add template validation<br>export const validateTemplateData = (templateId: string, data: any) => {<br>  const requiredFields = templateRegistry[templateId]?.requiredFields || [];<br>  const missing = requiredFields.filter(field => !data[field]);</p><p>  if (missing.length > 0) {<br>    throw new Error(<code>Missing required fields: ${missing.join(', ')}</code>);<br>  }<br>};</p><p>// Safe template rendering<br>export const renderTemplate = async (templateId: string, data: any) => {<br>  try {<br>    validateTemplateData(templateId, data);<br>    return await templateEngine.render(templateId, data);<br>  } catch (error) {<br>    console.error(<code>Template rendering failed for ${templateId}:</code>, error);<br>    throw new Error(<code>Template rendering failed: ${error.message}</code>);<br>  }<br>};<br></code></pre></p><p><h3>Missing Template Files</h3></p><p><strong>Symptoms:</strong><br>- "Template not found" errors<br>- 404 errors for template endpoints<br>- Blank notification content</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check template files</h1><br>ls -la src/new-templates/<br>ls -la src/slack-templates/</p><p><h1>Verify template registry</h1><br>node -e "console.log(require('./dist/template-registry').templateRegistry)"<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code><h1>Copy missing templates</h1><br>cp -r templates/* src/new-templates/<br>cp -r slack-templates/* src/slack-templates/</p><p><h1>Rebuild with templates</h1><br>npm run build<br></code></pre></p><p><pre><code>// Add template existence check<br>const checkTemplateExists = (templateId: string, channel: string) => {<br>  const templatePath = path.join(<br>    __dirname,<br>    <code>../templates/${channel}</code>,<br>    <code>${templateId}.hbs</code><br>  );</p><p>  if (!fs.existsSync(templatePath)) {<br>    throw new Error(<code>Template not found: ${templateId} for ${channel}</code>);<br>  }<br>};<br></code></pre></p><p><h2>API Issues</h2></p><p><h3>Authentication Problems</h3></p><p><strong>Symptoms:</strong><br>- 401 Unauthorized errors<br>- API key validation failures<br>- Session timeout issues</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Test API authentication</h1><br>curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/triggers</p><p><h1>Check token validity</h1><br>curl -X POST http://localhost:3000/auth/validate \<br>  -H "Authorization: Bearer YOUR_TOKEN"<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Improve auth middleware<br>export const authenticateRequest = async (req: Request, res: Response, next: NextFunction) => {<br>  try {<br>    const token = req.headers.authorization?.replace('Bearer ', '');</p><p>    if (!token) {<br>      return res.status(401).json({ error: 'Missing authorization token' });<br>    }</p><p>    const decoded = jwt.verify(token, process.env.JWT_SECRET!);<br>    req.user = decoded;<br>    next();<br>  } catch (error) {<br>    console.error('Auth error:', error);<br>    return res.status(401).json({ error: 'Invalid token' });<br>  }<br>};<br></code></pre></p><p><h3>Rate Limiting Issues</h3></p><p><strong>Symptoms:</strong><br>- 429 Too Many Requests errors<br>- API calls being blocked<br>- Legitimate traffic getting limited</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check rate limit headers</h1><br>curl -I http://localhost:3000/api/triggers</p><p><h1>Monitor rate limit metrics</h1><br>curl http://localhost:3000/metrics | grep rate_limit<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Configurable rate limiting<br>import rateLimit from 'express-rate-limit';</p><p>const createRateLimit = (windowMs: number, max: number) =><br>  rateLimit({<br>    windowMs,<br>    max,<br>    message: 'Too many requests, please try again later',<br>    standardHeaders: true,<br>    legacyHeaders: false,<br>  });</p><p>// Different limits for different endpoints<br>app.use('/api/triggers', createRateLimit(15 <em> 60 </em> 1000, 100)); // 100 requests per 15 minutes<br>app.use('/real-time', createRateLimit(60 * 1000, 10)); // 10 requests per minute<br></code></pre></p><p><h2>Performance Issues</h2></p><p><h3>High CPU Usage</h3></p><p><strong>Symptoms:</strong><br>- Container using > 80% CPU<br>- Slow API responses<br>- Queue processing delays</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Monitor CPU usage</h1><br>docker stats notification-engine --no-stream</p><p><h1>Profile Node.js application</h1><br>docker exec notification-engine node --prof dist/server.js</p><p><h1>Analyze CPU usage patterns</h1><br>top -p $(docker inspect -f '{{.State.Pid}}' notification-engine)<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Add CPU monitoring<br>const os = require('os');</p><p>setInterval(() => {<br>  const load = os.loadavg();<br>  if (load[0] > os.cpus().length) {<br>    console.warn('High CPU load detected:', load);<br>  }<br>}, 30000);</p><p>// Optimize heavy operations<br>const processInBatches = async (items: any[], batchSize = 10) => {<br>  for (let i = 0; i < items.length; i += batchSize) {<br>    const batch = items.slice(i, i + batchSize);<br>    await Promise.all(batch.map(processItem));</p><p>    // Allow event loop to process other tasks<br>    await new Promise(resolve => setImmediate(resolve));<br>  }<br>};<br></code></pre></p><p><h3>Memory Leaks</h3></p><p><strong>Symptoms:</strong><br>- Steadily increasing memory usage<br>- Out of memory errors<br>- Container restarts</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Track memory over time</h1><br>while true; do<br>  docker stats notification-engine --no-stream | grep notification-engine<br>  sleep 60<br>done</p><p><h1>Generate heap snapshot</h1><br>kill -USR2 $(docker inspect -f '{{.State.Pid}}' notification-engine)<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Add memory monitoring<br>const monitorMemory = () => {<br>  const usage = process.memoryUsage();<br>  const threshold = 800 <em> 1024 </em> 1024; // 800MB</p><p>  if (usage.heapUsed > threshold) {<br>    console.warn('Memory usage high:', usage);</p><p>    // Force garbage collection if available<br>    if (global.gc) {<br>      global.gc();<br>    }<br>  }<br>};</p><p>setInterval(monitorMemory, 60000);</p><p>// Proper event listener cleanup<br>const cleanup = () => {<br>  // Remove event listeners<br>  process.removeAllListeners();</p><p>  // Close database connections<br>  prisma.$disconnect();</p><p>  // Close Redis connections<br>  redis.disconnect();<br>};</p><p>process.on('SIGTERM', cleanup);<br>process.on('SIGINT', cleanup);<br></code></pre></p><p><h2>Security Issues</h2></p><p><h3>API Security</h3></p><p><strong>Symptoms:</strong><br>- Unauthorized access attempts<br>- SQL injection attempts<br>- Brute force attacks</p><p><strong>Diagnostics:</strong><br><pre><code><h1>Check access logs</h1><br>docker logs notification-engine | grep -E "(401|403|429)"</p><p><h1>Monitor for suspicious activity</h1><br>docker logs notification-engine | grep -E "DROP TABLE|UNION SELECT|<script"<br></code></pre></p><p><strong>Solutions:</strong></p><p><pre><code>// Security middleware stack<br>import helmet from 'helmet';<br>import rateLimit from 'express-rate-limit';<br>import validator from 'validator';</p><p>app.use(helmet());<br>app.use(rateLimit({<br>  windowMs: 15 <em> 60 </em> 1000,<br>  max: 100<br>}));</p><p>// Input validation<br>const validateInput = (req: Request, res: Response, next: NextFunction) => {<br>  // Sanitize strings<br>  for (const [key, value] of Object.entries(req.body)) {<br>    if (typeof value === 'string') {<br>      req.body[key] = validator.escape(value);<br>    }<br>  }<br>  next();<br>};</p><p>app.use(validateInput);<br></code></pre></p><p><h3>Environment Security</h3></p><p><strong>Symptoms:</strong><br>- Exposed sensitive information<br>- Weak authentication<br>- Unencrypted connections</p><p><strong>Solutions:</strong></p><p><pre><code><h1>Use proper secrets management</h1><br>export DATABASE_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/db/password --query SecretString --output text)</p><p><h1>Enable SSL/TLS</h1><br>export DATABASE_URL="mysql://user:password@host:3306/db?ssl=true&sslcert=/path/to/cert.pem"</p><p><h1>Rotate secrets regularly</h1><br>aws secretsmanager rotate-secret --secret-id prod/api/keys<br></code></pre></p><p><h2>Monitoring & Debugging</h2></p><p><h3>Application Monitoring</h3></p><p><pre><code>// Advanced logging setup<br>import winston from 'winston';</p><p>const logger = winston.createLogger({<br>  level: 'info',<br>  format: winston.format.combine(<br>    winston.format.timestamp(),<br>    winston.format.errors({ stack: true }),<br>    winston.format.json()<br>  ),<br>  transports: [<br>    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),<br>    new winston.transports.File({ filename: 'logs/combined.log' }),<br>    new winston.transports.Console({<br>      format: winston.format.simple()<br>    })<br>  ]<br>});</p><p>// Request logging middleware<br>app.use((req, res, next) => {<br>  const start = Date.now();</p><p>  res.on('finish', () => {<br>    const duration = Date.now() - start;<br>    logger.info({<br>      method: req.method,<br>      url: req.url,<br>      status: res.statusCode,<br>      duration,<br>      userAgent: req.get('User-Agent'),<br>      ip: req.ip<br>    });<br>  });</p><p>  next();<br>});<br></code></pre></p><p><h3>Performance Monitoring</h3></p><p><pre><code>// Performance tracking<br>const performanceTracker = {<br>  track: (operation: string, fn: Function) => {<br>    return async (...args: any[]) => {<br>      const start = process.hrtime.bigint();</p><p>      try {<br>        const result = await fn(...args);<br>        const end = process.hrtime.bigint();<br>        const duration = Number(end - start) / 1000000; // Convert to milliseconds</p><p>        logger.info({<br>          operation,<br>          duration,<br>          status: 'success'<br>        });</p><p>        return result;<br>      } catch (error) {<br>        const end = process.hrtime.bigint();<br>        const duration = Number(end - start) / 1000000;</p><p>        logger.error({<br>          operation,<br>          duration,<br>          status: 'error',<br>          error: error.message<br>        });</p><p>        throw error;<br>      }<br>    };<br>  }<br>};</p><p>// Usage<br>const sendEmail = performanceTracker.track('sendEmail', async (data) => {<br>  // Email sending logic<br>});<br></code></pre></p><p><h2>Frequently Asked Questions</h2></p><p><h3>Q: Why are notifications not being sent?</h3></p><p><strong>A:</strong> Check the following in order:<br>1. Verify <code>EMAIL_ON=true</code> in environment variables<br>2. Check SMTP configuration and credentials<br>3. Verify queue processing is running<br>4. Check for failed jobs in the queue dashboard<br>5. Verify template exists and renders correctly</p><p><h3>Q: How do I add a new notification template?</h3></p><p><strong>A:</strong> Follow these steps:<br>1. Create template files in <code>src/new-templates/</code> (email) and <code>src/slack-templates/</code> (Slack)<br>2. Add template definition to <code>template-registry.ts</code><br>3. Define validation schema in the registry<br>4. Test template rendering<br>5. Deploy and verify</p><p><h3>Q: Why is the queue processing slowly?</h3></p><p><strong>A:</strong> Common causes:<br>1. Redis memory exhaustion - check with <code>redis-cli info memory</code><br>2. Database connection pool exhaustion<br>3. Rate limiting from external services (SMTP, Slack)<br>4. Insufficient worker processes<br>5. Resource constraints (CPU/memory)</p><p><h3>Q: How do I scale the application?</h3></p><p><strong>A:</strong> Scaling options:<br>1. <strong>Horizontal scaling</strong>: Deploy multiple instances behind a load balancer<br>2. <strong>Database scaling</strong>: Use read replicas for read-heavy operations<br>3. <strong>Queue scaling</strong>: Use Redis Cluster and multiple RabbitMQ nodes<br>4. <strong>Caching</strong>: Implement Redis caching for frequently accessed data</p><p><h3>Q: How do I backup and restore data?</h3></p><p><strong>A:</strong> Backup procedures:<br><pre><code><h1>Database backup</h1><br>mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD notifications > backup.sql</p><p><h1>Redis backup</h1><br>redis-cli --rdb backup.rdb</p><p><h1>Restore database</h1><br>mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD notifications < backup.sql<br></code></pre></p><p><h3>Q: How do I monitor application health?</h3></p><p><strong>A:</strong> Use these endpoints and tools:<br>- <code>/up</code> - Basic health check<br>- <code>/health</code> - Detailed health with service status<br>- <code>/admin/queues</code> - Queue monitoring<br>- <code>/metrics</code> - Prometheus metrics<br>- Docker logs and stats for resource monitoring</p><p><h3>Q: What's the disaster recovery procedure?</h3></p><p><strong>A:</strong> Recovery steps:<br>1. <strong>Assess the situation</strong>: Check logs and monitoring alerts<br>2. <strong>Scale up resources</strong>: Increase container count if needed<br>3. <strong>Database recovery</strong>: Restore from backup if corrupted<br>4. <strong>Queue recovery</strong>: Purge stuck queues and restart processing<br>5. <strong>Verify functionality</strong>: Run health checks and test notifications<br>6. <strong>Monitor closely</strong>: Watch for recurring issues</p><p><h3>Q: How do I update to a new version?</h3></p><p><strong>A:</strong> Update procedure:<br>1. <strong>Test in staging</strong>: Deploy to staging environment first<br>2. <strong>Backup data</strong>: Create database and Redis backups<br>3. <strong>Run migrations</strong>: Apply any database schema changes<br>4. <strong>Deploy gradually</strong>: Use blue-green or rolling deployment<br>5. <strong>Monitor health</strong>: Watch logs and metrics during deployment<br>6. <strong>Rollback if needed</strong>: Have rollback plan ready</p><p>This troubleshooting guide should help you diagnose and resolve most issues with the Notification Engine System. For complex issues, consider enabling debug logging and consulting the application logs for more detailed information.<br>
            </article>
        </main>
    </div>
</body>
</html>