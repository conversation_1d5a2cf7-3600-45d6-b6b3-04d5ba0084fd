<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Architecture & Design › Database Schema
            </nav>
        </header>

        <main>
            <article>
                <h1>Database Schema</h1>
                <h1>Database Schema Documentation</h1></p><p><h2>Overview</h2></p><p>The notification engine uses a comprehensive MySQL database schema managed through Prisma ORM. The schema is designed to support multi-channel notifications, template management, trigger automation, and comprehensive audit logging.</p><p><h2>Core Models</h2></p><p><h3>NotificationLog</h3><br><strong>Purpose</strong>: Tracks all notification attempts, delivery status, and metadata</p><p><pre><code>model NotificationLog {<br>  id           BigInt             @id @default(autoincrement())<br>  triggerId    Int?               // Associated trigger<br>  templateId   Int?               // Template used<br>  idSite       Int                // Site identifier<br>  channelId    Int                // Delivery channel<br>  recipient    String             // Target recipient<br>  status       NotificationStatus // pending | sent | failed<br>  triggerValue String             // Trigger condition value<br>  metadata     Json?              // Additional data<br>  errorMessage String?            // Failure details<br>  createdAt    DateTime           @default(now())<br>  updatedAt    DateTime           @default(now())<br>  createdDate  DateTime?          // Indexed date field<br>}<br></code></pre></p><p><strong>Key Features</strong>:<br>- Comprehensive indexing for performance queries<br>- Status tracking for delivery confirmation<br>- JSON metadata for flexible data storage<br>- Error message logging for debugging</p><p><strong>Indexes</strong>:<br>- <code>idx_notification_logs_site_channel_trigger_value</code><br>- <code>idx_notification_logs_site_created_date</code><br>- <code>idx_notification_logs_status</code></p><p><h3>Trigger</h3><br><strong>Purpose</strong>: Defines automation rules and conditions for notifications</p><p><pre><code>model Trigger {<br>  id                   Int                    @id @default(autoincrement())<br>  name                 String                 @unique<br>  description          String?<br>  triggerTypeId        Int                    // Type of trigger<br>  metricKey            String?               // Metric to evaluate<br>  isConfigurable       Boolean               @default(false)<br>  metadata             Json?                 // Additional configuration<br>  cooldownSeconds      Int?                  // Minimum time between triggers<br>  publicId             String                @unique @default(dbgenerated("(uuid())"))<br>  maxTriggerCount      Int?                  // Maximum times to trigger<br>  maxTriggerPeriod     TriggerIntervalPeriod?<br>  minIntervalCount     Int?                  // Minimum interval<br>  minIntervalUnit      TriggerIntervalPeriod?<br>  fireOnce             Boolean               @default(false)<br>  deltaThreshold       Decimal?              // Change threshold<br>  createdAt            DateTime              @default(now())<br>  updatedAt            DateTime              @default(now())<br>  createdBy            String                @default("system")<br>  updatedBy            String                @default("system")<br>  deletedAt            DateTime?             // Soft delete<br>}<br></code></pre></p><p><strong>Key Features</strong>:<br>- Flexible trigger conditions with JSON metadata<br>- Cooldown periods to prevent spam<br>- Maximum trigger counts and intervals<br>- Soft delete capability<br>- UUID public IDs for external references</p><p><h3>Template</h3><br><strong>Purpose</strong>: Stores notification templates with versioning support</p><p><pre><code>model Template {<br>  id            Int              @id @default(autoincrement())<br>  triggerId     Int?             // Associated trigger<br>  name          String<br>  description   String?<br>  version       Int              @default(1)<br>  channelId     Int              // Target channel<br>  subject       String?          // Email subject or notification title<br>  body          String           // Template content<br>  contentTypeId Int              // Content format<br>  metadata      Json?<br>  status        TemplateStatus   @default(draft) // draft | archived | published<br>  publicId      String           @unique @default(dbgenerated("(uuid())"))<br>  createdAt     DateTime         @default(now())<br>  updatedAt     DateTime         @default(now())<br>  deletedAt     DateTime?        // Soft delete<br>  createdBy     String?<br>  updatedBy     String?<br>}<br></code></pre></p><p><strong>Key Features</strong>:<br>- Template versioning for rollback capability<br>- Multi-channel support<br>- Status management (draft, published, archived)<br>- Audit trail with created/updated by tracking</p><p><h3>Parameter</h3><br><strong>Purpose</strong>: Defines parameters for templates and triggers with validation</p><p><pre><code>model Parameter {<br>  id           Int           @id @default(autoincrement())<br>  triggerId    Int?          // For trigger parameters<br>  templateId   Int?          // For template parameters<br>  name         String<br>  description  String?<br>  required     Boolean       @default(false)<br>  paramTypeId  Int           // Data type<br>  validations  Json?         // Validation rules<br>  defaultValue String?<br>  exampleValue String?<br>  createdAt    DateTime      @default(now())<br>  updatedAt    DateTime      @default(now())<br>}<br></code></pre></p><p><strong>Key Features</strong>:<br>- Flexible parameter definition for templates and triggers<br>- Type validation with JSON validation rules<br>- Default and example values for documentation</p><p><h2>Reference Tables</h2></p><p><h3>ChannelType</h3><br>Defines supported notification channels:<br>- <code>email</code> - SMTP email notifications<br>- <code>slack</code> - Slack workspace notifications<br>- <code>hubspot</code> - HubSpot CRM notifications</p><p><h3>ContentType</h3><br>Defines template content formats:<br>- <code>html</code> - HTML formatted email content<br>- <code>text</code> - Plain text content<br>- <code>json</code> - JSON structured data<br>- <code>block_kit</code> - Slack Block Kit format<br>- <code>hubspot_crm</code> - HubSpot CRM format</p><p><h3>ParameterType</h3><br>Defines parameter data types:<br>- <code>string</code> - Text values<br>- <code>number</code> - Numeric values<br>- <code>boolean</code> - True/false values<br>- <code>date</code> - Date/time values<br>- <code>json</code> - JSON objects<br>- <code>array</code> - Array of values</p><p><h3>TriggerType</h3><br>Categorizes trigger types:<br>- <code>time_based</code> - Scheduled triggers<br>- <code>event_based</code> - Event-driven triggers<br>- <code>condition_based</code> - Conditional triggers</p><p><h2>Site-Specific Configuration</h2></p><p><h3>SiteNotificationPreference</h3><br><strong>Purpose</strong>: User preferences for notification channels per site</p><p><pre><code>model SiteNotificationPreference {<br>  id                Int         @id @default(autoincrement())<br>  idSite            Int         // Site identifier<br>  channelId         Int         // Notification channel<br>  triggerId         Int?        // Specific trigger<br>  isEnabled         Boolean     @default(false)<br>  destination       Json?       // Channel-specific config<br>  triggerNormalized Int?        // Normalized trigger reference<br>}<br></code></pre></p><p><h3>SiteTriggerSetting</h3><br><strong>Purpose</strong>: Site-specific overrides for trigger configurations</p><p><pre><code>model SiteTriggerSetting {<br>  id                       Int                    @id @default(autoincrement())<br>  idSite                   Int<br>  triggerId                Int<br>  isEnabled                Boolean                @default(false)<br>  conditionOverride        Json?                  // Custom conditions<br>  minIntervalCountOverride Int?<br>  minIntervalUnitOverride  TriggerIntervalPeriod?<br>  maxTriggerCountOverride  Int?<br>  maxTriggerPeriodOverride TriggerIntervalPeriod?<br>  deltaThresholdOverride   Decimal?<br>}<br></code></pre></p><p><h3>SiteToken</h3><br><strong>Purpose</strong>: Encrypted storage of external service tokens</p><p><pre><code>model SiteToken {<br>  id                   Int         @id @default(autoincrement())<br>  idSite               Int<br>  channelId            Int<br>  accessTokenEncrypted String      // AES encrypted token<br>  iv                   String      // Initialization vector<br>  tag                  String      // Authentication tag<br>}<br></code></pre></p><p><strong>Security Features</strong>:<br>- AES-256-GCM encryption for tokens<br>- Unique initialization vectors per token<br>- Authentication tags for integrity verification</p><p><h2>Audit and Logging</h2></p><p><h3>TemplateAuditLog</h3><br><strong>Purpose</strong>: Tracks all template changes for compliance and rollback</p><p><pre><code>model TemplateAuditLog {<br>  id          BigInt               @id @default(autoincrement())<br>  templateId  Int<br>  action      TemplateAuditAction? // created | edited | published | archived | rolled_back<br>  fromVersion Int?<br>  toVersion   Int?<br>  performedBy String?<br>  reason      String?<br>  createdAt   DateTime             @default(now())<br>}<br></code></pre></p><p><h2>Enums</h2></p><p><h3>NotificationStatus</h3><br><pre><code>enum NotificationStatus {<br>  pending   // Queued for processing<br>  sent      // Successfully delivered<br>  failed    // Delivery failed<br>}<br></code></pre></p><p><h3>TemplateStatus</h3><br><pre><code>enum TemplateStatus {<br>  draft     // Under development<br>  published // Active and available<br>  archived  // Deprecated but preserved<br>}<br></code></pre></p><p><h3>TriggerIntervalPeriod</h3><br><pre><code>enum TriggerIntervalPeriod {<br>  minute<br>  hour<br>  day<br>  week<br>  month<br>  year<br>}<br></code></pre></p><p><h3>TemplateAuditAction</h3><br><pre><code>enum TemplateAuditAction {<br>  created<br>  edited<br>  published<br>  archived<br>  rolled_back<br>}<br></code></pre></p><p><h2>Database Indexes</h2></p><p><h3>Performance Indexes</h3><br><pre><code>-- Notification logs for site queries<br>CREATE INDEX idx_notification_logs_site_created_date ON notification_logs(id_site, created_date);<br>CREATE INDEX idx_notification_logs_site_channel_trigger_value ON notification_logs(id_site, channel_id, trigger_value);</p><p>-- Template queries<br>CREATE INDEX idx_templates_channel_id ON templates(channel_id);<br>CREATE INDEX idx_templates_content_type_id ON templates(content_type_id);</p><p>-- Parameter lookups<br>CREATE INDEX idx_parameters_template_name ON parameters(template_id, name);<br>CREATE INDEX idx_parameters_trigger_name ON parameters(trigger_id, name);</p><p>-- Soft delete queries<br>CREATE INDEX idx_triggers_deleted_at ON triggers(deleted_at);<br>CREATE INDEX idx_templates_deleted_at ON templates(deleted_at);<br></code></pre></p><p><h2>Relationships Diagram</h2></p><p><pre><code>NotificationLog<br>├── belongs_to: Trigger<br>├── belongs_to: Template<br>└── belongs_to: ChannelType</p><p>Template<br>├── belongs_to: Trigger<br>├── belongs_to: ChannelType<br>├── belongs_to: ContentType<br>├── has_many: Parameter<br>├── has_many: NotificationLog<br>└── has_many: TemplateAuditLog</p><p>Trigger<br>├── belongs_to: TriggerType<br>├── has_many: Template<br>├── has_many: Parameter<br>├── has_many: NotificationLog<br>├── has_many: SiteTriggerSetting<br>└── has_many: SiteNotificationPreference</p><p>SiteNotificationPreference<br>├── belongs_to: ChannelType<br>└── belongs_to: Trigger</p><p>SiteTriggerSetting<br>└── belongs_to: Trigger</p><p>SiteToken<br>└── belongs_to: ChannelType<br></code></pre></p><p><h2>Migration Strategy</h2></p><p><h3>Adding New Columns</h3><br><pre><code>-- Use nullable columns for backward compatibility<br>ALTER TABLE triggers ADD COLUMN new_feature_flag BOOLEAN DEFAULT FALSE;</p><p>-- Update existing records if needed<br>UPDATE triggers SET new_feature_flag = TRUE WHERE condition;<br></code></pre></p><p><h3>Schema Evolution</h3><br>1. <strong>Add new columns as nullable</strong><br>2. <strong>Update application code to handle both states</strong><br>3. <strong>Migrate data in batches</strong><br>4. <strong>Make columns non-nullable if required</strong><br>5. <strong>Clean up old columns after full deployment</strong></p><p><h2>Backup and Recovery</h2></p><p><h3>Backup Strategy</h3><br><pre><code><h1>Full database backup</h1><br>mysqldump -u user -p notifications > backup_$(date +%Y%m%d_%H%M%S).sql</p><p><h1>Table-specific backup</h1><br>mysqldump -u user -p notifications notification_logs > notification_logs_backup.sql<br></code></pre></p><p><h3>Point-in-Time Recovery</h3><br>- Binary logging enabled for transaction recovery<br>- Regular full backups with incremental log backups<br>- Testing recovery procedures monthly</p><p><h2>Performance Optimization</h2></p><p><h3>Query Optimization</h3><br>- Use composite indexes for multi-column WHERE clauses<br>- Avoid SELECT * queries, specify columns<br>- Use LIMIT for pagination queries<br>- Consider read replicas for reporting queries</p><p><h3>Table Maintenance</h3><br><pre><code>-- Analyze table statistics<br>ANALYZE TABLE notification_logs;</p><p>-- Optimize table structure<br>OPTIMIZE TABLE notification_logs;</p><p>-- Check table integrity<br>CHECK TABLE notification_logs;<br></code></pre></p><p><h3>Monitoring Queries</h3><br><pre><code>-- Find slow queries<br>SELECT * FROM information_schema.PROCESSLIST WHERE Time > 60;</p><p>-- Check index usage<br>SHOW INDEX FROM notification_logs;</p><p>-- Table size analysis<br>SELECT<br>  table_name,<br>  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'<br>FROM information_schema.tables<br>WHERE table_schema = 'notifications';<br></code></pre><br>
            </article>
        </main>
    </div>
</body>
</html>