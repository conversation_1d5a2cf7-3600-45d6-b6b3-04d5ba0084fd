<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queue System - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Architecture & Design › Queue System
            </nav>
        </header>

        <main>
            <article>
                <h1>Queue System</h1>
                <h1>Queue System Documentation</h1></p><p><h2>Overview</h2></p><p>The notification engine implements a sophisticated dual-queue architecture combining BullMQ (Redis-based) and RabbitMQ to handle different types of workloads with optimal performance and reliability.</p><p><h2>Architecture Overview</h2></p><p><pre><code>┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐<br>│   BullMQ        │    │   RabbitMQ      │    │   Consumers     │<br>│   (Redis)       │    │   (AMQP)        │    │                 │<br>│                 │    │                 │    │                 │<br>│ • Job Queues    │    │ • Exchanges     │    │ • Site Eval     │<br>│ • Scheduling    │    │ • Routing       │    │ • Triggers      │<br>│ • Retry Logic   │    │ • Pub/Sub       │    │ • Notifications │<br>│ • Monitoring    │    │ • Distribution  │    │ • Observations  │<br>└─────────────────┘    └─────────────────┘    └─────────────────┘<br></code></pre></p><p><h2>BullMQ Queues (Redis-based)</h2></p><p><h3>Queue Types</h3></p><p>#### 1. EvaluationQueue<br><strong>Purpose</strong>: Site evaluation and trigger condition checking</p><p><pre><code>interface SiteEvaluationJob {<br>  sites: number[];           // Site IDs to evaluate<br>  triggerTypes?: string[];   // Specific trigger types<br>  immediate?: boolean;       // Skip scheduling delay<br>}<br></code></pre></p><p><strong>Features</strong>:<br>- Batch processing of multiple sites<br>- Configurable evaluation intervals<br>- Priority-based processing<br>- Automatic retry with exponential backoff</p><p><strong>Configuration</strong>:<br><pre><code>const evaluationQueue = new EvaluationQueue({<br>  connection: { host: 'redis-host', port: 6379 },<br>  defaultJobOptions: {<br>    removeOnComplete: 100,<br>    removeOnFail: 50,<br>    attempts: 3,<br>    backoff: { type: 'exponential', delay: 2000 }<br>  }<br>});<br></code></pre></p><p>#### 2. TriggerQueue<br><strong>Purpose</strong>: Process trigger conditions and determine notification eligibility</p><p><pre><code>interface TriggerJob {<br>  idSite: number;<br>  triggerName: string;<br>  triggerData: Record<string, any>;<br>  evaluationId?: string;<br>  metadata?: Record<string, any>;<br>}<br></code></pre></p><p><strong>Processing Flow</strong>:<br>1. Receive trigger evaluation request<br>2. Check trigger conditions against site data<br>3. Validate cooldown periods and limits<br>4. Generate notification jobs if conditions met<br>5. Update trigger statistics and logs</p><p>#### 3. NotificationQueue<br><strong>Purpose</strong>: Handle notification delivery across all channels</p><p><pre><code>interface NotificationJob {<br>  idSite: number;<br>  triggerName: string;<br>  templateName: string;<br>  channelType: 'email' | 'slack' | 'hubspot';<br>  recipient: string;<br>  templateData: Record<string, any>;<br>  priority?: number;<br>  scheduledFor?: Date;<br>}<br></code></pre></p><p><strong>Features</strong>:<br>- Multi-channel delivery support<br>- Template rendering and validation<br>- Delivery status tracking<br>- Error handling and retry logic</p><p>#### 4. BotQueue<br><strong>Purpose</strong>: Automated tasks and maintenance operations</p><p><pre><code>interface BotJob {<br>  taskType: 'cleanup' | 'maintenance' | 'sync' | 'report';<br>  parameters: Record<string, any>;<br>  scheduledTime?: Date;<br>}<br></code></pre></p><p><strong>Common Tasks</strong>:<br>- Database cleanup operations<br>- Cache warming and maintenance<br>- External service synchronization<br>- Periodic reporting and analytics</p><p>#### 5. ObservationQueue<br><strong>Purpose</strong>: Analytics, monitoring, and data collection</p><p><pre><code>interface ObservationJob {<br>  observationType: 'site_metrics' | 'user_behavior' | 'system_health';<br>  dataPoints: Record<string, any>;<br>  aggregationLevel: 'hourly' | 'daily' | 'weekly';<br>}<br></code></pre></p><p><h2>RabbitMQ Messaging (AMQP)</h2></p><p><h3>Exchange Configuration</h3></p><p>#### TriggerExchange<br><strong>Type</strong>: Topic Exchange<br><strong>Purpose</strong>: Route trigger events to appropriate consumers</p><p><pre><code>// Exchange configuration<br>await queueService.assertExchange('trigger-exchange', 'topic', {<br>  durable: true,<br>  autoDelete: false<br>});</p><p>// Routing patterns<br>const routingKeys = {<br>  trialSites: 'trigger.trial.*',<br>  paidSites: 'trigger.paid.*',<br>  urgentTriggers: 'trigger.*.urgent'<br>};<br></code></pre></p><p>#### NotificationExchange  <br><strong>Type</strong>: Topic Exchange<br><strong>Purpose</strong>: Distribute notification jobs to channel-specific consumers</p><p><pre><code>// Routing by channel<br>const notificationRouting = {<br>  email: 'notification.email.*',<br>  slack: 'notification.slack.*',<br>  hubspot: 'notification.hubspot.*'<br>};<br></code></pre></p><p><h3>Queue Binding Examples</h3></p><p><pre><code>// Bind queues to exchanges with routing keys<br>await queueService.bindQueue('email-notifications', 'notification-exchange', 'notification.email.*');<br>await queueService.bindQueue('slack-notifications', 'notification-exchange', 'notification.slack.*');<br>await queueService.bindQueue('urgent-triggers', 'trigger-exchange', 'trigger.*.urgent');<br></code></pre></p><p><h2>Queue Service Implementation</h2></p><p><h3>Connection Management</h3></p><p><pre><code>export class QueueService {<br>  private connection?: Connection;<br>  private channel?: Channel;<br>  private isInitialized = false;<br>  private reconnectAttempts = 0;<br>  private maxReconnectAttempts = 5;</p><p>  async initialize(): Promise<void> {<br>    try {<br>      this.connection = await amqp.connect({<br>        protocol: 'amqp',<br>        hostname: envConfig.rabbit.host,<br>        username: envConfig.rabbit.username,<br>        password: envConfig.rabbit.password,<br>        heartbeat: 60<br>      });</p><p>      this.channel = await this.connection.createChannel();<br>      this.setupEventListeners();<br>      this.isInitialized = true;</p><p>      logger.info('RabbitMQ connection established');<br>    } catch (error) {<br>      logger.error('Failed to connect to RabbitMQ:', error);<br>      await this.handleReconnect();<br>    }<br>  }</p><p>  private setupEventListeners(): void {<br>    this.connection?.on('error', async (err) => {<br>      logger.error('RabbitMQ connection error:', err);<br>      this.isInitialized = false;<br>      await this.handleReconnect();<br>    });</p><p>    this.connection?.on('close', async () => {<br>      logger.warn('RabbitMQ connection closed');<br>      this.isInitialized = false;<br>      if (!this.isClosing) {<br>        await this.handleReconnect();<br>      }<br>    });<br>  }</p><p>  private async handleReconnect(): Promise<void> {<br>    if (this.reconnectAttempts >= this.maxReconnectAttempts) {<br>      logger.error('Max reconnection attempts reached');<br>      return;<br>    }</p><p>    this.reconnectAttempts++;<br>    const delay = Math.pow(2, this.reconnectAttempts) * 1000;<br>    <br>    logger.info(<code>Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})</code>);<br>    <br>    setTimeout(() => {<br>      this.initialize();<br>    }, delay);<br>  }<br>}<br></code></pre></p><p><h3>Message Publishing</h3></p><p><pre><code>// Publish to exchange with routing<br>export async function publishToExchange<T>(<br>  exchange: string,<br>  routingKey: string,<br>  data: T,<br>  retries: number = 3<br>): Promise<void> {<br>  const queueService = QueueService.getInstance();<br>  <br>  for (let attempt = 1; attempt <= retries; attempt++) {<br>    try {<br>      await queueService.publishToExchange(<br>        exchange,<br>        routingKey,<br>        Buffer.from(JSON.stringify(data)),<br>        { persistent: true }<br>      );<br>      <br>      logger.info('Message published successfully', {<br>        exchange,<br>        routingKey,<br>        attempt<br>      });<br>      return;<br>    } catch (error) {<br>      logger.error(<code>Publish attempt ${attempt} failed:</code>, error);<br>      <br>      if (attempt === retries) {<br>        throw new Error(<code>Failed to publish after ${retries} attempts</code>);<br>      }<br>      <br>      // Exponential backoff<br>      await new Promise(resolve => <br>        setTimeout(resolve, Math.pow(2, attempt) * 1000)<br>      );<br>    }<br>  }<br>}<br></code></pre></p><p><h2>Consumer Implementation</h2></p><p><h3>Site Evaluation Consumer</h3></p><p><pre><code>export async function startSiteEvaluationConsumer(<br>  queue: EvaluationQueue<br>): Promise<void> {<br>  const queueService = QueueService.getInstance();<br>  <br>  await queueService.assertQueue('site-evaluation-queue', { durable: true });<br>  <br>  await queueService.consume('site-evaluation-queue', async (message) => {<br>    if (!message) return;<br>    <br>    try {<br>      const jobData: SiteEvaluationJob = JSON.parse(message.content.toString());<br>      <br>      logger.info('Processing site evaluation job', { <br>        sites: jobData.sites.length,<br>        triggerTypes: jobData.triggerTypes <br>      });<br>      <br>      // Process each site<br>      for (const siteId of jobData.sites) {<br>        await evaluateSiteConditions(siteId, jobData.triggerTypes);<br>      }<br>      <br>      // Acknowledge message<br>      queueService.ack(message);<br>      <br>    } catch (error) {<br>      logger.error('Site evaluation failed:', error);<br>      <br>      // Reject and requeue with limit<br>      queueService.nack(message, false, true);<br>    }<br>  });<br>}<br></code></pre></p><p><h3>Notification Consumer</h3></p><p><pre><code>export async function startNotificationConsumer(<br>  queue: NotificationQueue<br>): Promise<void> {<br>  const queueService = QueueService.getInstance();<br>  <br>  await queueService.assertQueue('notification-queue', { durable: true });<br>  <br>  await queueService.consume('notification-queue', async (message) => {<br>    if (!message) return;<br>    <br>    try {<br>      const jobData: NotificationJobData = JSON.parse(message.content.toString());<br>      <br>      // Render template<br>      const renderedContent = await renderTemplate(<br>        jobData.templateName,<br>        jobData.channelType,<br>        jobData.templateData<br>      );<br>      <br>      // Send notification<br>      const connector = ConnectorFactory.getConnector(jobData.channelType);<br>      await connector.send({<br>        recipient: jobData.recipient,<br>        subject: renderedContent.subject,<br>        content: renderedContent.body,<br>        metadata: jobData.templateData<br>      });<br>      <br>      // Log success<br>      await logNotificationStatus(jobData, 'sent');<br>      <br>      queueService.ack(message);<br>      <br>    } catch (error) {<br>      logger.error('Notification delivery failed:', error);<br>      <br>      // Log failure<br>      await logNotificationStatus(jobData, 'failed', error.message);<br>      <br>      queueService.nack(message, false, false); // Don't requeue<br>    }<br>  });<br>}<br></code></pre></p><p><h2>Queue Monitoring</h2></p><p><h3>Bull Board Integration</h3></p><p><pre><code>import { createBullBoard } from '@bull-board/api';<br>import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';<br>import { ExpressAdapter } from '@bull-board/express';</p><p>const serverAdapter = new ExpressAdapter();<br>serverAdapter.setBasePath('/admin/queues');</p><p>export const setupBullBoard = (app: Express): void => {<br>  const { addQueue } = createBullBoard({<br>    queues: [<br>      new BullMQAdapter(evaluationQueue.getQueue()),<br>      new BullMQAdapter(triggerQueue.getQueue()),<br>      new BullMQAdapter(notificationQueue.getQueue()),<br>      new BullMQAdapter(botQueue.getQueue()),<br>      new BullMQAdapter(observationQueue.getQueue())<br>    ],<br>    serverAdapter: serverAdapter<br>  });</p><p>  app.use('/admin/queues', serverAdapter.getRouter());<br>};<br></code></pre></p><p><strong>Dashboard Features</strong>:<br>- Real-time queue status and metrics<br>- Job details and execution logs<br>- Retry and failure management<br>- Performance analytics<br>- Manual job triggering</p><p><h3>Custom Metrics</h3></p><p><pre><code>// Queue health monitoring<br>export class QueueMetrics {<br>  static async getQueueStats(queueName: string) {<br>    const queue = getQueueInstance(queueName);<br>    <br>    return {<br>      waiting: await queue.getWaiting(),<br>      active: await queue.getActive(),<br>      completed: await queue.getCompleted(),<br>      failed: await queue.getFailed(),<br>      delayed: await queue.getDelayed(),<br>      <br>      // Performance metrics<br>      throughput: await this.calculateThroughput(queue),<br>      averageProcessingTime: await this.getAverageProcessingTime(queue),<br>      errorRate: await this.calculateErrorRate(queue)<br>    };<br>  }<br>  <br>  static async calculateThroughput(queue: Queue): Promise<number> {<br>    const oneHourAgo = Date.now() - (60 <em> 60 </em> 1000);<br>    const completedJobs = await queue.getJobs(['completed'], oneHourAgo);<br>    return completedJobs.length;<br>  }<br>}<br></code></pre></p><p><h2>Error Handling and Recovery</h2></p><p><h3>Retry Strategies</h3></p><p><pre><code>// Exponential backoff with jitter<br>const retryConfig = {<br>  attempts: 5,<br>  backoff: {<br>    type: 'exponential',<br>    delay: 2000,<br>    settings: {<br>      jitter: true  // Add randomness to prevent thundering herd<br>    }<br>  }<br>};</p><p>// Custom retry logic<br>const customRetry = {<br>  attempts: 3,<br>  backoff: (attemptsMade: number) => {<br>    return Math.min(Math.pow(2, attemptsMade) * 1000, 30000);<br>  }<br>};<br></code></pre></p><p><h3>Dead Letter Queues</h3></p><p><pre><code>// Configure dead letter queue for failed messages<br>await queueService.assertQueue('notification-queue', {<br>  durable: true,<br>  arguments: {<br>    'x-dead-letter-exchange': 'dlx-exchange',<br>    'x-dead-letter-routing-key': 'failed-notifications',<br>    'x-message-ttl': 3600000  // 1 hour TTL<br>  }<br>});</p><p>// Dead letter exchange setup<br>await queueService.assertExchange('dlx-exchange', 'direct', { durable: true });<br>await queueService.assertQueue('failed-notifications', { durable: true });<br>await queueService.bindQueue('failed-notifications', 'dlx-exchange', 'failed-notifications');<br></code></pre></p><p><h3>Circuit Breaker Pattern</h3></p><p><pre><code>class CircuitBreaker {<br>  private failures = 0;<br>  private lastFailureTime = 0;<br>  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';<br>  <br>  constructor(<br>    private threshold = 5,<br>    private timeout = 60000<br>  ) {}<br>  <br>  async execute<T>(operation: () => Promise<T>): Promise<T> {<br>    if (this.state === 'OPEN') {<br>      if (Date.now() - this.lastFailureTime < this.timeout) {<br>        throw new Error('Circuit breaker is OPEN');<br>      }<br>      this.state = 'HALF_OPEN';<br>    }<br>    <br>    try {<br>      const result = await operation();<br>      this.onSuccess();<br>      return result;<br>    } catch (error) {<br>      this.onFailure();<br>      throw error;<br>    }<br>  }<br>  <br>  private onSuccess(): void {<br>    this.failures = 0;<br>    this.state = 'CLOSED';<br>  }<br>  <br>  private onFailure(): void {<br>    this.failures++;<br>    this.lastFailureTime = Date.now();<br>    <br>    if (this.failures >= this.threshold) {<br>      this.state = 'OPEN';<br>    }<br>  }<br>}<br></code></pre></p><p><h2>Performance Optimization</h2></p><p><h3>Connection Pooling</h3></p><p><pre><code>// Redis connection pool for BullMQ<br>const redisPool = {<br>  maxRetriesPerRequest: 3,<br>  retryDelayOnFailover: 100,<br>  enableReadyCheck: false,<br>  maxConnections: 20,<br>  connectionIdleTimeout: 30000<br>};</p><p>// RabbitMQ channel pooling<br>class ChannelPool {<br>  private channels: Channel[] = [];<br>  private maxChannels = 10;<br>  <br>  async getChannel(): Promise<Channel> {<br>    if (this.channels.length > 0) {<br>      return this.channels.pop()!;<br>    }<br>    <br>    if (this.channels.length < this.maxChannels) {<br>      return await this.connection.createChannel();<br>    }<br>    <br>    // Wait for available channel<br>    return new Promise((resolve) => {<br>      const checkForChannel = () => {<br>        if (this.channels.length > 0) {<br>          resolve(this.channels.pop()!);<br>        } else {<br>          setTimeout(checkForChannel, 10);<br>        }<br>      };<br>      checkForChannel();<br>    });<br>  }<br>  <br>  releaseChannel(channel: Channel): void {<br>    this.channels.push(channel);<br>  }<br>}<br></code></pre></p><p><h3>Batch Processing</h3></p><p><pre><code>// Batch job processing for efficiency<br>export class BatchProcessor {<br>  private batch: any[] = [];<br>  private batchSize = 100;<br>  private flushInterval = 5000; // 5 seconds<br>  <br>  constructor(private processor: (items: any[]) => Promise<void>) {<br>    setInterval(() => this.flush(), this.flushInterval);<br>  }<br>  <br>  add(item: any): void {<br>    this.batch.push(item);<br>    <br>    if (this.batch.length >= this.batchSize) {<br>      this.flush();<br>    }<br>  }<br>  <br>  private async flush(): Promise<void> {<br>    if (this.batch.length === 0) return;<br>    <br>    const itemsToProcess = [...this.batch];<br>    this.batch = [];<br>    <br>    try {<br>      await this.processor(itemsToProcess);<br>    } catch (error) {<br>      logger.error('Batch processing failed:', error);<br>      // Could implement retry logic here<br>    }<br>  }<br>}<br></code></pre></p><p><h2>Configuration Best Practices</h2></p><p><h3>Environment-Specific Settings</h3></p><p><pre><code>// Development<br>const devConfig = {<br>  redis: {<br>    maxRetriesPerRequest: 1,<br>    connectTimeout: 5000<br>  },<br>  queues: {<br>    removeOnComplete: 10,<br>    removeOnFail: 10<br>  }<br>};</p><p>// Production<br>const prodConfig = {<br>  redis: {<br>    maxRetriesPerRequest: 3,<br>    connectTimeout: 10000,<br>    enableAutoPipelining: true<br>  },<br>  queues: {<br>    removeOnComplete: 100,<br>    removeOnFail: 50,<br>    attempts: 5<br>  }<br>};<br></code></pre></p><p><h3>Monitoring and Alerting</h3></p><p><pre><code>// Queue health checks<br>export async function checkQueueHealth(): Promise<{<br>  status: 'healthy' | 'degraded' | 'unhealthy';<br>  details: Record<string, any>;<br>}> {<br>  const queues = ['evaluation', 'trigger', 'notification', 'bot', 'observation'];<br>  const results = {};<br>  let overallStatus = 'healthy';<br>  <br>  for (const queueName of queues) {<br>    const stats = await QueueMetrics.getQueueStats(queueName);<br>    <br>    // Check for concerning metrics<br>    if (stats.failed > 100 || stats.waiting > 1000) {<br>      overallStatus = 'degraded';<br>    }<br>    <br>    if (stats.errorRate > 0.1) { // 10% error rate<br>      overallStatus = 'unhealthy';<br>    }<br>    <br>    results[queueName] = stats;<br>  }<br>  <br>  return { status: overallStatus, details: results };<br>}<br></code></pre><br>
            </article>
        </main>
    </div>
</body>
</html>