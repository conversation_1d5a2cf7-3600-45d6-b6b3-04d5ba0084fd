<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Engine - Notification Engine Documentation</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">Notification Engine Documentation</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › Architecture & Design › Template Engine
            </nav>
        </header>

        <main>
            <article>
                <h1>Template Engine</h1>
                <h1>Template System Documentation</h1></p><p><h2>Overview</h2></p><p>The notification engine's template system provides a powerful, flexible framework for creating and managing multi-channel notification templates using Handlebars.js with custom layouts and helpers.</p><p><h2>Architecture</h2></p><p><pre><code>Template Engine<br>├── Template Registry      # Schema definitions and validation<br>├── Template Loader       # File system template loading<br>├── Layout System        # Base layouts for consistent branding<br>├── Custom Helpers       # Handlebars helper functions<br>├── Validation Layer     # Zod schema validation<br>└── Rendering Engine     # Template compilation and rendering<br></code></pre></p><p><h2>Template Registry</h2></p><p>The template registry defines available templates, their schemas, and validation rules for each channel.</p><p><h3>Registry Structure</h3></p><p><pre><code>// src/template-registry.ts<br>export const templateRegistry = {<br>  'template-name': {<br>    email: {<br>      schema: z.object({<br>        // Email-specific validation schema<br>        subject: z.string().min(1),<br>        recipientName: z.string(),<br>        // ... other properties<br>      })<br>    },<br>    slack: {<br>      schema: z.object({<br>        // Slack-specific validation schema<br>        channelId: z.string(),<br>        blocks: z.array(z.object({<br>          type: z.string(),<br>          // ... block kit schema<br>        }))<br>      })<br>    },<br>    hubspot: {<br>      schema: z.object({<br>        // HubSpot CRM schema<br>        contactId: z.string(),<br>        properties: z.record(z.any())<br>      })<br>    }<br>  }<br>}<br></code></pre></p><p><h3>Template Examples</h3></p><p>#### Weekly Summary Template<br><pre><code>'weekly-summary-recurring': {<br>  email: {<br>    schema: z.object({<br>      siteName: z.string(),<br>      startDate: z.string(),<br>      endDate: z.string(),<br>      metrics: z.object({<br>        sessions: z.number(),<br>        revenue: z.number().optional(),<br>        pageviews: z.number(),<br>        conversionRate: z.number().optional()<br>      }),<br>      topPages: z.array(z.object({<br>        url: z.string(),<br>        views: z.number(),<br>        conversions: z.number().optional()<br>      })),<br>      userName: z.string()<br>    })<br>  },<br>  slack: {<br>    schema: z.object({<br>      siteName: z.string(),<br>      metrics: z.object({<br>        sessions: z.number(),<br>        revenue: z.number().optional()<br>      }),<br>      channelId: z.string()<br>    })<br>  }<br>}<br></code></pre></p><p><h2>Template Engine Implementation</h2></p><p><h3>Core Engine Class</h3></p><p><pre><code>export class TemplateEngine {<br>  registry: typeof templateRegistry;<br>  templates: Record<string, string> = {};<br>  layouts: Record<string, string> = {};</p><p>  constructor(<br>    private readonly templatesPath: string,<br>    private readonly layoutsPath?: string,<br>    registry = templateRegistry<br>  ) {<br>    this.registry = registry;<br>  }</p><p>  async init(): Promise<void> {<br>    // Register custom helpers for layout functionality<br>    this.registerLayoutHelpers();</p><p>    // Load layout templates first<br>    if (this.layoutsPath) {<br>      await this.loadLayouts();<br>    }</p><p>    // Load regular templates<br>    await this.loadTemplates();<br>    <br>    // Validate registry completeness<br>    await this.checkTemplateRegistryCompleteness();<br>  }</p><p>  /<em></em><br>   * Render a template with provided data<br>   */<br>  render(templateName: string, channel: string, data: any): string {<br>    const template = this.getCompiledTemplate(templateName, channel);<br>    return template(data);<br>  }</p><p>  /<em></em><br>   * Validate data against template schema<br>   */<br>  validateData(templateName: string, channel: string, data: any): boolean {<br>    const schema = this.getSchema(templateName, channel);<br>    const result = schema.safeParse(data);<br>    <br>    if (!result.success) {<br>      throw new Error(<code>Template validation failed: ${result.error.message}</code>);<br>    }<br>    <br>    return true;<br>  }<br>}<br></code></pre></p><p><h3>Layout System</h3></p><p>The template engine supports layout inheritance using custom Handlebars helpers that mimic <code>handlebars-layouts</code> functionality.</p><p>#### Layout Helper Implementation</p><p><pre><code>private registerLayoutHelpers(): void {<br>  const blocks: Record<string, Record<string, string>> = {};</p><p>  // Helper to define content blocks<br>  Handlebars.registerHelper('content', function(this: any, name, options) {<br>    const data = Handlebars.createFrame(options.data);<br>    const blockName = name.trim();</p><p>    if (!blocks[data.layoutName]) {<br>      blocks[data.layoutName] = {};<br>    }</p><p>    // Store the block content<br>    blocks[data.layoutName][blockName] = options.fn(this);<br>    return null;<br>  });</p><p>  // Helper to retrieve content blocks<br>  Handlebars.registerHelper('block', function(this: any, name, options) {<br>    const data = options.data;<br>    const layoutName = data.layoutName;<br>    const blockName = name.trim();</p><p>    // Get block content or use default<br>    let content = '';<br>    if (blocks[layoutName] && blocks[layoutName][blockName]) {<br>      content = blocks[layoutName][blockName];<br>    } else if (options.fn) {<br>      content = options.fn(this);<br>    }</p><p>    return new Handlebars.SafeString(content);<br>  });</p><p>  // Helper to extend layouts<br>  Handlebars.registerHelper('extend', function(this: any, layoutName, options) {<br>    const data = Handlebars.createFrame(options.data);<br>    data.layoutName = layoutName;</p><p>    // Execute the child template to populate blocks<br>    options.fn(this, { data: data });</p><p>    // Render the layout with populated blocks<br>    const layout = this.layouts[layoutName];<br>    if (!layout) {<br>      throw new Error(<code>Layout '${layoutName}' not found</code>);<br>    }</p><p>    const template = Handlebars.compile(layout);<br>    return template(this, { data: data });<br>  });<br>}<br></code></pre></p><p><h2>Template Structure</h2></p><p><h3>Directory Organization</h3></p><p><pre><code>templates/<br>├── layouts/                    # Base layouts<br>│   ├── email.hbs              # Email HTML layout<br>│   ├── email-plain.hbs        # Plain text email layout<br>│   └── slack.hbs              # Slack message layout<br>├── new-templates/             # Current email templates<br>│   ├── credit-card.hbs<br>│   ├── weekly-summary-recurring.hbs<br>│   ├── login-case-study.hbs<br>│   └── ...<br>├── slack-templates/           # Slack-specific templates<br>│   ├── credit-card.hbs<br>│   ├── weekly-summary-recurring.hbs<br>│   └── ...<br>└── templating/               # Legacy templates<br>    └── ...<br></code></pre></p><p><h3>Template File Examples</h3></p><p>#### Email Layout (layouts/email.hbs)<br><pre><code><!DOCTYPE html><br><html lang="en"><br><head><br>    <meta charset="UTF-8"><br>    <meta name="viewport" content="width=device-width, initial-scale=1.0"><br>    <title>{{#block "title"}}Notification{{/block}}</title><br>    <style><br>        body { <br>            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;<br>            line-height: 1.6;<br>            color: #333;<br>            max-width: 600px;<br>            margin: 0 auto;<br>            padding: 20px;<br>        }<br>        .header {<br>            background: #f8f9fa;<br>            padding: 20px;<br>            border-radius: 8px 8px 0 0;<br>            border-bottom: 3px solid #007bff;<br>        }<br>        .content {<br>            background: white;<br>            padding: 30px;<br>            border-radius: 0 0 8px 8px;<br>            box-shadow: 0 2px 4px rgba(0,0,0,0.1);<br>        }<br>        .footer {<br>            text-align: center;<br>            color: #666;<br>            font-size: 12px;<br>            margin-top: 20px;<br>        }<br>        .btn {<br>            display: inline-block;<br>            padding: 12px 24px;<br>            background: #007bff;<br>            color: white;<br>            text-decoration: none;<br>            border-radius: 4px;<br>            margin: 10px 0;<br>        }<br>    </style><br></head><br><body><br>    <div class="header"><br>        {{#block "header"}}<br>            <h1>{{siteName}} Notification</h1><br>        {{/block}}<br>    </div><br>    <br>    <div class="content"><br>        {{#block "content"}}<br>            <p>Default content</p><br>        {{/block}}<br>    </div><br>    <br>    <div class="footer"><br>        {{#block "footer"}}<br>            <p>© 2025 Your Company. All rights reserved.</p><br>            <p><a href="{{unsubscribeUrl}}">Unsubscribe</a></p><br>        {{/block}}<br>    </div><br></body><br></html><br></code></pre></p><p>#### Email Template (new-templates/weekly-summary-recurring.hbs)<br><pre><code>{{#extend "email"}}<br>  {{#content "title"}}Weekly Summary - {{siteName}}{{/content}}<br>  <br>  {{#content "header"}}<br>    <h1>📊 Weekly Summary for {{siteName}}</h1><br>    <p>{{startDate}} - {{endDate}}</p><br>  {{/content}}<br>  <br>  {{#content "content"}}<br>    <h2>Hey {{userName}}! 👋</h2><br>    <br>    <p>Here's how {{siteName}} performed this week:</p><br>    <br>    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;"><br>      <h3>📈 Key Metrics</h3><br>      <ul style="list-style: none; padding: 0;"><br>        <li><strong>Sessions:</strong> {{formatNumber metrics.sessions}}</li><br>        <li><strong>Page Views:</strong> {{formatNumber metrics.pageviews}}</li><br>        {{#if metrics.revenue}}<br>        <li><strong>Revenue:</strong> ${{formatCurrency metrics.revenue}}</li><br>        {{/if}}<br>        {{#if metrics.conversionRate}}<br>        <li><strong>Conversion Rate:</strong> {{formatPercent metrics.conversionRate}}</li><br>        {{/if}}<br>      </ul><br>    </div><br>    <br>    {{#if topPages}}<br>    <h3>🔥 Top Performing Pages</h3><br>    <table style="width: 100%; border-collapse: collapse;"><br>      <thead><br>        <tr style="background: #f8f9fa;"><br>          <th style="padding: 10px; text-align: left;">Page</th><br>          <th style="padding: 10px; text-align: right;">Views</th><br>          {{#if (hasProperty (first topPages) 'conversions')}}<br>          <th style="padding: 10px; text-align: right;">Conversions</th><br>          {{/if}}<br>        </tr><br>      </thead><br>      <tbody><br>        {{#each topPages}}<br>        <tr><br>          <td style="padding: 10px; border-bottom: 1px solid #eee;"><br>            <a href="{{url}}" style="color: #007bff;">{{truncate url 50}}</a><br>          </td><br>          <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;"><br>            {{formatNumber views}}<br>          </td><br>          {{#if conversions}}<br>          <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;"><br>            {{formatNumber conversions}}<br>          </td><br>          {{/if}}<br>        </tr><br>        {{/each}}<br>      </tbody><br>    </table><br>    {{/if}}<br>    <br>    <div style="margin: 30px 0; text-align: center;"><br>      <a href="{{dashboardUrl}}" class="btn">View Full Dashboard</a><br>    </div><br>    <br>    <p>Keep up the great work! 🚀</p><br>  {{/content}}<br>{{/extend}}<br></code></pre></p><p>#### Slack Template (slack-templates/weekly-summary-recurring.hbs)<br><pre><code>{<br>  "blocks": [<br>    {<br>      "type": "header",<br>      "text": {<br>        "type": "plain_text",<br>        "text": "📊 Weekly Summary: {{siteName}}"<br>      }<br>    },<br>    {<br>      "type": "context",<br>      "elements": [<br>        {<br>          "type": "mrkdwn",<br>          "text": "{{startDate}} - {{endDate}}"<br>        }<br>      ]<br>    },<br>    {<br>      "type": "section",<br>      "text": {<br>        "type": "mrkdwn",<br>        "text": "Hey {{userName}}! Here's how <em>{{siteName}}</em> performed this week:"<br>      }<br>    },<br>    {<br>      "type": "section",<br>      "fields": [<br>        {<br>          "type": "mrkdwn",<br>          "text": "<em>Sessions:</em>\n{{formatNumber metrics.sessions}}"<br>        },<br>        {<br>          "type": "mrkdwn",<br>          "text": "<em>Page Views:</em>\n{{formatNumber metrics.pageviews}}"<br>        }<br>        {{#if metrics.revenue}},<br>        {<br>          "type": "mrkdwn",<br>          "text": "<em>Revenue:</em>\n${{formatCurrency metrics.revenue}}"<br>        }<br>        {{/if}}<br>        {{#if metrics.conversionRate}},<br>        {<br>          "type": "mrkdwn",<br>          "text": "<em>Conversion Rate:</em>\n{{formatPercent metrics.conversionRate}}"<br>        }<br>        {{/if}}<br>      ]<br>    },<br>    {{#if topPages}}<br>    {<br>      "type": "section",<br>      "text": {<br>        "type": "mrkdwn",<br>        "text": "<em>🔥 Top Pages:</em>\n{{#each topPages}}• <{{url}}|{{truncate url 40}}> ({{formatNumber views}} views)\n{{/each}}"<br>      }<br>    },<br>    {{/if}}<br>    {<br>      "type": "actions",<br>      "elements": [<br>        {<br>          "type": "button",<br>          "text": {<br>            "type": "plain_text",<br>            "text": "View Dashboard"<br>          },<br>          "url": "{{dashboardUrl}}",<br>          "style": "primary"<br>        }<br>      ]<br>    }<br>  ]<br>}<br></code></pre></p><p><h2>Custom Handlebars Helpers</h2></p><p><h3>Formatting Helpers</h3></p><p><pre><code>// Number formatting<br>Handlebars.registerHelper('formatNumber', function(value: number): string {<br>  if (typeof value !== 'number') return '0';<br>  return value.toLocaleString('en-US');<br>});</p><p>// Currency formatting<br>Handlebars.registerHelper('formatCurrency', function(value: number): string {<br>  if (typeof value !== 'number') return '0.00';<br>  return value.toLocaleString('en-US', {<br>    minimumFractionDigits: 2,<br>    maximumFractionDigits: 2<br>  });<br>});</p><p>// Percentage formatting<br>Handlebars.registerHelper('formatPercent', function(value: number): string {<br>  if (typeof value !== 'number') return '0%';<br>  return (value * 100).toFixed(1) + '%';<br>});</p><p>// Date formatting<br>Handlebars.registerHelper('formatDate', function(date: string, format?: string): string {<br>  const d = new Date(date);<br>  if (format === 'short') {<br>    return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });<br>  }<br>  return d.toLocaleDateString('en-US', { <br>    year: 'numeric', <br>    month: 'long', <br>    day: 'numeric' <br>  });<br>});<br></code></pre></p><p><h3>Utility Helpers</h3></p><p><pre><code>// String truncation<br>Handlebars.registerHelper('truncate', function(str: string, length: number): string {<br>  if (!str || str.length <= length) return str;<br>  return str.substring(0, length) + '...';<br>});</p><p>// Conditional helpers<br>Handlebars.registerHelper('gt', function(a: number, b: number): boolean {<br>  return a > b;<br>});</p><p>Handlebars.registerHelper('lt', function(a: number, b: number): boolean {<br>  return a < b;<br>});</p><p>Handlebars.registerHelper('eq', function(a: any, b: any): boolean {<br>  return a === b;<br>});</p><p>// Array helpers<br>Handlebars.registerHelper('first', function(array: any[]): any {<br>  return array && array.length > 0 ? array[0] : null;<br>});</p><p>Handlebars.registerHelper('last', function(array: any[]): any {<br>  return array && array.length > 0 ? array[array.length - 1] : null;<br>});</p><p>Handlebars.registerHelper('hasProperty', function(obj: any, property: string): boolean {<br>  return obj && obj.hasOwnProperty(property);<br>});<br></code></pre></p><p><h3>Business Logic Helpers</h3></p><p><pre><code>// Site-specific helpers<br>Handlebars.registerHelper('getDashboardUrl', function(siteId: number): string {<br>  return <code>https://dashboard.yourapp.com/sites/${siteId}</code>;<br>});</p><p>Handlebars.registerHelper('getUnsubscribeUrl', function(siteId: number, email: string): string {<br>  const token = generateUnsubscribeToken(siteId, email);<br>  return <code>https://yourapp.com/unsubscribe?token=${token}</code>;<br>});</p><p>// Metric analysis helpers<br>Handlebars.registerHelper('getTrend', function(current: number, previous: number): string {<br>  if (!previous) return 'new';<br>  const change = ((current - previous) / previous) * 100;<br>  <br>  if (change > 10) return 'up-significant';<br>  if (change > 0) return 'up';<br>  if (change < -10) return 'down-significant';<br>  if (change < 0) return 'down';<br>  return 'stable';<br>});</p><p>Handlebars.registerHelper('getTrendIcon', function(trend: string): string {<br>  const icons = {<br>    'up-significant': '📈',<br>    'up': '⬆️',<br>    'down-significant': '📉',<br>    'down': '⬇️',<br>    'stable': '➡️',<br>    'new': '✨'<br>  };<br>  return icons[trend] || '➡️';<br>});<br></code></pre></p><p><h2>Template Validation</h2></p><p><h3>Schema Validation</h3></p><p><pre><code>export function validateTemplateData(<br>  templateName: string,<br>  channel: string,<br>  data: any<br>): { valid: boolean; errors?: string[] } {<br>  const templateConfig = templateRegistry[templateName];<br>  <br>  if (!templateConfig) {<br>    return { valid: false, errors: [<code>Template '${templateName}' not found</code>] };<br>  }<br>  <br>  const channelConfig = templateConfig[channel];<br>  if (!channelConfig) {<br>    return { valid: false, errors: [<code>Channel '${channel}' not supported for template '${templateName}'</code>] };<br>  }<br>  <br>  const result = channelConfig.schema.safeParse(data);<br>  <br>  if (!result.success) {<br>    return {<br>      valid: false,<br>      errors: result.error.errors.map(err => <code>${err.path.join('.')}: ${err.message}</code>)<br>    };<br>  }<br>  <br>  return { valid: true };<br>}<br></code></pre></p><p><h3>Template Completeness Check</h3></p><p><pre><code>async checkTemplateRegistryCompleteness(): Promise<void> {<br>  const registryTemplates = Object.keys(this.registry);<br>  const missingTemplates: string[] = [];<br>  <br>  for (const templateName of registryTemplates) {<br>    const templateConfig = this.registry[templateName];<br>    <br>    for (const channel of Object.keys(templateConfig)) {<br>      const expectedFileName = <code>${templateName}.hbs</code>;<br>      const channelPath = channel === 'email' ? this.templatesPath : <br>                         channel === 'slack' ? this.templatesPath.replace('new-templates', 'slack-templates') :<br>                         this.templatesPath;<br>      <br>      const templatePath = path.join(channelPath, expectedFileName);<br>      <br>      try {<br>        await fs.access(templatePath);<br>      } catch (error) {<br>        missingTemplates.push(<code>${templateName}/${channel} (${templatePath})</code>);<br>      }<br>    }<br>  }<br>  <br>  if (missingTemplates.length > 0) {<br>    console.warn('Missing template files:', missingTemplates);<br>  } else {<br>    console.log('✅ All registry templates have corresponding files');<br>  }<br>}<br></code></pre></p><p><h2>Template Rendering Pipeline</h2></p><p><h3>Rendering Process</h3></p><p><pre><code>export async function renderTemplate(<br>  templateName: string,<br>  channel: string,<br>  data: any<br>): Promise<{ subject?: string; body: string }> {<br>  // 1. Validate input data<br>  const validation = validateTemplateData(templateName, channel, data);<br>  if (!validation.valid) {<br>    throw new Error(<code>Template validation failed: ${validation.errors?.join(', ')}</code>);<br>  }<br>  <br>  // 2. Get template engine instance<br>  const engine = await getTemplateEngine();<br>  <br>  // 3. Prepare template data with helpers<br>  const templateData = {<br>    ...data,<br>    // Add global helpers/data<br>    timestamp: new Date().toISOString(),<br>    environment: process.env.NODE_ENV,<br>    // Add any computed values<br>    ...computeAdditionalData(data)<br>  };<br>  <br>  // 4. Render template<br>  try {<br>    const rendered = engine.render(templateName, channel, templateData);<br>    <br>    // 5. Parse rendered content for subject/body<br>    if (channel === 'email') {<br>      return parseEmailTemplate(rendered);<br>    } else {<br>      return { body: rendered };<br>    }<br>  } catch (error) {<br>    logger.error('Template rendering failed', {<br>      templateName,<br>      channel,<br>      error: error.message<br>    });<br>    throw error;<br>  }<br>}</p><p>function parseEmailTemplate(rendered: string): { subject?: string; body: string } {<br>  // Extract subject from title tag if present<br>  const titleMatch = rendered.match(/<title>(.*?)<\/title>/);<br>  const subject = titleMatch ? titleMatch[1] : undefined;<br>  <br>  return { subject, body: rendered };<br>}<br></code></pre></p><p><h3>Template Caching</h3></p><p><pre><code>class TemplateCache {<br>  private cache = new Map<string, HandlebarsTemplateDelegate>();<br>  private maxAge = 5 <em> 60 </em> 1000; // 5 minutes<br>  private timestamps = new Map<string, number>();<br>  <br>  get(key: string): HandlebarsTemplateDelegate | null {<br>    const template = this.cache.get(key);<br>    const timestamp = this.timestamps.get(key);<br>    <br>    if (!template || !timestamp) return null;<br>    <br>    // Check if cache entry is expired<br>    if (Date.now() - timestamp > this.maxAge) {<br>      this.cache.delete(key);<br>      this.timestamps.delete(key);<br>      return null;<br>    }<br>    <br>    return template;<br>  }<br>  <br>  set(key: string, template: HandlebarsTemplateDelegate): void {<br>    this.cache.set(key, template);<br>    this.timestamps.set(key, Date.now());<br>  }<br>  <br>  clear(): void {<br>    this.cache.clear();<br>    this.timestamps.clear();<br>  }<br>}<br></code></pre></p><p><h2>Template Development Workflow</h2></p><p><h3>Development Setup</h3></p><p>1. <strong>Create template files</strong><br>   <pre><code>   # Email template<br>   touch src/new-templates/my-new-template.hbs<br>   <br>   # Slack template<br>   touch src/slack-templates/my-new-template.hbs<br>   </code></pre></p><p>2. <strong>Register in template registry</strong><br>   <pre><code>   // Add to src/template-registry.ts<br>   'my-new-template': {<br>     email: {<br>       schema: z.object({<br>         userName: z.string(),<br>         // ... other properties<br>       })<br>     },<br>     slack: {<br>       schema: z.object({<br>         // ... slack properties<br>       })<br>     }<br>   }<br>   </code></pre></p><p>3. <strong>Test template rendering</strong><br>   <pre><code>   // Development testing<br>   const testData = {<br>     userName: 'John Doe',<br>     // ... test data<br>   };<br>   <br>   const rendered = await renderTemplate('my-new-template', 'email', testData);<br>   console.log(rendered);<br>   </code></pre></p><p><h3>Template Testing</h3></p><p><pre><code>// Template test suite<br>describe('Template Rendering', () => {<br>  test('should render weekly summary email', async () => {<br>    const testData = {<br>      siteName: 'Test Site',<br>      userName: 'John Doe',<br>      startDate: '2025-06-01',<br>      endDate: '2025-06-08',<br>      metrics: {<br>        sessions: 1234,<br>        pageviews: 5678,<br>        revenue: 1234.56<br>      },<br>      topPages: [<br>        { url: '/home', views: 100, conversions: 5 },<br>        { url: '/product', views: 80, conversions: 8 }<br>      ]<br>    };<br>    <br>    const result = await renderTemplate('weekly-summary-recurring', 'email', testData);<br>    <br>    expect(result.body).toContain('Test Site');<br>    expect(result.body).toContain('John Doe');<br>    expect(result.body).toContain('1,234');<br>    expect(result.subject).toContain('Weekly Summary');<br>  });<br>  <br>  test('should validate template data', () => {<br>    const invalidData = { /<em> missing required fields </em>/ };<br>    <br>    const validation = validateTemplateData('weekly-summary-recurring', 'email', invalidData);<br>    <br>    expect(validation.valid).toBe(false);<br>    expect(validation.errors).toBeDefined();<br>  });<br>});<br></code></pre></p><p><h2>Performance Considerations</h2></p><p><h3>Template Compilation</h3></p><p>- Templates are compiled once and cached in memory<br>- Hot reloading in development mode<br>- Precompilation for production deployments</p><p><h3>Memory Management</h3></p><p>- Template cache with TTL to prevent memory leaks<br>- Lazy loading of templates<br>- Cleanup of unused templates</p><p><h3>Optimization Tips</h3></p><p>1. <strong>Minimize template complexity</strong> - Keep logic simple in templates<br>2. <strong>Use helpers</strong> - Move complex logic to Handlebars helpers<br>3. <strong>Cache compiled templates</strong> - Avoid recompilation<br>4. <strong>Optimize images</strong> - Use CDN for template assets<br>5. <strong>Minify output</strong> - Remove unnecessary whitespace in production<br>
            </article>
        </main>
    </div>
</body>
</html>