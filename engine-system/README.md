# Notification Engine System

A comprehensive TypeScript-based notification engine designed to handle multi-channel notifications, trigger evaluations, and template management for SaaS applications.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Getting Started](#getting-started)
- [Database Schema](#database-schema)
- [Queue System](#queue-system)
- [Template Engine](#template-engine)
- [API Documentation](#api-documentation)
- [Environment Configuration](#environment-configuration)
- [Development Guide](#development-guide)
- [Deployment](#deployment)
- [Documentation](#documentation)

## Overview

The Notification Engine System is a robust, scalable solution for managing notifications across multiple channels including email, Slack, and HubSpot. It features:

- **Multi-channel notifications** (<PERSON>ail, Slack, HubSpot CRM)
- **Template-based messaging** with Handlebars templating
- **Trigger-based automation** with configurable conditions
- **Queue-based processing** using BullMQ and RabbitMQ
- **Comprehensive audit logging** and notification tracking
- **Site-specific customization** and preferences
- **Real-time and scheduled notifications**

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Queue System   │    │   Connectors    │
│                 │    │                 │    │                 │
│ • REST APIs     │───▶│ • BullMQ/Redis  │───▶│ • Email (SMTP)  │
│ • Webhooks      │    │ • RabbitMQ      │    │ • Slack API     │
│ • Admin Panel   │    │ • Job Queues    │    │ • HubSpot CRM   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │ Template Engine │    │   External      │
│                 │    │                 │    │   Services      │
│ • MySQL/Prisma  │    │ • Handlebars    │    │                 │
│ • Notifications │    │ • Email/Slack   │    │ • Site APIs     │
│ • Templates     │    │ • Validation    │    │ • Analytics     │
│ • Triggers      │    │ • Rendering     │    │ • Integrations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

1. **API Layer** - Express.js REST API with middleware for authentication and validation
2. **Queue System** - Multi-queue architecture for job processing and message routing
3. **Template Engine** - Handlebars-based templating with layout support
4. **Database Layer** - Prisma ORM with MySQL for data persistence
5. **Connectors** - Channel-specific integrations for message delivery
6. **Job Processors** - Background workers for notification processing

## Technology Stack

### Core Technologies
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: MySQL with Prisma ORM
- **Queue System**: BullMQ (Redis) + RabbitMQ
- **Template Engine**: Handlebars.js
- **Validation**: Zod schemas

### Key Dependencies
- **@bull-board/express** - Queue monitoring dashboard
- **@slack/web-api** - Slack integration
- **nodemailer** - Email sending
- **axios** - HTTP client for external APIs
- **helmet** - Security middleware
- **ioredis** - Redis client
- **amqplib** - RabbitMQ client

### Development Tools
- **tsx** - TypeScript execution
- **copyfiles** - Build asset copying
- **pino** - Structured logging
- **dotenv** - Environment configuration

## Getting Started

### Prerequisites

- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- RabbitMQ 3.8+

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd notification-engine/engine-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Environment setup**
   ```bash
   cp env.sample .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   ```

5. **Start the application**
   ```bash
   npm run dev
   ```

### Docker Setup

```bash
docker-compose up -d
```

## Database Schema

The system uses a comprehensive database schema with the following core entities:

### Core Models

#### Notifications & Logging
- **NotificationLog** - Tracks all notification attempts and statuses
- **TemplateAuditLog** - Maintains template change history

#### Templates & Content
- **Template** - Stores notification templates with versioning
- **ContentType** - Defines template content formats (HTML, JSON, Block Kit, etc.)
- **Parameter** - Template and trigger parameter definitions

#### Triggers & Automation
- **Trigger** - Defines automation rules and conditions
- **TriggerType** - Categorizes trigger types
- **SiteTriggerSetting** - Site-specific trigger configurations
- **SiteNotificationPreference** - User notification preferences

#### Channel Management
- **ChannelType** - Supported notification channels (email, slack, hubspot)
- **SiteToken** - Encrypted access tokens for external services

### Key Relationships

```sql
NotificationLog ──▶ Trigger
NotificationLog ──▶ Template
NotificationLog ──▶ ChannelType

Template ──▶ Trigger
Template ──▶ ChannelType
Template ──▶ ContentType

Trigger ──▶ TriggerType
Trigger ──▶ Parameter[]

SiteTriggerSetting ──▶ Trigger
SiteNotificationPreference ──▶ ChannelType
SiteToken ──▶ ChannelType
```

### Database Features
- **Soft deletes** with `deletedAt` timestamps
- **Comprehensive indexing** for performance
- **UUID public IDs** for external references
- **JSON metadata** columns for flexibility
- **Audit trails** for compliance and debugging

## Queue System

The notification engine uses a dual-queue architecture combining BullMQ (Redis-based) and RabbitMQ for different use cases.

### Queue Architecture

#### BullMQ Queues (Redis-based)
```typescript
// High-performance job queues with built-in retry logic
EvaluationQueue    // Site evaluation and trigger checking
TriggerQueue       // Trigger processing and condition evaluation
NotificationQueue  // Notification delivery processing
BotQueue          // Bot and automation tasks
ObservationQueue  // Analytics and monitoring
```

#### RabbitMQ Queues (Message broker)
```typescript
// Distributed messaging with exchange routing
TriggerExchange    // Routes trigger events
NotificationExchange // Routes notification jobs
```

### Queue Configuration

Each queue supports:
- **Retry mechanisms** with exponential backoff
- **Dead letter queues** for failed jobs
- **Job prioritization** and scheduling
- **Concurrency control** and rate limiting
- **Monitoring and metrics** via Bull Board

### Job Processing Flow

```
Site Event ──▶ EvaluationQueue ──▶ TriggerQueue ──▶ NotificationQueue ──▶ Delivery
     │               │                  │                   │              │
     ▼               ▼                  ▼                   ▼              ▼
  Analytics    Condition Check    Template Render    Channel Send    Audit Log
```

## Template Engine

The template engine provides a powerful, flexible system for creating and managing notification templates across multiple channels.

### Features

- **Handlebars templating** with custom helpers
- **Layout inheritance** for consistent branding
- **Multi-channel support** (Email HTML, Slack Block Kit, etc.)
- **Parameter validation** with Zod schemas
- **Template versioning** and rollback capabilities
- **Hot reloading** in development mode

### Template Structure

```
templates/
├── layouts/           # Base layouts
│   ├── email.hbs     # Email HTML layout
│   └── slack.hbs     # Slack message layout
├── email/            # Email templates
│   ├── welcome.hbs
│   ├── reminder.hbs
│   └── summary.hbs
└── slack/            # Slack templates
    ├── alert.hbs
    └── notification.hbs
```

### Template Registry

Templates are registered with validation schemas:

```typescript
export const templateRegistry = {
  'weekly-summary': {
    email: {
      schema: z.object({
        siteName: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        metrics: z.object({
          sessions: z.number(),
          revenue: z.number()
        })
      })
    },
    slack: {
      schema: z.object({
        // Slack-specific schema
      })
    }
  }
}
```

### Template Examples

#### Email Template (Handlebars)
```handlebars
{{#> email-layout}}
  {{#content "header"}}
    <h1>Weekly Summary for {{siteName}}</h1>
  {{/content}}

  {{#content "body"}}
    <p>Here's your weekly summary from {{startDate}} to {{endDate}}:</p>
    <ul>
      <li>Sessions: {{metrics.sessions}}</li>
      <li>Revenue: ${{metrics.revenue}}</li>
    </ul>
  {{/content}}
{{/email-layout}}
```

#### Slack Template (Block Kit)
```handlebars
{
  "blocks": [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "Weekly Summary: {{siteName}}"
      }
    },
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*Sessions:* {{metrics.sessions}}\n*Revenue:* ${{metrics.revenue}}"
      }
    }
  ]
}
```

## API Documentation

### Core Endpoints

#### Trigger Management
```http
POST   /trigger/evaluation    # Trigger site evaluation
GET    /trigger/:id          # Get trigger details
PUT    /trigger/:id          # Update trigger configuration
```

#### Real-time Notifications
```http
POST   /real-time            # Send immediate notification
```

#### Slack Integration
```http
POST   /slack/auth           # OAuth authentication
POST   /slack/message        # Send Slack message
```

#### Admin & Monitoring
```http
GET    /up                   # Health check
GET    /admin/queues         # Queue monitoring dashboard
```

### Request/Response Examples

#### Send Real-time Notification
```http
POST /real-time
Content-Type: application/json

{
  "idSite": 123,
  "triggerName": "login-reminder",
  "templateData": {
    "userName": "John Doe",
    "daysSinceLogin": 7
  },
  "channels": ["email", "slack"]
}
```

#### Trigger Site Evaluation
```http
POST /trigger/evaluation
Content-Type: application/json

{
  "sites": [123, 456, 789],
  "triggerTypes": ["trial", "engagement"],
  "immediate": true
}
```

## Environment Configuration

### Required Environment Variables

```bash
# Application
NODE_ENV=development|production
PORT=3000
BUILD_TARGET=dev|prod

# Database
DATABASE_URL=mysql://user:password@localhost:3306/notifications

# Redis (BullMQ)
REDIS_HOST=localhost
REDIS_PORT=6379

# RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# Email (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
DEFAULT_EMAIL_FROM=<EMAIL>

# Slack Integration
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret
SLACK_REDIRECT_BASE_URL=https://yourapp.com

# Scheduling (Cron expressions)
TRIAL_SITES_SCHEDULE=0 8 * * *      # Daily at 8 AM
ACTIVE_SITES_SCHEDULE=0 0 * * 1     # Weekly on Monday
WEEK_METRICS_SCHEDULE=0 6 * * 1     # Weekly on Monday at 6 AM

# External Services
HEATMAP_CONTEXT_BASE_URL=https://api.yourservice.com
OBSERVATION_URL=https://observation.yourservice.com

# Security
ENCRYPTION_KEY=your-32-character-encryption-key

# Feature Flags
EMAIL_ON=true
LOGGER_TOGGLE=true
RUN_SEED=false
```

### Environment Validation

The application includes comprehensive environment validation:

```typescript
// Validates required variables, cron expressions, and data types
export const envConfig = {
  nodeEnv: process.env.NODE_ENV,     // Validated against allowed values
  port: Number(process.env.PORT),    // Parsed and validated
  redis: {
    host: process.env.REDIS_HOST,    // Required
    port: Number(process.env.REDIS_PORT)
  },
  // ... other validated configs
}
```

## Development Guide

### Project Structure

```
src/
├── server.ts              # Application entry point
├── app.ts                 # Express app configuration
├── db.ts                  # Database connection
├── env.constant.ts        # Environment validation
├── template-engine.ts     # Template processing
├── template-registry.ts   # Template definitions
├── api/                   # REST API layer
│   ├── controllers/       # Request handlers
│   ├── routes/           # Route definitions
│   ├── middlewares.ts    # Express middleware
│   ├── schemas/          # Validation schemas
│   └── utils/            # API utilities
├── jobs/                 # Queue job definitions
│   ├── evaluation/       # Site evaluation jobs
│   ├── trigger/          # Trigger processing jobs
│   ├── notification/     # Notification jobs
│   ├── bot/             # Bot automation jobs
│   └── observation/     # Analytics jobs
├── messaging/           # Queue and messaging
│   ├── queue-service.ts # RabbitMQ service
│   ├── consumers/       # Message consumers
│   └── producers/       # Message producers
├── connectors/          # External service integrations
│   ├── smtp.ts         # Email connector
│   ├── slack.ts        # Slack connector
│   └── hubspot.ts      # HubSpot connector
├── services/           # Business logic services
├── types/              # TypeScript type definitions
└── utils/              # Shared utilities
```

### Development Commands

```bash
# Development
npm run dev                # Start with hot reload
npm run build             # Build for production
npm run seed              # Run database seed

# Database
npx prisma generate       # Generate Prisma client
npx prisma db push        # Push schema changes
npx prisma studio        # Database GUI

# Queue Monitoring
# Visit http://localhost:3000/admin/queues
```

### Adding New Templates

1. **Create template files**
   ```bash
   # Email template
   touch src/new-templates/my-template.hbs

   # Slack template
   touch src/slack-templates/my-template.hbs
   ```

2. **Register in template registry**
   ```typescript
   // src/template-registry.ts
   export const templateRegistry = {
     'my-template': {
       email: {
         schema: z.object({
           // Define validation schema
         })
       },
       slack: {
         schema: z.object({
           // Define validation schema
         })
       }
     }
   }
   ```

3. **Create database entries**
   ```sql
   -- Add to triggers table
   INSERT INTO triggers (name, description) VALUES ('my-template', 'My template description');

   -- Add templates for each channel
   INSERT INTO templates (trigger_id, channel_id, name, body, content_type_id)
   VALUES (trigger_id, 1, 'my-template-email', 'template-content', 1);
   ```

### Adding New Job Types

1. **Create job definition**
   ```typescript
   // src/jobs/my-job/jobs.ts
   export interface MyJobData {
     siteId: number;
     // ... other properties
   }

   export async function processMyJob(job: Job<MyJobData>) {
     // Job processing logic
   }
   ```

2. **Add to queue**
   ```typescript
   // src/jobs/my-job/my-job-queue.ts
   export class MyJobQueue extends BaseQueue<MyJobData> {
     // Queue implementation
   }
   ```

3. **Register consumer**
   ```typescript
   // src/messaging/consumers/my-job.ts
   export async function startMyJobConsumer(queue: MyJobQueue) {
     // Consumer implementation
   }
   ```

## Deployment

### Docker Deployment

The application includes Docker configuration for containerized deployment:

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - mysql
      - redis
      - rabbitmq

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: notifications

  redis:
    image: redis:6-alpine

  rabbitmq:
    image: rabbitmq:3-management
```

### Production Considerations

1. **Environment Variables**
   - Use secure secret management
   - Enable SSL/TLS for external connections
   - Configure proper CORS settings

2. **Database**
   - Set up read replicas for scaling
   - Configure regular backups
   - Monitor query performance

3. **Queue System**
   - Configure Redis persistence
   - Set up RabbitMQ clustering
   - Monitor queue depths and processing times

4. **Monitoring**
   - Set up application performance monitoring
   - Configure log aggregation
   - Set up alerts for critical failures

5. **Security**
   - Enable rate limiting
   - Configure proper authentication
   - Regular security updates

### Health Checks

The application provides health check endpoints:

```http
GET /up                    # Basic health check
GET /admin/queues         # Queue status monitoring
```

### Logging

Structured logging with multiple levels:

```typescript
// Log levels: trace, debug, info, warn, error, fatal
logger.info('Application started', { port: 3000 });
logger.error('Database connection failed', { error: err.message });
```

---

## Documentation

### Comprehensive Guides

The system includes detailed documentation for all aspects:

- **[Database Schema](docs/DATABASE.md)** - Complete database documentation with models, relationships, and optimization
- **[Queue System](docs/QUEUES.md)** - Dual-queue architecture with BullMQ and RabbitMQ implementation details
- **[Template Engine](docs/TEMPLATES.md)** - Handlebars templating system with custom helpers and multi-channel support
- **[API Reference](docs/API.md)** - Complete REST API documentation with examples and schemas
- **[Testing Guide](docs/TESTING.md)** - Comprehensive testing strategies including unit, integration, and load testing
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment instructions for Docker, AWS, and Kubernetes
- **[Troubleshooting Guide](docs/TROUBLESHOOTING.md)** - Common issues, debugging techniques, and FAQ

### Quick Links

- **Setup & Installation**: See [Getting Started](#getting-started) section above
- **API Usage**: Check [API Documentation](docs/API.md) for endpoint details
- **Template Development**: Review [Template System](docs/TEMPLATES.md) guide
- **Queue Monitoring**: Use [Queue Dashboard](docs/QUEUES.md#monitoring) instructions
- **Performance Tuning**: Follow [Deployment Guide](docs/DEPLOYMENT.md#scaling-considerations)
- **Issue Resolution**: Consult [Troubleshooting Guide](docs/TROUBLESHOOTING.md)

---

## Support

For questions, issues, or contributions:

1. Check the [Issues](repository-url/issues) section
2. Review existing documentation in the `docs/` folder
3. Consult the [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
4. Contact the development team

## License

[License Type] - See LICENSE file for details
