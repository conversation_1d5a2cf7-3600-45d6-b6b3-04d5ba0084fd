# Documentation System Testing Guide

This guide provides comprehensive testing instructions for the unified documentation system.

## Quick Test (2 minutes)

### 1. Basic Functionality Test

```bash
# Navigate to the engine-system directory
cd engine-system

# Test the documentation generation
npm run docs

# Verify output files were created
ls -la DOCUMENTATION.md docs-site/
```

**Expected Results:**
- `DOCUMENTATION.md` file created (should be 50KB+ in size)
- `docs-site/` directory created with HTML files
- No error messages in console

### 2. Quick Visual Test

```bash
# Open the generated documentation site
open docs-site/index.html
# Or on Linux: xdg-open docs-site/index.html
# Or on Windows: start docs-site/index.html
```

**Expected Results:**
- Clean, professional-looking documentation site loads
- Navigation works between sections
- All content displays properly

## Comprehensive Testing (10 minutes)

### Step 1: Environment Setup

```bash
# Ensure you're in the correct directory
cd engine-system

# Install dependencies if not already done
npm install

# Verify TypeScript execution works
npx tsx --version
```

### Step 2: Configuration Validation

```bash
# Test configuration file
npm run docs:test

# Or run the test script directly
tsx scripts/test-docs.ts
```

**Expected Output:**
```
🧪 Running documentation tests...

📊 Test Results:
================

✅ Configuration file exists: docs-config.json found and valid
✅ Configuration has title: Field title present
✅ Configuration has description: Field description present
✅ Configuration has version: Field version present
✅ Configuration has structure: Field structure present
✅ Configuration has sections: Found 5 sections
✅ Configuration has files: Found 7 files across all sections
✅ Source files exist: Found 7 files, 0 missing
✅ Script docs.ts exists: Script file found
✅ NPM script docs: Script docs defined
✅ NPM script docs:merge: Script docs:merge defined
✅ NPM script docs:site: Script docs:site defined

📈 Summary: 12/12 tests passed
🎉 All tests passed! Documentation system is ready to use.
```

### Step 3: Individual Component Testing

#### Test Merged Documentation Generation

```bash
# Generate only merged documentation
npm run docs:merge

# Verify the output
ls -la DOCUMENTATION.md
wc -l DOCUMENTATION.md
head -20 DOCUMENTATION.md
```

**Expected Results:**
- File size: 50KB+ (varies based on content)
- Line count: 1000+ lines
- Proper header with title, description, version
- Table of contents with numbered sections
- All source files included

#### Test Site Generation

```bash
# Clean previous output
npm run docs:clean

# Generate only the documentation site
npm run docs:site

# Verify the site structure
find docs-site -type f -name "*.html" | head -10
ls -la docs-site/assets/
```

**Expected Results:**
- `docs-site/index.html` exists
- Section directories created (overview/, api/, architecture/, etc.)
- HTML files in each section directory
- `assets/` directory with CSS files
- No broken file references

### Step 4: Content Validation

#### Check Merged Documentation Content

```bash
# Verify table of contents
grep -n "## Table of Contents" DOCUMENTATION.md

# Check section headers
grep -n "^## [0-9]" DOCUMENTATION.md

# Verify source links are included
grep -c "Source:" DOCUMENTATION.md
```

#### Check Site Navigation

```bash
# Test that all HTML files are valid
for file in docs-site/**/*.html; do
  if ! grep -q "<html" "$file"; then
    echo "Invalid HTML: $file"
  fi
done

# Check for CSS and asset links
grep -r "assets/style.css" docs-site/ | wc -l
```

### Step 5: Error Handling Tests

#### Test Missing Configuration

```bash
# Backup original config
cp docs-config.json docs-config.json.backup

# Test with missing config
rm docs-config.json
npm run docs 2>&1 | grep "Configuration file not found"

# Restore config
mv docs-config.json.backup docs-config.json
```

#### Test Invalid Configuration

```bash
# Create invalid config
echo '{"invalid": "json"' > docs-config-invalid.json

# Test with invalid config
tsx scripts/docs.ts both docs-config-invalid.json 2>&1 | grep "Failed to load configuration"

# Clean up
rm docs-config-invalid.json
```

### Step 6: Performance Testing

```bash
# Time the generation process
time npm run docs

# Check output sizes
du -sh DOCUMENTATION.md docs-site/

# Count generated files
find docs-site -type f | wc -l
```

**Expected Performance:**
- Generation time: < 10 seconds
- Merged doc size: 50-200KB
- Site size: < 1MB
- File count: 10-20 files

## Advanced Testing (20 minutes)

### Test Custom Output Paths

```bash
# Test custom merged output
tsx scripts/docs.ts merge --output ./custom-docs.md
ls -la custom-docs.md

# Test custom site output
tsx scripts/docs.ts site --output-dir ./custom-site
ls -la custom-site/

# Clean up
rm -rf custom-docs.md custom-site/
```

### Test Different Commands

```bash
# Test help command
tsx scripts/docs.ts --help

# Test individual commands
tsx scripts/docs.ts merge
tsx scripts/docs.ts site
tsx scripts/docs.ts both
```

### Test Make Commands (if available)

```bash
# Test make commands
make docs-validate
make docs-stats
make docs-clean
make docs
```

## Troubleshooting Common Issues

### Issue: "Configuration file not found"

**Solution:**
```bash
# Ensure you're in the engine-system directory
pwd
ls -la docs-config.json

# If missing, check if you're in the right directory
cd engine-system
```

### Issue: "tsx command not found"

**Solution:**
```bash
# Install tsx globally
npm install -g tsx

# Or use npx
npx tsx scripts/docs.ts both
```

### Issue: Missing source files

**Solution:**
```bash
# Check which files are missing
tsx scripts/test-docs.ts

# Verify file paths in configuration
cat docs-config.json | grep -A 5 "file"
```

### Issue: Empty or broken HTML files

**Solution:**
```bash
# Clean and regenerate
npm run docs:clean
npm run docs

# Check for errors in generation
tsx scripts/docs.ts both 2>&1 | grep -i error
```

### Issue: Styling not applied

**Solution:**
```bash
# Verify CSS file exists
ls -la docs-site/assets/style.css

# Check CSS file size (should be > 1KB)
wc -c docs-site/assets/style.css

# Regenerate if needed
npm run docs:site
```

## Validation Checklist

Use this checklist to verify everything is working correctly:

- [ ] Configuration file loads without errors
- [ ] All source documentation files are found
- [ ] Merged documentation generates successfully
- [ ] Documentation site generates successfully
- [ ] HTML files are valid and complete
- [ ] CSS styling is applied correctly
- [ ] Navigation links work properly
- [ ] No broken internal links
- [ ] File sizes are reasonable (not empty, not too large)
- [ ] Generation completes in reasonable time (< 30 seconds)

## Automated Testing

For continuous integration, use:

```bash
# Run all tests
npm run docs:test && npm run docs && npm run docs:validate
```

This will:
1. Test the configuration and setup
2. Generate all documentation
3. Validate the output

## Performance Benchmarks

Expected performance on a modern machine:

| Operation | Time | Output Size |
|-----------|------|-------------|
| Merge docs | < 2 seconds | 50-200KB |
| Generate site | < 5 seconds | < 1MB |
| Full generation | < 10 seconds | Combined |

If performance is significantly slower, check:
- Large source files (> 1MB each)
- Network issues (if fetching remote content)
- Disk space availability
- System resources

## Next Steps

After successful testing:

1. **Customize the configuration** in `docs-config.json`
2. **Add your own documentation files** to the `docs/` directory
3. **Update the structure** in the configuration to match your files
4. **Set up automation** using the provided GitHub Actions workflow
5. **Deploy** the generated site to your hosting platform

For more information, see `DOCS_README.md` for complete usage instructions.
