# Documentation Generation Makefile
# Provides convenient commands for generating and managing documentation

.PHONY: help docs docs-merge docs-site docs-watch docs-dev docs-build docs-clean docs-serve docs-validate install-deps

# Default target
help: ## Show this help message
	@echo "📚 Documentation Generation Commands"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "Examples:"
	@echo "  make docs              # Generate both merged docs and site"
	@echo "  make docs-watch        # Generate and watch for changes"
	@echo "  make docs-serve        # Generate site and serve locally"
	@echo "  make docs-validate     # Validate generated documentation"

install-deps: ## Install Node.js dependencies
	@echo "📦 Installing dependencies..."
	npm install

docs: install-deps ## Generate both merged documentation and site
	@echo "📚 Generating complete documentation..."
	npm run docs

docs-merge: install-deps ## Generate merged markdown documentation only
	@echo "📄 Generating merged documentation..."
	npm run docs:merge

docs-site: install-deps ## Generate documentation site only
	@echo "🌐 Generating documentation site..."
	npm run docs:site

docs-watch: install-deps ## Generate documentation and watch for changes
	@echo "👀 Starting documentation watch mode..."
	@echo "Press Ctrl+C to stop"
	npm run docs:watch

docs-dev: install-deps ## Development mode with site generation and watching
	@echo "🔧 Starting documentation development mode..."
	@echo "Press Ctrl+C to stop"
	npm run docs:dev

docs-build: install-deps ## Build documentation for production
	@echo "🏗️  Building documentation for production..."
	npm run docs:build

docs-clean: ## Clean generated documentation files
	@echo "🧹 Cleaning generated documentation..."
	npm run docs:clean

docs-serve: docs-site ## Generate site and serve locally with Python
	@echo "🌍 Serving documentation site locally..."
	@echo "Open http://localhost:8000 in your browser"
	@echo "Press Ctrl+C to stop the server"
	@cd docs-site && python3 -m http.server 8000 2>/dev/null || python -m SimpleHTTPServer 8000

docs-serve-node: docs-site ## Generate site and serve locally with Node.js
	@echo "🌍 Serving documentation site locally with Node.js..."
	@echo "Open http://localhost:3000 in your browser"
	@echo "Press Ctrl+C to stop the server"
	@npx serve docs-site -p 3000

docs-validate: docs ## Validate generated documentation
	@echo "🔍 Validating generated documentation..."
	@if [ ! -f "DOCUMENTATION.md" ]; then \
		echo "❌ DOCUMENTATION.md not found"; \
		exit 1; \
	fi
	@if [ ! -d "docs-site" ]; then \
		echo "❌ docs-site directory not found"; \
		exit 1; \
	fi
	@if [ ! -f "docs-site/index.html" ]; then \
		echo "❌ docs-site/index.html not found"; \
		exit 1; \
	fi
	@echo "✅ Documentation validation passed"
	@echo "📊 Documentation statistics:"
	@wc -l DOCUMENTATION.md | awk '{print "  Merged doc lines: " $$1}'
	@find docs-site -name "*.html" | wc -l | awk '{print "  HTML pages: " $$1}'
	@du -sh docs-site | awk '{print "  Site size: " $$1}'

docs-stats: ## Show documentation statistics
	@echo "📊 Documentation Statistics"
	@echo "=========================="
	@if [ -f "DOCUMENTATION.md" ]; then \
		echo "Merged Documentation:"; \
		wc -l DOCUMENTATION.md | awk '{print "  Lines: " $$1}'; \
		wc -w DOCUMENTATION.md | awk '{print "  Words: " $$1}'; \
		wc -c DOCUMENTATION.md | awk '{print "  Characters: " $$1}'; \
		echo ""; \
	fi
	@if [ -d "docs-site" ]; then \
		echo "Documentation Site:"; \
		find docs-site -name "*.html" | wc -l | awk '{print "  HTML pages: " $$1}'; \
		find docs-site -name "*.css" | wc -l | awk '{print "  CSS files: " $$1}'; \
		find docs-site -name "*.js" | wc -l | awk '{print "  JS files: " $$1}'; \
		du -sh docs-site | awk '{print "  Total size: " $$1}'; \
		echo ""; \
	fi
	@echo "Source Documentation:"
	@find docs -name "*.md" | wc -l | awk '{print "  Markdown files: " $$1}'
	@find docs -name "*.md" -exec wc -l {} + | tail -1 | awk '{print "  Total lines: " $$1}'

docs-check-config: ## Validate documentation configuration
	@echo "🔧 Checking documentation configuration..."
	@if [ ! -f "docs-config.json" ]; then \
		echo "❌ docs-config.json not found"; \
		exit 1; \
	fi
	@echo "✅ Configuration file exists"
	@node -e "try { JSON.parse(require('fs').readFileSync('docs-config.json', 'utf8')); console.log('✅ Configuration is valid JSON'); } catch(e) { console.log('❌ Invalid JSON:', e.message); process.exit(1); }"

docs-open: ## Open generated documentation in browser
	@if [ -f "docs-site/index.html" ]; then \
		echo "🌐 Opening documentation site..."; \
		if command -v open >/dev/null 2>&1; then \
			open docs-site/index.html; \
		elif command -v xdg-open >/dev/null 2>&1; then \
			xdg-open docs-site/index.html; \
		elif command -v start >/dev/null 2>&1; then \
			start docs-site/index.html; \
		else \
			echo "Please open docs-site/index.html in your browser"; \
		fi; \
	else \
		echo "❌ Documentation site not found. Run 'make docs-site' first."; \
	fi

docs-deploy-prep: docs-build docs-validate ## Prepare documentation for deployment
	@echo "🚀 Preparing documentation for deployment..."
	@echo "✅ Documentation is ready for deployment"
	@echo ""
	@echo "Deployment options:"
	@echo "  - Copy docs-site/ to your web server"
	@echo "  - Use GitHub Pages with the generated files"
	@echo "  - Deploy to Netlify, Vercel, or similar platforms"
	@echo ""
	@echo "Files ready for deployment:"
	@find docs-site -type f | head -10
	@find docs-site -type f | wc -l | awk '{print "  Total files: " $$1}'

# Development helpers
dev-setup: install-deps docs-check-config ## Setup development environment
	@echo "🔧 Setting up documentation development environment..."
	@echo "✅ Development environment ready"
	@echo ""
	@echo "Next steps:"
	@echo "  make docs-dev    # Start development mode with watching"
	@echo "  make docs-serve  # Generate and serve documentation locally"

# CI/CD helpers
ci-docs: docs-build docs-validate ## CI/CD documentation generation
	@echo "🤖 CI/CD documentation generation completed"

# Quick commands
quick-merge: ## Quick merged documentation generation
	@tsx scripts/docs-cli.ts merge --verbose

quick-site: ## Quick site generation
	@tsx scripts/docs-cli.ts site --verbose

quick-both: ## Quick generation of both formats
	@tsx scripts/docs-cli.ts both --verbose
