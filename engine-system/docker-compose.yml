services:
    engine-system:
        build:
            context: .
            target: ${BUILD_TARGET:-prod}

        ports:
            - '${PORT:-3000}:${PORT:-3000}'
        env_file:
            - .env

        depends_on:
            rabbitmq:
                condition: service_healthy
                restart: true
            redis:
                condition: service_healthy
                restart: true
        restart: unless-stopped

    rabbitmq:
        image: rabbitmq:4-management
        ports:
            - '5772:5672'
            - '15772:15672'
        environment:
            RABBITMQ_DEFAULT_USER: ${RABBITMQ_USERNAME}
            RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
            #      RABBITMQ_DEFAULT_VHOST: /
        env_file:
            -   .env
        volumes:
            - rabbitmq-data:/var/lib/rabbitmq
        healthcheck:
            test: ['CMD', 'rabbitmqctl', 'status']
            interval: 10s
            timeout: 5s
            retries: 5

        restart: unless-stopped

    redis:
        image: redis:7.4
        ports:
            - '6479:6379'
        volumes:
            - redis-data:/data
        restart: unless-stopped
        healthcheck:
            test: ['CMD', 'redis-cli', 'ping']
            interval: 10s
            timeout: 10s
            retries: 5
            start_period: 10s

volumes:
    redis-data:
    rabbitmq-data:
