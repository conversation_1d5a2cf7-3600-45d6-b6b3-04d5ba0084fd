#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// Types for documentation configuration
interface DocFile {
  id: string;
  title: string;
  file: string;
  description: string;
  order: number;
}

interface DocSection {
  id: string;
  title: string;
  description: string;
  order: number;
  files: DocFile[];
}

interface DocConfig {
  title: string;
  description: string;
  version: string;
  baseUrl: string;
  structure: {
    sections: DocSection[];
  };
  navigation: {
    showSectionNumbers: boolean;
    showFileNumbers: boolean;
    generateToc: boolean;
    maxTocDepth: number;
  };
  output: {
    formats: string[];
    mergedFile: string;
    siteDir: string;
    includeSourceLinks: boolean;
    includeLastModified: boolean;
  };
  styling: {
    theme: string;
    codeHighlighting: boolean;
    responsiveDesign: boolean;
  };
}

class DocumentationGenerator {
  private config: DocConfig;
  private rootDir: string;

  constructor(configPath: string) {
    this.rootDir = path.dirname(configPath);
    this.config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
  }

  /**
   * Generate documentation in the specified formats
   */
  async generate(formats?: string[]): Promise<void> {
    const outputFormats = formats || this.config.output.formats;

    console.log(`🚀 Generating documentation for: ${outputFormats.join(', ')}`);

    for (const format of outputFormats) {
      switch (format) {
        case 'merged':
          await this.generateMergedDocs();
          break;
        case 'site':
          await this.generateSite();
          break;
        default:
          console.warn(`⚠️  Unknown format: ${format}`);
      }
    }

    console.log('✅ Documentation generation complete!');
  }

  /**
   * Generate a single merged markdown file
   */
  private async generateMergedDocs(): Promise<void> {
    console.log('📄 Generating merged documentation...');

    let mergedContent = this.generateHeader();
    mergedContent += this.generateTableOfContents();

    // Sort sections by order
    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      mergedContent += this.generateSectionContent(section);
    }

    mergedContent += this.generateFooter();

    const outputPath = path.join(this.rootDir, this.config.output.mergedFile);
    fs.writeFileSync(outputPath, mergedContent, 'utf-8');

    console.log(`✅ Merged documentation saved to: ${outputPath}`);
  }

  /**
   * Generate documentation site with navigation
   */
  private async generateSite(): Promise<void> {
    console.log('🌐 Generating documentation site...');

    const siteDir = path.join(this.rootDir, this.config.output.siteDir);

    // Create site directory
    if (!fs.existsSync(siteDir)) {
      fs.mkdirSync(siteDir, { recursive: true });
    }

    // Generate index page
    await this.generateIndexPage(siteDir);

    // Generate individual pages for each section
    for (const section of this.config.structure.sections) {
      await this.generateSectionPage(section, siteDir);
    }

    // Generate navigation and assets
    await this.generateNavigation(siteDir);
    await this.generateAssets(siteDir);

    console.log(`✅ Documentation site generated in: ${siteDir}`);
  }

  /**
   * Generate the header for merged documentation
   */
  private generateHeader(): string {
    const lastModified = this.config.output.includeLastModified
      ? `\n*Last updated: ${new Date().toISOString().split('T')[0]}*\n`
      : '';

    return `# ${this.config.title}

${this.config.description}

**Version:** ${this.config.version}${lastModified}

---

`;
  }

  /**
   * Generate table of contents
   */
  private generateTableOfContents(): string {
    if (!this.config.navigation.generateToc) {
      return '';
    }

    let toc = '## Table of Contents\n\n';

    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      const sectionNumber = this.config.navigation.showSectionNumbers ? `${section.order}. ` : '';
      toc += `${sectionNumber}[${section.title}](#${this.slugify(section.title)})\n`;

      const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);
      for (const file of sortedFiles) {
        const fileNumber = this.config.navigation.showFileNumbers ? `${section.order}.${file.order} ` : '';
        toc += `   ${fileNumber}[${file.title}](#${this.slugify(file.title)})\n`;
      }
      toc += '\n';
    }

    return toc + '---\n\n';
  }

  /**
   * Generate content for a section
   */
  private generateSectionContent(section: DocSection): string {
    let content = `## ${section.title}\n\n${section.description}\n\n`;

    const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);

    for (const file of sortedFiles) {
      content += this.generateFileContent(file, section);
    }

    return content;
  }

  /**
   * Generate content for a file
   */
  private generateFileContent(file: DocFile, section: DocSection): string {
    const filePath = path.join(this.rootDir, file.file);

    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  File not found: ${filePath}`);
      return `### ${file.title}\n\n*File not found: ${file.file}*\n\n`;
    }

    let fileContent = fs.readFileSync(filePath, 'utf-8');

    // Remove the first H1 heading if it exists to avoid duplicate titles
    fileContent = fileContent.replace(/^#\s+.*$/m, '');

    // Add source link if enabled
    const sourceLink = this.config.output.includeSourceLinks
      ? `\n\n*Source: [${file.file}](${file.file})*\n`
      : '';

    return `### ${file.title}\n\n${fileContent.trim()}${sourceLink}\n\n---\n\n`;
  }

  /**
   * Generate footer
   */
  private generateFooter(): string {
    return `---

*Generated by Documentation Generator v${this.config.version}*
*Generated on: ${new Date().toISOString()}*
`;
  }

  /**
   * Convert text to URL-friendly slug
   */
  private slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Generate index page for the site
   */
  private async generateIndexPage(siteDir: string): Promise<void> {
    const indexContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.config.title}</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>${this.config.title}</h1>
            <p>${this.config.description}</p>
        </header>

        <nav class="main-nav">
            ${this.generateNavigationHTML()}
        </nav>

        <main>
            <div class="welcome-section">
                <h2>Welcome to the Documentation</h2>
                <p>Select a section from the navigation to get started.</p>
            </div>
        </main>
    </div>
    <script src="assets/script.js"></script>
</body>
</html>`;

    fs.writeFileSync(path.join(siteDir, 'index.html'), indexContent);
  }

  /**
   * Generate navigation HTML
   */
  private generateNavigationHTML(): string {
    let nav = '<ul class="nav-sections">';

    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      nav += `<li class="nav-section">
        <a href="#${section.id}" class="section-link">${section.title}</a>
        <ul class="nav-files">`;

      const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);
      for (const file of sortedFiles) {
        nav += `<li><a href="${section.id}/${file.id}.html">${file.title}</a></li>`;
      }

      nav += '</ul></li>';
    }

    nav += '</ul>';
    return nav;
  }

  /**
   * Generate individual section pages
   */
  private async generateSectionPage(section: DocSection, siteDir: string): Promise<void> {
    const sectionDir = path.join(siteDir, section.id);
    if (!fs.existsSync(sectionDir)) {
      fs.mkdirSync(sectionDir, { recursive: true });
    }

    const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);

    for (const file of sortedFiles) {
      const filePath = path.join(this.rootDir, file.file);

      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️  File not found: ${filePath}`);
        continue;
      }

      let markdownContent = fs.readFileSync(filePath, 'utf-8');
      const htmlContent = this.markdownToHtml(markdownContent);

      const pageContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${file.title} - ${this.config.title}</title>
    <link rel="stylesheet" href="../assets/style.css">
    <link rel="stylesheet" href="../assets/highlight.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">${this.config.title}</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> >
                <span>${section.title}</span> >
                <span>${file.title}</span>
            </nav>
        </header>

        <div class="content-wrapper">
            <nav class="sidebar">
                ${this.generateNavigationHTML()}
            </nav>

            <main class="content">
                <article>
                    ${htmlContent}
                </article>

                <footer class="page-footer">
                    ${this.config.output.includeSourceLinks ?
                      `<p><a href="../../${file.file}">View source</a></p>` : ''}
                    ${this.config.output.includeLastModified ?
                      `<p>Last modified: ${this.getFileModifiedDate(filePath)}</p>` : ''}
                </footer>
            </main>
        </div>
    </div>
    <script src="../assets/script.js"></script>
</body>
</html>`;

      fs.writeFileSync(path.join(sectionDir, `${file.id}.html`), pageContent);
    }
  }

  /**
   * Generate navigation files
   */
  private async generateNavigation(siteDir: string): Promise<void> {
    // Generate sitemap
    const sitemap = this.generateSitemap();
    fs.writeFileSync(path.join(siteDir, 'sitemap.xml'), sitemap);

    // Generate search index (simple JSON for client-side search)
    const searchIndex = this.generateSearchIndex();
    fs.writeFileSync(path.join(siteDir, 'search-index.json'), JSON.stringify(searchIndex, null, 2));
  }

  /**
   * Generate CSS and JS assets
   */
  private async generateAssets(siteDir: string): Promise<void> {
    const assetsDir = path.join(siteDir, 'assets');
    if (!fs.existsSync(assetsDir)) {
      fs.mkdirSync(assetsDir, { recursive: true });
    }

    // Generate CSS
    const css = this.generateCSS();
    fs.writeFileSync(path.join(assetsDir, 'style.css'), css);

    // Generate highlight.js CSS for code highlighting
    const highlightCSS = this.generateHighlightCSS();
    fs.writeFileSync(path.join(assetsDir, 'highlight.css'), highlightCSS);

    // Generate JavaScript
    const js = this.generateJavaScript();
    fs.writeFileSync(path.join(assetsDir, 'script.js'), js);
  }

  /**
   * Convert markdown to HTML (basic implementation)
   */
  private markdownToHtml(markdown: string): string {
    // This is a basic markdown to HTML converter
    // In a production environment, you'd want to use a proper markdown parser like marked or markdown-it

    let html = markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code class="language-$1">$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      // Line breaks
      .replace(/\n/gim, '<br>');

    return html;
  }

  /**
   * Get file modification date
   */
  private getFileModifiedDate(filePath: string): string {
    try {
      const stats = fs.statSync(filePath);
      return stats.mtime.toISOString().split('T')[0];
    } catch {
      return 'Unknown';
    }
  }

  /**
   * Generate sitemap XML
   */
  private generateSitemap(): string {
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${this.config.baseUrl}/</loc>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>`;

    for (const section of this.config.structure.sections) {
      for (const file of section.files) {
        sitemap += `
  <url>
    <loc>${this.config.baseUrl}/${section.id}/${file.id}.html</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
      }
    }

    sitemap += '\n</urlset>';
    return sitemap;
  }

  /**
   * Generate search index for client-side search
   */
  private generateSearchIndex(): any[] {
    const index = [];

    for (const section of this.config.structure.sections) {
      for (const file of section.files) {
        const filePath = path.join(this.rootDir, file.file);

        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf-8');

          index.push({
            id: `${section.id}-${file.id}`,
            title: file.title,
            section: section.title,
            description: file.description,
            url: `${section.id}/${file.id}.html`,
            content: content.substring(0, 500), // First 500 chars for search
            keywords: this.extractKeywords(content)
          });
        }
      }
    }

    return index;
  }

  /**
   * Extract keywords from content for search
   */
  private extractKeywords(content: string): string[] {
    // Simple keyword extraction - remove markdown and get common words
    const cleanContent = content
      .replace(/[#*`\[\]()]/g, ' ')
      .replace(/\s+/g, ' ')
      .toLowerCase();

    const words = cleanContent.split(' ')
      .filter(word => word.length > 3)
      .filter(word => !['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'].includes(word));

    // Return unique words, limited to top 10
    return [...new Set(words)].slice(0, 10);
  }

  /**
   * Generate CSS styles
   */
  private generateCSS(): string {
    return `/* Documentation Site Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

header {
  background: #2c3e50;
  color: white;
  padding: 1rem 2rem;
  border-bottom: 3px solid #3498db;
}

header h1 {
  margin: 0;
  font-size: 1.8rem;
}

header h1 a {
  color: white;
  text-decoration: none;
}

.breadcrumb {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.breadcrumb a {
  color: #3498db;
  text-decoration: none;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 120px);
}

.sidebar {
  width: 280px;
  background: #ecf0f1;
  padding: 1.5rem;
  border-right: 1px solid #bdc3c7;
  overflow-y: auto;
}

.nav-sections {
  list-style: none;
}

.nav-section {
  margin-bottom: 1rem;
}

.section-link {
  font-weight: bold;
  color: #2c3e50;
  text-decoration: none;
  display: block;
  padding: 0.5rem 0;
  border-bottom: 1px solid #bdc3c7;
}

.nav-files {
  list-style: none;
  margin-top: 0.5rem;
}

.nav-files li {
  margin-left: 1rem;
}

.nav-files a {
  color: #7f8c8d;
  text-decoration: none;
  padding: 0.25rem 0;
  display: block;
  font-size: 0.9rem;
}

.nav-files a:hover {
  color: #3498db;
}

.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

article {
  max-width: 800px;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; border-bottom: 2px solid #ecf0f1; padding-bottom: 0.5rem; }
h3 { font-size: 1.5rem; }

p {
  margin-bottom: 1rem;
}

code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
}

pre {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1rem 0;
}

pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.page-footer {
  margin-top: 3rem;
  padding-top: 1rem;
  border-top: 1px solid #ecf0f1;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.welcome-section {
  text-align: center;
  padding: 3rem 2rem;
}

@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .content {
    order: 1;
  }
}`;
  }

  /**
   * Generate highlight.js CSS for code syntax highlighting
   */
  private generateHighlightCSS(): string {
    return `/* Code Highlighting Styles */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #2c3e50;
  color: #ecf0f1;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: #3498db;
}

.hljs-string,
.hljs-title,
.hljs-name,
.hljs-type,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: #e74c3c;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
  color: #95a5a6;
}

.hljs-number,
.hljs-regexp,
.hljs-literal {
  color: #f39c12;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}`;
  }

  /**
   * Generate JavaScript for interactive features
   */
  private generateJavaScript(): string {
    return `// Documentation Site JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Add smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Highlight current page in navigation
  const currentPath = window.location.pathname;
  document.querySelectorAll('.nav-files a').forEach(link => {
    if (link.getAttribute('href') === currentPath.split('/').pop()) {
      link.style.color = '#3498db';
      link.style.fontWeight = 'bold';
    }
  });

  // Add copy button to code blocks
  document.querySelectorAll('pre code').forEach(block => {
    const button = document.createElement('button');
    button.textContent = 'Copy';
    button.className = 'copy-button';
    button.style.cssText = \`
      position: absolute;
      top: 5px;
      right: 5px;
      background: #3498db;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
    \`;

    const pre = block.parentElement;
    pre.style.position = 'relative';
    pre.appendChild(button);

    button.addEventListener('click', () => {
      navigator.clipboard.writeText(block.textContent).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 2000);
      });
    });
  });

  // Simple search functionality
  const searchInput = document.getElementById('search');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const query = this.value.toLowerCase();
      document.querySelectorAll('.nav-files a').forEach(link => {
        const text = link.textContent.toLowerCase();
        const listItem = link.parentElement;
        if (text.includes(query) || query === '') {
          listItem.style.display = 'block';
        } else {
          listItem.style.display = 'none';
        }
      });
    });
  }
});`;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const configPath = args[0] || 'docs-config.json';
  const formats = args.slice(1);

  if (!fs.existsSync(configPath)) {
    console.error(\`❌ Configuration file not found: \${configPath}\`);
    console.log('Usage: tsx docs-generator.ts [config-file] [format1] [format2]...');
    console.log('Formats: merged, site');
    process.exit(1);
  }

  try {
    const generator = new DocumentationGenerator(configPath);
    await generator.generate(formats.length > 0 ? formats : undefined);
  } catch (error) {
    console.error('❌ Error generating documentation:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === \`file://\${process.argv[1]}\`) {
  main();
}
