#!/usr/bin/env tsx

/**
 * Manual test script for user deletion functionality
 * Run with: tsx scripts/test-user-deletion.ts
 */

import { UserService } from '../src/modules/users/services/user.service';
import { UserRepository } from '../src/modules/users/repositories/user.repository';
import { UserReassignmentService } from '../src/modules/users/services/user-reassignment.service';
import prisma from '../src/db';
import Logger from '../src/utils/logger';

const logger = Logger.getInstance();

async function createTestData() {
  console.log('🔧 Creating test data...');
  
  const testUserId = '123e4567-e89b-12d3-a456-************';
  const adminUserId = '987fcdeb-51a2-43d1-b789-123456789abc';

  try {
    // Create a test trigger
    const trigger = await prisma.trigger.create({
      data: {
        name: `test-trigger-${Date.now()}`,
        triggerTypeId: 1, // Assuming this exists
        createdBy: testUserId,
        updatedBy: testUserId,
      }
    });
    console.log(`✅ Created test trigger: ${trigger.id}`);

    // Create a test template
    const template = await prisma.template.create({
      data: {
        name: `test-template-${Date.now()}`,
        body: 'Test template body for user deletion testing',
        channelId: 1, // Assuming this exists
        contentTypeId: 1, // Assuming this exists
        createdBy: testUserId,
        updatedBy: testUserId,
      }
    });
    console.log(`✅ Created test template: ${template.id}`);

    // Create a test audit log
    const auditLog = await prisma.templateAuditLog.create({
      data: {
        templateId: template.id,
        action: 'created',
        performedBy: testUserId,
      }
    });
    console.log(`✅ Created test audit log: ${auditLog.id}`);

    return { testUserId, adminUserId, trigger, template, auditLog };
  } catch (error) {
    console.error('❌ Error creating test data:', error);
    throw error;
  }
}

async function testConstraintCheck(userService: UserService, userId: string) {
  console.log('\n📋 Testing constraint check...');
  
  try {
    const hasConstraints = await userService.hasConstrainingContent(userId);
    console.log(`✅ User ${userId} has constraining content: ${hasConstraints}`);
    return hasConstraints;
  } catch (error) {
    console.error('❌ Error checking constraints:', error);
    return false;
  }
}

async function testUserDeletion(userService: UserService, userId: string, adminUserId: string) {
  console.log('\n🗑️ Testing user deletion with reassignment...');
  
  try {
    const result = await userService.deleteUserWithReassignment(userId, adminUserId);
    
    if (result.success) {
      console.log('✅ User deletion completed successfully!');
      console.log(`   Direct deletion: ${result.deletedDirectly}`);
      
      if (result.reassignmentSummary) {
        console.log(`   Total reassigned: ${result.reassignmentSummary.totalReassigned}`);
        result.reassignmentSummary.results.forEach(r => {
          const status = r.success ? '✅' : '❌';
          console.log(`   ${status} ${r.entityType}: ${r.reassignedCount} items`);
          if (r.error) console.log(`      Error: ${r.error}`);
        });
      }
    } else {
      console.log('❌ User deletion failed!');
      console.log(`   Error: ${result.error}`);
    }
    
    return result;
  } catch (error) {
    console.error('❌ Error during user deletion:', error);
    return null;
  }
}

async function verifyReassignment(testData: any) {
  console.log('\n🔍 Verifying reassignment...');
  
  try {
    const { adminUserId, trigger, template } = testData;
    
    // Check trigger reassignment
    const updatedTrigger = await prisma.trigger.findUnique({
      where: { id: trigger.id }
    });
    
    if (updatedTrigger?.createdBy === adminUserId) {
      console.log('✅ Trigger successfully reassigned to admin');
    } else {
      console.log('❌ Trigger reassignment failed');
    }
    
    // Check template reassignment
    const updatedTemplate = await prisma.template.findUnique({
      where: { id: template.id }
    });
    
    if (updatedTemplate?.createdBy === adminUserId) {
      console.log('✅ Template successfully reassigned to admin');
    } else {
      console.log('❌ Template reassignment failed');
    }
    
    // Check audit log reassignment
    const auditLogs = await prisma.templateAuditLog.findMany({
      where: { templateId: template.id }
    });
    
    if (auditLogs.length > 0 && auditLogs[0].performedBy === adminUserId) {
      console.log('✅ Audit log successfully reassigned to admin');
    } else {
      console.log('❌ Audit log reassignment failed');
    }
    
  } catch (error) {
    console.error('❌ Error verifying reassignment:', error);
  }
}

async function cleanupTestData(testData: any) {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    const { trigger, template, auditLog } = testData;
    
    await prisma.templateAuditLog.delete({ where: { id: auditLog.id } });
    await prisma.template.delete({ where: { id: template.id } });
    await prisma.trigger.delete({ where: { id: trigger.id } });
    
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
}

async function testErrorScenarios(userService: UserService) {
  console.log('\n⚠️ Testing error scenarios...');
  
  // Test invalid UUID
  console.log('Testing invalid UUID...');
  const result1 = await userService.deleteUserWithReassignment(
    'invalid-uuid',
    '987fcdeb-51a2-43d1-b789-123456789abc'
  );
  console.log(`Result: ${result1.success ? 'Success' : 'Failed'} - ${result1.error}`);
  
  // Test self-reassignment
  console.log('Testing self-reassignment...');
  const sameId = '123e4567-e89b-12d3-a456-************';
  const result2 = await userService.deleteUserWithReassignment(sameId, sameId);
  console.log(`Result: ${result2.success ? 'Success' : 'Failed'} - ${result2.error}`);
  
  // Test empty parameters
  console.log('Testing empty parameters...');
  const result3 = await userService.deleteUserWithReassignment('', '');
  console.log(`Result: ${result3.success ? 'Success' : 'Failed'} - ${result3.error}`);
}

async function main() {
  console.log('🚀 Starting User Deletion Tests\n');
  
  // Initialize services
  const userRepository = new UserRepository();
  const reassignmentService = new UserReassignmentService();
  const userService = new UserService(userRepository, reassignmentService);
  
  let testData: any = null;
  
  try {
    // Create test data
    testData = await createTestData();
    
    // Test constraint checking
    await testConstraintCheck(userService, testData.testUserId);
    
    // Test user deletion
    await testUserDeletion(userService, testData.testUserId, testData.adminUserId);
    
    // Verify reassignment worked
    await verifyReassignment(testData);
    
    // Test error scenarios
    await testErrorScenarios(userService);
    
    console.log('\n✨ All tests completed!');
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    // Clean up
    if (testData) {
      await cleanupTestData(testData);
    }
    
    await prisma.$disconnect();
  }
}

// Run the tests
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main as runUserDeletionTests };
