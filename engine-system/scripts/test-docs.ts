#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
}

class DocumentationTester {
  private results: TestResult[] = [];
  private rootDir: string;

  constructor(rootDir: string = '.') {
    this.rootDir = rootDir;
  }

  /**
   * Run all documentation tests
   */
  async runTests(): Promise<boolean> {
    console.log('🧪 Running documentation tests...\n');

    // Test configuration
    this.testConfigurationFile();
    this.testConfigurationStructure();

    // Test source files
    this.testSourceFiles();

    // Test scripts
    this.testScriptFiles();

    // Test generated files (if they exist)
    this.testGeneratedFiles();

    // Test npm scripts
    this.testNpmScripts();

    // Print results
    this.printResults();

    return this.results.every(result => result.passed);
  }

  /**
   * Test if configuration file exists and is valid
   */
  private testConfigurationFile(): void {
    const configPath = path.join(this.rootDir, 'docs-config.json');
    
    if (!fs.existsSync(configPath)) {
      this.addResult('Configuration file exists', false, 'docs-config.json not found');
      return;
    }

    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
      this.addResult('Configuration file exists', true, 'docs-config.json found and valid');
      
      // Test required fields
      const requiredFields = ['title', 'description', 'version', 'structure'];
      for (const field of requiredFields) {
        if (!config[field]) {
          this.addResult(`Configuration has ${field}`, false, `Missing required field: ${field}`);
        } else {
          this.addResult(`Configuration has ${field}`, true, `Field ${field} present`);
        }
      }
    } catch (error) {
      this.addResult('Configuration file valid', false, `Invalid JSON: ${error}`);
    }
  }

  /**
   * Test configuration structure
   */
  private testConfigurationStructure(): void {
    const configPath = path.join(this.rootDir, 'docs-config.json');
    
    if (!fs.existsSync(configPath)) {
      return; // Already tested above
    }

    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
      
      if (config.structure && config.structure.sections) {
        const sections = config.structure.sections;
        this.addResult('Configuration has sections', true, `Found ${sections.length} sections`);
        
        let totalFiles = 0;
        for (const section of sections) {
          if (section.files && Array.isArray(section.files)) {
            totalFiles += section.files.length;
          }
        }
        this.addResult('Configuration has files', true, `Found ${totalFiles} files across all sections`);
      } else {
        this.addResult('Configuration structure', false, 'Missing structure.sections');
      }
    } catch (error) {
      // Already handled above
    }
  }

  /**
   * Test if source documentation files exist
   */
  private testSourceFiles(): void {
    const configPath = path.join(this.rootDir, 'docs-config.json');
    
    if (!fs.existsSync(configPath)) {
      return;
    }

    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
      
      if (config.structure && config.structure.sections) {
        let foundFiles = 0;
        let missingFiles = 0;
        
        for (const section of config.structure.sections) {
          if (section.files) {
            for (const file of section.files) {
              const filePath = path.join(this.rootDir, file.file);
              if (fs.existsSync(filePath)) {
                foundFiles++;
              } else {
                missingFiles++;
                console.warn(`⚠️  Missing file: ${file.file}`);
              }
            }
          }
        }
        
        this.addResult('Source files exist', missingFiles === 0, 
          `Found ${foundFiles} files, ${missingFiles} missing`);
      }
    } catch (error) {
      // Already handled above
    }
  }

  /**
   * Test if script files exist
   */
  private testScriptFiles(): void {
    const scriptFiles = [
      'scripts/docs-cli.ts',
      'scripts/merge-docs.ts',
      'scripts/generate-site.ts',
      'scripts/test-docs.ts'
    ];

    for (const scriptFile of scriptFiles) {
      const scriptPath = path.join(this.rootDir, scriptFile);
      const exists = fs.existsSync(scriptPath);
      this.addResult(`Script ${scriptFile} exists`, exists, 
        exists ? 'Script file found' : 'Script file missing');
    }
  }

  /**
   * Test generated files (if they exist)
   */
  private testGeneratedFiles(): void {
    const mergedDocPath = path.join(this.rootDir, 'DOCUMENTATION.md');
    const siteDirPath = path.join(this.rootDir, 'docs-site');

    // Test merged documentation
    if (fs.existsSync(mergedDocPath)) {
      const stats = fs.statSync(mergedDocPath);
      this.addResult('Merged documentation exists', true, 
        `DOCUMENTATION.md found (${(stats.size / 1024).toFixed(1)} KB)`);
    } else {
      this.addResult('Merged documentation exists', false, 
        'DOCUMENTATION.md not found (run generation first)');
    }

    // Test documentation site
    if (fs.existsSync(siteDirPath)) {
      const indexPath = path.join(siteDirPath, 'index.html');
      const assetsPath = path.join(siteDirPath, 'assets');
      
      this.addResult('Documentation site exists', true, 'docs-site directory found');
      this.addResult('Site index exists', fs.existsSync(indexPath), 
        fs.existsSync(indexPath) ? 'index.html found' : 'index.html missing');
      this.addResult('Site assets exist', fs.existsSync(assetsPath), 
        fs.existsSync(assetsPath) ? 'assets directory found' : 'assets directory missing');
    } else {
      this.addResult('Documentation site exists', false, 
        'docs-site directory not found (run generation first)');
    }
  }

  /**
   * Test npm scripts
   */
  private testNpmScripts(): void {
    const packagePath = path.join(this.rootDir, 'package.json');
    
    if (!fs.existsSync(packagePath)) {
      this.addResult('Package.json exists', false, 'package.json not found');
      return;
    }

    try {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf-8'));
      
      if (packageJson.scripts) {
        const expectedScripts = [
          'docs',
          'docs:merge',
          'docs:site',
          'docs:watch',
          'docs:build',
          'docs:clean'
        ];

        for (const script of expectedScripts) {
          const exists = !!packageJson.scripts[script];
          this.addResult(`NPM script ${script}`, exists, 
            exists ? `Script ${script} defined` : `Script ${script} missing`);
        }
      } else {
        this.addResult('NPM scripts section', false, 'No scripts section in package.json');
      }
    } catch (error) {
      this.addResult('Package.json valid', false, `Invalid JSON: ${error}`);
    }
  }

  /**
   * Add a test result
   */
  private addResult(name: string, passed: boolean, message: string): void {
    this.results.push({ name, passed, message });
  }

  /**
   * Print test results
   */
  private printResults(): void {
    console.log('\n📊 Test Results:');
    console.log('================\n');

    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;

    for (const result of this.results) {
      const icon = result.passed ? '✅' : '❌';
      console.log(`${icon} ${result.name}: ${result.message}`);
    }

    console.log(`\n📈 Summary: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All tests passed! Documentation system is ready to use.');
    } else {
      console.log('⚠️  Some tests failed. Please check the issues above.');
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const rootDir = args[0] || '.';

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🧪 Documentation System Tester

Usage: tsx test-docs.ts [directory] [options]

Options:
  --help, -h    Show this help message

Examples:
  tsx test-docs.ts                    # Test current directory
  tsx test-docs.ts /path/to/project   # Test specific directory
`);
    process.exit(0);
  }

  try {
    const tester = new DocumentationTester(rootDir);
    const success = await tester.runTests();
    
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Error running tests:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DocumentationTester };
