#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

interface MergeOptions {
  configPath: string;
  outputPath?: string;
  includeTableOfContents?: boolean;
  includeSectionNumbers?: boolean;
  includeFileNumbers?: boolean;
  includeSourceLinks?: boolean;
  includeLastModified?: boolean;
  maxTocDepth?: number;
}

interface DocFile {
  id: string;
  title: string;
  file: string;
  description: string;
  order: number;
}

interface DocSection {
  id: string;
  title: string;
  description: string;
  order: number;
  files: DocFile[];
}

interface DocConfig {
  title: string;
  description: string;
  version: string;
  structure: {
    sections: DocSection[];
  };
  navigation: {
    showSectionNumbers: boolean;
    showFileNumbers: boolean;
    generateToc: boolean;
    maxTocDepth: number;
  };
  output: {
    mergedFile: string;
    includeSourceLinks: boolean;
    includeLastModified: boolean;
  };
}

class MarkdownMerger {
  private config: DocConfig;
  private rootDir: string;
  private options: MergeOptions;

  constructor(options: MergeOptions) {
    this.options = options;
    this.rootDir = path.dirname(options.configPath);
    this.config = JSON.parse(fs.readFileSync(options.configPath, 'utf-8'));
  }

  /**
   * Merge all markdown files into a single document
   */
  async merge(): Promise<string> {
    console.log('📄 Merging documentation files...');
    
    let mergedContent = this.generateHeader();
    
    if (this.shouldIncludeToc()) {
      mergedContent += this.generateTableOfContents();
    }
    
    // Sort sections by order
    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);
    
    for (const section of sortedSections) {
      mergedContent += this.generateSectionContent(section);
    }
    
    mergedContent += this.generateFooter();
    
    // Write to file if output path is specified
    if (this.options.outputPath) {
      fs.writeFileSync(this.options.outputPath, mergedContent, 'utf-8');
      console.log(`✅ Merged documentation saved to: ${this.options.outputPath}`);
    }
    
    return mergedContent;
  }

  /**
   * Generate the document header
   */
  private generateHeader(): string {
    const lastModified = this.shouldIncludeLastModified()
      ? `\n*Last updated: ${new Date().toISOString().split('T')[0]}*\n`
      : '';
    
    return `# ${this.config.title}

${this.config.description}

**Version:** ${this.config.version}${lastModified}

---

`;
  }

  /**
   * Generate table of contents
   */
  private generateTableOfContents(): string {
    let toc = '## Table of Contents\n\n';
    
    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);
    
    for (const section of sortedSections) {
      const sectionNumber = this.shouldIncludeSectionNumbers() ? `${section.order}. ` : '';
      toc += `${sectionNumber}[${section.title}](#${this.slugify(section.title)})\n`;
      
      const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);
      for (const file of sortedFiles) {
        const fileNumber = this.shouldIncludeFileNumbers() ? `${section.order}.${file.order} ` : '';
        toc += `   ${fileNumber}[${file.title}](#${this.slugify(file.title)})\n`;
      }
      toc += '\n';
    }
    
    return toc + '---\n\n';
  }

  /**
   * Generate content for a section
   */
  private generateSectionContent(section: DocSection): string {
    const sectionNumber = this.shouldIncludeSectionNumbers() ? `${section.order}. ` : '';
    let content = `## ${sectionNumber}${section.title}\n\n${section.description}\n\n`;
    
    const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);
    
    for (const file of sortedFiles) {
      content += this.generateFileContent(file, section);
    }
    
    return content;
  }

  /**
   * Generate content for a file
   */
  private generateFileContent(file: DocFile, section: DocSection): string {
    const filePath = path.join(this.rootDir, file.file);
    
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  File not found: ${filePath}`);
      return `### ${file.title}\n\n*File not found: ${file.file}*\n\n`;
    }
    
    let fileContent = fs.readFileSync(filePath, 'utf-8');
    
    // Remove the first H1 heading if it exists to avoid duplicate titles
    fileContent = fileContent.replace(/^#\s+.*$/m, '');
    
    // Adjust heading levels to fit within the document structure
    fileContent = this.adjustHeadingLevels(fileContent);
    
    // Add source link if enabled
    const sourceLink = this.shouldIncludeSourceLinks()
      ? `\n\n*Source: [${file.file}](${file.file})*\n`
      : '';
    
    const fileNumber = this.shouldIncludeFileNumbers() ? `${section.order}.${file.order} ` : '';
    
    return `### ${fileNumber}${file.title}\n\n${fileContent.trim()}${sourceLink}\n\n---\n\n`;
  }

  /**
   * Adjust heading levels in content to fit document structure
   */
  private adjustHeadingLevels(content: string): string {
    // Convert H1 -> H4, H2 -> H5, H3 -> H6, etc.
    // This ensures section content doesn't conflict with main document structure
    return content
      .replace(/^######\s+/gm, '###### ')  // H6 stays H6
      .replace(/^#####\s+/gm, '###### ')   // H5 -> H6
      .replace(/^####\s+/gm, '##### ')     // H4 -> H5
      .replace(/^###\s+/gm, '#### ')       // H3 -> H4
      .replace(/^##\s+/gm, '#### ')        // H2 -> H4 (skip H3 for file titles)
      .replace(/^#\s+/gm, '#### ');        // H1 -> H4
  }

  /**
   * Generate document footer
   */
  private generateFooter(): string {
    return `---

*Generated by Documentation Merger v${this.config.version}*
*Generated on: ${new Date().toISOString()}*
`;
  }

  /**
   * Convert text to URL-friendly slug
   */
  private slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Helper methods for checking options
   */
  private shouldIncludeToc(): boolean {
    return this.options.includeTableOfContents ?? this.config.navigation.generateToc;
  }

  private shouldIncludeSectionNumbers(): boolean {
    return this.options.includeSectionNumbers ?? this.config.navigation.showSectionNumbers;
  }

  private shouldIncludeFileNumbers(): boolean {
    return this.options.includeFileNumbers ?? this.config.navigation.showFileNumbers;
  }

  private shouldIncludeSourceLinks(): boolean {
    return this.options.includeSourceLinks ?? this.config.output.includeSourceLinks;
  }

  private shouldIncludeLastModified(): boolean {
    return this.options.includeLastModified ?? this.config.output.includeLastModified;
  }

  /**
   * Get statistics about the merged document
   */
  getStats(): { sections: number; files: number; totalSize: number } {
    let totalFiles = 0;
    let totalSize = 0;

    for (const section of this.config.structure.sections) {
      totalFiles += section.files.length;
      
      for (const file of section.files) {
        const filePath = path.join(this.rootDir, file.file);
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          totalSize += stats.size;
        }
      }
    }

    return {
      sections: this.config.structure.sections.length,
      files: totalFiles,
      totalSize
    };
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(`
📄 Documentation Merger

Usage: tsx merge-docs.ts <config-file> [options]

Options:
  --output, -o <file>     Output file path (default: from config)
  --no-toc               Disable table of contents
  --no-section-numbers   Disable section numbering
  --no-file-numbers      Disable file numbering
  --no-source-links      Disable source file links
  --no-last-modified     Disable last modified timestamp
  --help, -h             Show this help message

Examples:
  tsx merge-docs.ts docs-config.json
  tsx merge-docs.ts docs-config.json --output MERGED_DOCS.md
  tsx merge-docs.ts docs-config.json --no-toc --no-section-numbers
`);
    process.exit(0);
  }

  const configPath = args[0];
  
  if (!fs.existsSync(configPath)) {
    console.error(`❌ Configuration file not found: ${configPath}`);
    process.exit(1);
  }

  // Parse command line options
  const options: MergeOptions = {
    configPath,
    outputPath: undefined,
    includeTableOfContents: !args.includes('--no-toc'),
    includeSectionNumbers: !args.includes('--no-section-numbers'),
    includeFileNumbers: !args.includes('--no-file-numbers'),
    includeSourceLinks: !args.includes('--no-source-links'),
    includeLastModified: !args.includes('--no-last-modified')
  };

  // Check for output file option
  const outputIndex = args.findIndex(arg => arg === '--output' || arg === '-o');
  if (outputIndex !== -1 && outputIndex + 1 < args.length) {
    options.outputPath = args[outputIndex + 1];
  } else {
    // Use default from config
    const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    options.outputPath = path.join(path.dirname(configPath), config.output.mergedFile);
  }

  try {
    const merger = new MarkdownMerger(options);
    const stats = merger.getStats();
    
    console.log(`📊 Documentation stats:`);
    console.log(`   Sections: ${stats.sections}`);
    console.log(`   Files: ${stats.files}`);
    console.log(`   Total size: ${(stats.totalSize / 1024).toFixed(1)} KB`);
    console.log('');
    
    await merger.merge();
    
    console.log('✅ Documentation merge completed successfully!');
  } catch (error) {
    console.error('❌ Error merging documentation:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { MarkdownMerger, MergeOptions };
