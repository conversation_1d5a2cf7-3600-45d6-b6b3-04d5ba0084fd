#!/usr/bin/env tsx

import * as fs from 'fs'
import * as path from 'path'

// Simple documentation generator that combines all functionality
class SimpleDocumentationGenerator {
    private config: any
    private rootDir: string

    constructor(configPath: string) {
        this.rootDir = path.dirname(configPath)

        try {
            this.config = JSON.parse(fs.readFileSync(configPath, 'utf-8'))
            this.validateConfig()
        } catch (error) {
            throw new Error(
                `Failed to load configuration: ${error instanceof Error ? error.message : error}`
            )
        }
    }

    private validateConfig(): void {
        const required = ['title', 'description', 'version', 'structure']
        for (const field of required) {
            if (!this.config[field]) {
                throw new Error(
                    `Missing required configuration field: ${field}`
                )
            }
        }

        if (
            !this.config.structure.sections ||
            !Array.isArray(this.config.structure.sections)
        ) {
            throw new Error('Configuration must have structure.sections array')
        }

        if (this.config.structure.sections.length === 0) {
            throw new Error('Configuration must have at least one section')
        }
    }

    /**
     * Generate merged markdown documentation
     */
    async generateMerged(outputPath?: string): Promise<void> {
        console.log('📄 Generating merged documentation...')

        const output =
            outputPath || path.join(this.rootDir, this.config.output.mergedFile)
        let content = this.generateHeader()
        content += this.generateTableOfContents()

        // Sort sections by order
        const sortedSections = [...this.config.structure.sections].sort(
            (a: any, b: any) => a.order - b.order
        )

        for (const section of sortedSections) {
            content += this.generateSectionContent(section)
        }

        content += this.generateFooter()

        fs.writeFileSync(output, content, 'utf-8')
        console.log(`✅ Merged documentation saved to: ${output}`)
    }

    /**
     * Generate documentation site
     */
    async generateSite(outputDir?: string): Promise<void> {
        console.log('🌐 Generating documentation site...')

        const siteDir =
            outputDir || path.join(this.rootDir, this.config.output.siteDir)

        // Create output directory
        if (!fs.existsSync(siteDir)) {
            fs.mkdirSync(siteDir, { recursive: true })
        }

        // Generate index page
        this.generateIndexPage(siteDir)

        // Generate section pages
        this.generateSectionPages(siteDir)

        // Generate assets
        this.generateAssets(siteDir)

        console.log(`✅ Documentation site generated in: ${siteDir}`)
    }

    private generateHeader(): string {
        return `# ${this.config.title}

${this.config.description}

**Version:** ${this.config.version}

*Last updated: ${new Date().toISOString().split('T')[0]}*

---

`
    }

    private generateTableOfContents(): string {
        let toc = '## Table of Contents\n\n'

        const sortedSections = [...this.config.structure.sections].sort(
            (a: any, b: any) => a.order - b.order
        )

        for (const section of sortedSections) {
            toc += `${section.order}. [${section.title}](#${this.slugify(section.title)})\n`

            const sortedFiles = [...section.files].sort(
                (a: any, b: any) => a.order - b.order
            )
            for (const file of sortedFiles) {
                toc += `   ${section.order}.${file.order} [${file.title}](#${this.slugify(file.title)})\n`
            }
            toc += '\n'
        }

        return toc + '---\n\n'
    }

    private generateSectionContent(section: any): string {
        let content = `## ${section.order}. ${section.title}\n\n${section.description}\n\n`

        const sortedFiles = [...section.files].sort(
            (a: any, b: any) => a.order - b.order
        )

        for (const file of sortedFiles) {
            content += this.generateFileContent(file, section)
        }

        return content
    }

    private generateFileContent(file: any, section: any): string {
        const filePath = path.join(this.rootDir, file.file)

        if (!fs.existsSync(filePath)) {
            console.warn(`⚠️  File not found: ${filePath}`)
            return `### ${section.order}.${file.order} ${file.title}\n\n*File not found: ${file.file}*\n\n---\n\n`
        }

        try {
            let fileContent = fs.readFileSync(filePath, 'utf-8')

            // Validate file is not empty
            if (fileContent.trim().length === 0) {
                console.warn(`⚠️  Empty file: ${filePath}`)
                return `### ${section.order}.${file.order} ${file.title}\n\n*File is empty: ${file.file}*\n\n---\n\n`
            }

            // Remove the first H1 heading if it exists
            fileContent = fileContent.replace(/^#\s+.*$/m, '')

            // Adjust heading levels
            fileContent = fileContent
                .replace(/^######\s+/gm, '###### ')
                .replace(/^#####\s+/gm, '###### ')
                .replace(/^####\s+/gm, '##### ')
                .replace(/^###\s+/gm, '#### ')
                .replace(/^##\s+/gm, '#### ')
                .replace(/^#\s+/gm, '#### ')

            const sourceLink = `\n\n*Source: [${file.file}](${file.file})*\n`

            return `### ${section.order}.${file.order} ${file.title}\n\n${fileContent.trim()}${sourceLink}\n\n---\n\n`
        } catch (error) {
            console.error(`❌ Error reading file ${filePath}:`, error)
            return `### ${section.order}.${file.order} ${file.title}\n\n*Error reading file: ${file.file}*\n\n---\n\n`
        }
    }

    private generateFooter(): string {
        return `---

*Generated by Documentation Generator v${this.config.version}*
*Generated on: ${new Date().toISOString()}*
`
    }

    private generateIndexPage(siteDir: string): void {
        const indexContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.config.title}</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>${this.config.title}</h1>
            <p>${this.config.description}</p>
        </header>

        <main>
            <div class="welcome">
                <h2>Welcome to the Documentation</h2>
                <p>Select a section below to get started.</p>

                <div class="sections">
                    ${this.generateSectionsHTML()}
                </div>
            </div>
        </main>
    </div>
</body>
</html>`

        fs.writeFileSync(path.join(siteDir, 'index.html'), indexContent)
    }

    private generateSectionsHTML(): string {
        let html = ''
        const sortedSections = [...this.config.structure.sections].sort(
            (a: any, b: any) => a.order - b.order
        )

        for (const section of sortedSections) {
            const firstFile = section.files.sort(
                (a: any, b: any) => a.order - b.order
            )[0]
            const href = firstFile ? `${section.id}/${firstFile.id}.html` : '#'

            html += `
        <div class="section-card">
          <h3><a href="${href}">${section.title}</a></h3>
          <p>${section.description}</p>
          <div class="file-count">${section.files.length} pages</div>
        </div>
      `
        }

        return html
    }

    private generateSectionPages(siteDir: string): void {
        const sortedSections = [...this.config.structure.sections].sort(
            (a: any, b: any) => a.order - b.order
        )

        for (const section of sortedSections) {
            const sectionDir = path.join(siteDir, section.id)
            if (!fs.existsSync(sectionDir)) {
                fs.mkdirSync(sectionDir, { recursive: true })
            }

            const sortedFiles = [...section.files].sort(
                (a: any, b: any) => a.order - b.order
            )

            for (const file of sortedFiles) {
                this.generateFilePage(file, section, siteDir)
            }
        }
    }

    private generateFilePage(file: any, section: any, siteDir: string): void {
        const filePath = path.join(this.rootDir, file.file)

        if (!fs.existsSync(filePath)) {
            console.warn(`⚠️  File not found: ${filePath}`)
            return
        }

        try {
            let markdownContent = fs.readFileSync(filePath, 'utf-8')

            if (markdownContent.trim().length === 0) {
                console.warn(`⚠️  Empty file: ${filePath}`)
                markdownContent = `*This file is empty.*`
            }

            const htmlContent = this.markdownToHtml(markdownContent)

            const pageContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${file.title} - ${this.config.title}</title>
    <link rel="stylesheet" href="../assets/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><a href="../index.html">${this.config.title}</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> › ${section.title} › ${file.title}
            </nav>
        </header>

        <main>
            <article>
                <h1>${file.title}</h1>
                ${htmlContent}
            </article>
        </main>
    </div>
</body>
</html>`

            const sectionDir = path.join(siteDir, section.id)
            const outputPath = path.join(sectionDir, `${file.id}.html`)

            fs.writeFileSync(outputPath, pageContent)
        } catch (error) {
            console.error(`❌ Error generating page for ${file.title}:`, error)
        }
    }

    private generateAssets(siteDir: string): void {
        const assetsDir = path.join(siteDir, 'assets')
        if (!fs.existsSync(assetsDir)) {
            fs.mkdirSync(assetsDir, { recursive: true })
        }

        const css = `
/* Simple Documentation Styles */
* { margin: 0; padding: 0; box-sizing: border-box; }

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

header {
  background: #2c3e50;
  color: white;
  padding: 2rem;
  text-align: center;
}

header h1 { margin: 0; font-size: 2rem; }
header h1 a { color: white; text-decoration: none; }
header p { margin: 1rem 0 0 0; opacity: 0.9; }

.breadcrumb {
  margin-top: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.breadcrumb a { color: #ecf0f1; text-decoration: none; }

main { padding: 2rem; }

.welcome { text-align: center; max-width: 800px; margin: 0 auto; }
.welcome h2 { color: #2c3e50; margin-bottom: 1rem; }

.sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.section-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: left;
}

.section-card h3 { margin: 0 0 0.5rem 0; color: #2c3e50; }
.section-card h3 a { color: #2c3e50; text-decoration: none; }
.section-card h3 a:hover { color: #3498db; }
.file-count { font-size: 0.8rem; color: #7f8c8d; margin-top: 0.5rem; }

article { max-width: 800px; }
h1, h2, h3, h4, h5, h6 { margin-top: 2rem; margin-bottom: 1rem; color: #2c3e50; }
h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; border-bottom: 2px solid #ecf0f1; padding-bottom: 0.5rem; }
h3 { font-size: 1.5rem; }
p { margin-bottom: 1rem; }

code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
}

pre {
  background: #2c3e50;
  color: #ecf0f1;
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
  margin: 1rem 0;
}

pre code { background: none; padding: 0; color: inherit; }

@media (max-width: 768px) {
  .sections { grid-template-columns: 1fr; }
  header { padding: 1rem; }
  main { padding: 1rem; }
}
`

        fs.writeFileSync(path.join(assetsDir, 'style.css'), css)
    }

    private markdownToHtml(markdown: string): string {
        return markdown
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/gim, '<em>$1</em>')
            .replace(
                /```(\w+)?\n([\s\S]*?)```/gim,
                '<pre><code>$2</code></pre>'
            )
            .replace(/`([^`]+)`/gim, '<code>$1</code>')
            .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
            .replace(/\n\n/gim, '</p><p>')
            .replace(/\n/gim, '<br>')
    }

    private slugify(text: string): string {
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '')
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2)

    if (args.length === 0 || args.includes('--help')) {
        console.log(`
📚 Simple Documentation Generator

Usage: tsx docs.ts [command] [config-file] [options]

Commands:
  merge     Generate merged markdown document
  site      Generate documentation site
  both      Generate both (default)

Options:
  --output <file>    Output file for merged docs
  --output-dir <dir> Output directory for site
  --help             Show this help

Examples:
  tsx docs.ts both
  tsx docs.ts merge --output DOCS.md
  tsx docs.ts site --output-dir ./my-docs
`)
        return
    }

    const command = args[0] && !args[0].startsWith('-') ? args[0] : 'both'
    const configPath =
        args.find((arg) => !arg.startsWith('-') && arg !== command) ||
        'docs-config.json'

    if (!fs.existsSync(configPath)) {
        console.error(`❌ Configuration file not found: ${configPath}`)
        process.exit(1)
    }

    try {
        const generator = new SimpleDocumentationGenerator(configPath)

        const outputIndex = args.findIndex((arg) => arg === '--output')
        const outputDirIndex = args.findIndex((arg) => arg === '--output-dir')

        const outputPath =
            outputIndex !== -1 ? args[outputIndex + 1] : undefined
        const outputDir =
            outputDirIndex !== -1 ? args[outputDirIndex + 1] : undefined

        if (command === 'merge' || command === 'both') {
            await generator.generateMerged(outputPath)
        }

        if (command === 'site' || command === 'both') {
            await generator.generateSite(outputDir)
        }

        console.log('🎉 Documentation generation completed!')
    } catch (error) {
        console.error('❌ Error:', error)
        process.exit(1)
    }
}

main()
