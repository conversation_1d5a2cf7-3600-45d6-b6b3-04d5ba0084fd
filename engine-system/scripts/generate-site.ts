#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

interface SiteOptions {
  configPath: string;
  outputDir?: string;
  theme?: string;
  includeSearch?: boolean;
  includeSourceLinks?: boolean;
  includeLastModified?: boolean;
}

interface DocFile {
  id: string;
  title: string;
  file: string;
  description: string;
  order: number;
}

interface DocSection {
  id: string;
  title: string;
  description: string;
  order: number;
  files: DocFile[];
}

interface DocConfig {
  title: string;
  description: string;
  version: string;
  baseUrl: string;
  structure: {
    sections: DocSection[];
  };
  navigation: {
    showSectionNumbers: boolean;
    showFileNumbers: boolean;
    generateToc: boolean;
    maxTocDepth: number;
  };
  output: {
    siteDir: string;
    includeSourceLinks: boolean;
    includeLastModified: boolean;
  };
  styling: {
    theme: string;
    codeHighlighting: boolean;
    responsiveDesign: boolean;
  };
}

class SiteGenerator {
  private config: DocConfig;
  private rootDir: string;
  private options: SiteOptions;
  private outputDir: string;

  constructor(options: SiteOptions) {
    this.options = options;
    this.rootDir = path.dirname(options.configPath);
    this.config = JSON.parse(fs.readFileSync(options.configPath, 'utf-8'));
    this.outputDir = options.outputDir || path.join(this.rootDir, this.config.output.siteDir);
  }

  /**
   * Generate the complete documentation site
   */
  async generate(): Promise<void> {
    console.log('🌐 Generating documentation site...');

    // Create output directory
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    // Generate pages
    await this.generateIndexPage();
    await this.generateSectionPages();

    // Generate assets
    await this.generateAssets();

    // Generate navigation and search
    await this.generateNavigation();

    if (this.shouldIncludeSearch()) {
      await this.generateSearchIndex();
    }

    console.log(`✅ Documentation site generated in: ${this.outputDir}`);
    console.log(`🌍 Open ${path.join(this.outputDir, 'index.html')} in your browser`);
  }

  /**
   * Generate the main index page
   */
  private async generateIndexPage(): Promise<void> {
    const indexContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.config.title}</title>
    <meta name="description" content="${this.config.description}">
    <link rel="stylesheet" href="assets/style.css">
    ${this.config.styling.codeHighlighting ? '<link rel="stylesheet" href="assets/highlight.css">' : ''}
</head>
<body>
    <div class="container">
        <header class="site-header">
            <h1>${this.config.title}</h1>
            <p class="site-description">${this.config.description}</p>
            <div class="version-badge">v${this.config.version}</div>
        </header>

        <div class="content-wrapper">
            <nav class="sidebar">
                <div class="nav-header">
                    <h3>Documentation</h3>
                    ${this.shouldIncludeSearch() ? '<input type="text" id="search" placeholder="Search documentation...">' : ''}
                </div>
                ${this.generateNavigationHTML()}
            </nav>

            <main class="content">
                <div class="welcome-section">
                    <h2>Welcome to the Documentation</h2>
                    <p>This documentation provides comprehensive information about the ${this.config.title}.</p>

                    <div class="quick-links">
                        <h3>Quick Start</h3>
                        <div class="link-grid">
                            ${this.generateQuickLinks()}
                        </div>
                    </div>

                    <div class="sections-overview">
                        <h3>Documentation Sections</h3>
                        ${this.generateSectionsOverview()}
                    </div>
                </div>
            </main>
        </div>

        <footer class="site-footer">
            <p>Generated by Documentation Site Generator v${this.config.version}</p>
            <p>Last updated: ${new Date().toISOString().split('T')[0]}</p>
        </footer>
    </div>

    <script src="assets/script.js"></script>
</body>
</html>`;

    fs.writeFileSync(path.join(this.outputDir, 'index.html'), indexContent);
  }

  /**
   * Generate individual section pages
   */
  private async generateSectionPages(): Promise<void> {
    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      const sectionDir = path.join(this.outputDir, section.id);
      if (!fs.existsSync(sectionDir)) {
        fs.mkdirSync(sectionDir, { recursive: true });
      }

      const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);

      for (const file of sortedFiles) {
        await this.generateFilePage(file, section);
      }
    }
  }

  /**
   * Generate a page for a specific file
   */
  private async generateFilePage(file: DocFile, section: DocSection): Promise<void> {
    const filePath = path.join(this.rootDir, file.file);

    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  File not found: ${filePath}`);
      return;
    }

    let markdownContent = fs.readFileSync(filePath, 'utf-8');
    const htmlContent = this.markdownToHtml(markdownContent);

    const pageContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${file.title} - ${this.config.title}</title>
    <meta name="description" content="${file.description}">
    <link rel="stylesheet" href="../assets/style.css">
    ${this.config.styling.codeHighlighting ? '<link rel="stylesheet" href="../assets/highlight.css">' : ''}
</head>
<body>
    <div class="container">
        <header class="site-header">
            <h1><a href="../index.html">${this.config.title}</a></h1>
            <nav class="breadcrumb">
                <a href="../index.html">Home</a> ›
                <span>${section.title}</span> ›
                <span>${file.title}</span>
            </nav>
        </header>

        <div class="content-wrapper">
            <nav class="sidebar">
                <div class="nav-header">
                    <h3>Navigation</h3>
                    ${this.shouldIncludeSearch() ? '<input type="text" id="search" placeholder="Search...">' : ''}
                </div>
                ${this.generateNavigationHTML(section.id, file.id)}
            </nav>

            <main class="content">
                <article class="documentation-content">
                    <header class="content-header">
                        <h1>${file.title}</h1>
                        <p class="content-description">${file.description}</p>
                    </header>

                    <div class="content-body">
                        ${htmlContent}
                    </div>

                    <footer class="content-footer">
                        ${this.generateContentFooter(file, filePath)}
                    </footer>
                </article>

                <nav class="page-navigation">
                    ${this.generatePageNavigation(file, section)}
                </nav>
            </main>
        </div>

        <footer class="site-footer">
            <p>Generated by Documentation Site Generator v${this.config.version}</p>
        </footer>
    </div>

    <script src="../assets/script.js"></script>
</body>
</html>`;

    const sectionDir = path.join(this.outputDir, section.id);
    fs.writeFileSync(path.join(sectionDir, `${file.id}.html`), pageContent);
  }

  /**
   * Generate navigation HTML
   */
  private generateNavigationHTML(currentSectionId?: string, currentFileId?: string): string {
    let nav = '<ul class="nav-sections">';

    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      const isCurrentSection = section.id === currentSectionId;
      const sectionClass = isCurrentSection ? 'nav-section current-section' : 'nav-section';

      nav += `<li class="${sectionClass}">
        <div class="section-header">
          <span class="section-title">${section.title}</span>
          <span class="section-toggle">▼</span>
        </div>
        <ul class="nav-files ${isCurrentSection ? 'expanded' : ''}">`;

      const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);
      for (const file of sortedFiles) {
        const isCurrentFile = file.id === currentFileId && isCurrentSection;
        const fileClass = isCurrentFile ? 'current-file' : '';
        const href = currentSectionId ? `../${section.id}/${file.id}.html` : `${section.id}/${file.id}.html`;

        nav += `<li class="${fileClass}">
          <a href="${href}">${file.title}</a>
        </li>`;
      }

      nav += '</ul></li>';
    }

    nav += '</ul>';
    return nav;
  }

  /**
   * Generate quick links for the index page
   */
  private generateQuickLinks(): string {
    const firstSection = this.config.structure.sections.sort((a, b) => a.order - b.order)[0];
    if (!firstSection || firstSection.files.length === 0) {
      return '<p>No quick links available</p>';
    }

    const firstFile = firstSection.files.sort((a, b) => a.order - b.order)[0];

    return `
      <div class="quick-link-card">
        <h4>Getting Started</h4>
        <p>Start with the ${firstFile.title}</p>
        <a href="${firstSection.id}/${firstFile.id}.html" class="btn btn-primary">Get Started →</a>
      </div>
    `;
  }

  /**
   * Generate sections overview for the index page
   */
  private generateSectionsOverview(): string {
    let overview = '<div class="sections-grid">';

    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      const firstFile = section.files.sort((a, b) => a.order - b.order)[0];
      const href = firstFile ? `${section.id}/${firstFile.id}.html` : '#';

      overview += `
        <div class="section-card">
          <h4><a href="${href}">${section.title}</a></h4>
          <p>${section.description}</p>
          <div class="file-count">${section.files.length} ${section.files.length === 1 ? 'page' : 'pages'}</div>
        </div>
      `;
    }

    overview += '</div>';
    return overview;
  }

  /**
   * Generate content footer with metadata
   */
  private generateContentFooter(file: DocFile, filePath: string): string {
    let footer = '<div class="content-meta">';

    if (this.shouldIncludeSourceLinks()) {
      footer += `<p><a href="../../${file.file}" class="source-link">📄 View source</a></p>`;
    }

    if (this.shouldIncludeLastModified()) {
      footer += `<p class="last-modified">Last modified: ${this.getFileModifiedDate(filePath)}</p>`;
    }

    footer += '</div>';
    return footer;
  }

  /**
   * Generate page navigation (previous/next)
   */
  private generatePageNavigation(currentFile: DocFile, currentSection: DocSection): string {
    const allFiles = this.getAllFilesInOrder();
    const currentIndex = allFiles.findIndex(f => f.file.id === currentFile.id && f.section.id === currentSection.id);

    if (currentIndex === -1) return '';

    const prevFile = currentIndex > 0 ? allFiles[currentIndex - 1] : null;
    const nextFile = currentIndex < allFiles.length - 1 ? allFiles[currentIndex + 1] : null;

    let nav = '<div class="page-nav">';

    if (prevFile) {
      const href = prevFile.section.id === currentSection.id
        ? `${prevFile.file.id}.html`
        : `../${prevFile.section.id}/${prevFile.file.id}.html`;
      nav += `<a href="${href}" class="nav-prev">← ${prevFile.file.title}</a>`;
    }

    if (nextFile) {
      const href = nextFile.section.id === currentSection.id
        ? `${nextFile.file.id}.html`
        : `../${nextFile.section.id}/${nextFile.file.id}.html`;
      nav += `<a href="${href}" class="nav-next">${nextFile.file.title} →</a>`;
    }

    nav += '</div>';
    return nav;
  }

  /**
   * Get all files in order across all sections
   */
  private getAllFilesInOrder(): Array<{section: DocSection, file: DocFile}> {
    const allFiles: Array<{section: DocSection, file: DocFile}> = [];

    const sortedSections = [...this.config.structure.sections].sort((a, b) => a.order - b.order);

    for (const section of sortedSections) {
      const sortedFiles = [...section.files].sort((a, b) => a.order - b.order);
      for (const file of sortedFiles) {
        allFiles.push({ section, file });
      }
    }

    return allFiles;
  }

  /**
   * Generate CSS and JS assets
   */
  private async generateAssets(): Promise<void> {
    const assetsDir = path.join(this.outputDir, 'assets');
    if (!fs.existsSync(assetsDir)) {
      fs.mkdirSync(assetsDir, { recursive: true });
    }

    // Generate CSS
    const css = this.generateCSS();
    fs.writeFileSync(path.join(assetsDir, 'style.css'), css);

    // Generate highlight.js CSS for code highlighting
    if (this.config.styling.codeHighlighting) {
      const highlightCSS = this.generateHighlightCSS();
      fs.writeFileSync(path.join(assetsDir, 'highlight.css'), highlightCSS);
    }

    // Generate JavaScript
    const js = this.generateJavaScript();
    fs.writeFileSync(path.join(assetsDir, 'script.js'), js);
  }

  /**
   * Generate navigation files
   */
  private async generateNavigation(): Promise<void> {
    // Generate sitemap
    const sitemap = this.generateSitemap();
    fs.writeFileSync(path.join(this.outputDir, 'sitemap.xml'), sitemap);
  }

  /**
   * Generate search index for client-side search
   */
  private async generateSearchIndex(): Promise<void> {
    const searchIndex = [];

    for (const section of this.config.structure.sections) {
      for (const file of section.files) {
        const filePath = path.join(this.rootDir, file.file);

        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf-8');

          searchIndex.push({
            id: `${section.id}-${file.id}`,
            title: file.title,
            section: section.title,
            description: file.description,
            url: `${section.id}/${file.id}.html`,
            content: content.substring(0, 500), // First 500 chars for search
            keywords: this.extractKeywords(content)
          });
        }
      }
    }

    fs.writeFileSync(path.join(this.outputDir, 'search-index.json'), JSON.stringify(searchIndex, null, 2));
  }

  /**
   * Convert markdown to HTML (basic implementation)
   */
  private markdownToHtml(markdown: string): string {
    // Basic markdown to HTML converter
    let html = markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code class="language-$1">$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      // Lists
      .replace(/^\* (.+)$/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
      // Line breaks
      .replace(/\n\n/gim, '</p><p>')
      .replace(/\n/gim, '<br>');

    // Wrap in paragraphs
    if (!html.startsWith('<')) {
      html = '<p>' + html + '</p>';
    }

    return html;
  }

  /**
   * Extract keywords from content for search
   */
  private extractKeywords(content: string): string[] {
    const cleanContent = content
      .replace(/[#*`\[\]()]/g, ' ')
      .replace(/\s+/g, ' ')
      .toLowerCase();

    const words = cleanContent.split(' ')
      .filter(word => word.length > 3)
      .filter(word => !['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will', 'with'].includes(word));

    return [...new Set(words)].slice(0, 10);
  }

  /**
   * Get file modification date
   */
  private getFileModifiedDate(filePath: string): string {
    try {
      const stats = fs.statSync(filePath);
      return stats.mtime.toISOString().split('T')[0];
    } catch {
      return 'Unknown';
    }
  }

  /**
   * Generate sitemap XML
   */
  private generateSitemap(): string {
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${this.config.baseUrl}/</loc>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>`;

    for (const section of this.config.structure.sections) {
      for (const file of section.files) {
        sitemap += `
  <url>
    <loc>${this.config.baseUrl}/${section.id}/${file.id}.html</loc>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
      }
    }

    sitemap += '\n</urlset>';
    return sitemap;
  }

  /**
   * Generate CSS styles
   */
  private generateCSS(): string {
    return `/* Documentation Site Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.site-header {
  background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  position: relative;
}

.site-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.site-header h1 a {
  color: white;
  text-decoration: none;
}

.site-description {
  margin: 1rem 0 0 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.version-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255,255,255,0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.breadcrumb {
  margin-top: 1rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.breadcrumb a {
  color: #ecf0f1;
  text-decoration: none;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 200px);
}

.sidebar {
  width: 320px;
  background: #ecf0f1;
  border-right: 1px solid #bdc3c7;
  overflow-y: auto;
  position: sticky;
  top: 0;
  height: calc(100vh - 200px);
}

.nav-header {
  padding: 1.5rem;
  border-bottom: 1px solid #bdc3c7;
  background: #d5dbdb;
}

.nav-header h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
}

#search {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  font-size: 0.9rem;
}

.nav-sections {
  list-style: none;
  padding: 0;
}

.nav-section {
  border-bottom: 1px solid #bdc3c7;
}

.section-header {
  padding: 1rem 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ecf0f1;
  transition: background-color 0.2s;
}

.section-header:hover {
  background: #d5dbdb;
}

.section-title {
  font-weight: bold;
  color: #2c3e50;
}

.section-toggle {
  transition: transform 0.2s;
}

.nav-section.current-section .section-toggle {
  transform: rotate(180deg);
}

.nav-files {
  list-style: none;
  background: #f8f9fa;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.nav-files.expanded {
  max-height: 500px;
}

.nav-files li {
  border-bottom: 1px solid #ecf0f1;
}

.nav-files a {
  display: block;
  padding: 0.75rem 2rem;
  color: #7f8c8d;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.nav-files a:hover {
  color: #3498db;
  background: #ecf0f1;
}

.nav-files .current-file a {
  color: #3498db;
  background: #ecf0f1;
  font-weight: bold;
}

.content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.welcome-section {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.welcome-section h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.quick-links {
  margin: 3rem 0;
}

.link-grid {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.quick-link-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.2s;
  margin-top: 1rem;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.sections-overview {
  margin: 3rem 0;
  text-align: left;
}

.sections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.section-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-card h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.section-card h4 a {
  color: #2c3e50;
  text-decoration: none;
}

.section-card h4 a:hover {
  color: #3498db;
}

.file-count {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-top: 0.5rem;
}

.site-footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 1rem;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: static;
  }

  .nav-files.expanded {
    max-height: none;
  }
}`;
  }

  /**
   * Generate highlight.js CSS for code syntax highlighting
   */
  private generateHighlightCSS(): string {
    return `/* Code Highlighting Styles */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 1rem;
  background: #2c3e50;
  color: #ecf0f1;
  border-radius: 4px;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: #3498db;
}

.hljs-string,
.hljs-title,
.hljs-name,
.hljs-type,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  color: #e74c3c;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
  color: #95a5a6;
}

.hljs-number,
.hljs-regexp,
.hljs-literal {
  color: #f39c12;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}`;
  }

  /**
   * Generate JavaScript for interactive features
   */
  private generateJavaScript(): string {
    return `// Documentation Site JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Initialize navigation
  initializeNavigation();

  // Initialize search
  if (document.getElementById('search')) {
    initializeSearch();
  }

  // Add smooth scrolling
  addSmoothScrolling();

  // Add copy buttons to code blocks
  addCopyButtons();
});

function initializeNavigation() {
  // Toggle section expansion
  document.querySelectorAll('.section-header').forEach(header => {
    header.addEventListener('click', function() {
      const section = this.parentElement;
      const files = section.querySelector('.nav-files');
      const toggle = section.querySelector('.section-toggle');

      if (files.classList.contains('expanded')) {
        files.classList.remove('expanded');
        toggle.style.transform = 'rotate(0deg)';
      } else {
        files.classList.add('expanded');
        toggle.style.transform = 'rotate(180deg)';
      }
    });
  });

  // Highlight current page
  const currentPath = window.location.pathname;
  document.querySelectorAll('.nav-files a').forEach(link => {
    if (link.getAttribute('href') === currentPath.split('/').pop()) {
      link.parentElement.classList.add('current-file');

      // Expand parent section
      const section = link.closest('.nav-section');
      const files = section.querySelector('.nav-files');
      const toggle = section.querySelector('.section-toggle');
      files.classList.add('expanded');
      toggle.style.transform = 'rotate(180deg)';
    }
  });
}

function initializeSearch() {
  const searchInput = document.getElementById('search');

  searchInput.addEventListener('input', function() {
    const query = this.value.toLowerCase();

    document.querySelectorAll('.nav-section').forEach(section => {
      let sectionHasMatch = false;
      const files = section.querySelectorAll('.nav-files a');

      files.forEach(link => {
        const text = link.textContent.toLowerCase();
        const listItem = link.parentElement;

        if (text.includes(query) || query === '') {
          listItem.style.display = 'block';
          if (query !== '') sectionHasMatch = true;
        } else {
          listItem.style.display = 'none';
        }
      });

      // Show/hide section based on matches
      if (query === '' || sectionHasMatch) {
        section.style.display = 'block';
        if (sectionHasMatch && query !== '') {
          // Auto-expand sections with matches
          const navFiles = section.querySelector('.nav-files');
          const toggle = section.querySelector('.section-toggle');
          navFiles.classList.add('expanded');
          toggle.style.transform = 'rotate(180deg)';
        }
      } else {
        section.style.display = 'none';
      }
    });
  });
}

function addSmoothScrolling() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
}

function addCopyButtons() {
  document.querySelectorAll('pre code').forEach(block => {
    const button = document.createElement('button');
    button.textContent = 'Copy';
    button.className = 'copy-button';
    button.style.cssText = \`
      position: absolute;
      top: 8px;
      right: 8px;
      background: #3498db;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 11px;
      opacity: 0.8;
      transition: opacity 0.2s;
    \`;

    const pre = block.parentElement;
    pre.style.position = 'relative';
    pre.appendChild(button);

    button.addEventListener('mouseenter', () => {
      button.style.opacity = '1';
    });

    button.addEventListener('mouseleave', () => {
      button.style.opacity = '0.8';
    });

    button.addEventListener('click', () => {
      navigator.clipboard.writeText(block.textContent).then(() => {
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy';
        }, 2000);
      });
    });
  });
}`;
  }

  // Helper methods
  private shouldIncludeSearch(): boolean {
    return this.options.includeSearch ?? true;
  }

  private shouldIncludeSourceLinks(): boolean {
    return this.options.includeSourceLinks ?? this.config.output.includeSourceLinks;
  }

  private shouldIncludeLastModified(): boolean {
    return this.options.includeLastModified ?? this.config.output.includeLastModified;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    console.log(\`
🌐 Documentation Site Generator

Usage: tsx generate-site.ts <config-file> [options]

Options:
  --output, -o <dir>      Output directory (default: from config)
  --theme <theme>         Theme to use (default: from config)
  --no-search            Disable search functionality
  --no-source-links      Disable source file links
  --no-last-modified     Disable last modified timestamps
  --help, -h             Show this help message

Examples:
  tsx generate-site.ts docs-config.json
  tsx generate-site.ts docs-config.json --output ./my-docs-site
  tsx generate-site.ts docs-config.json --no-search --no-source-links
\`);
    process.exit(0);
  }

  const configPath = args[0];

  if (!fs.existsSync(configPath)) {
    console.error(\`❌ Configuration file not found: \${configPath}\`);
    process.exit(1);
  }

  // Parse command line options
  const options: SiteOptions = {
    configPath,
    includeSearch: !args.includes('--no-search'),
    includeSourceLinks: !args.includes('--no-source-links'),
    includeLastModified: !args.includes('--no-last-modified')
  };

  // Check for output directory option
  const outputIndex = args.findIndex(arg => arg === '--output' || arg === '-o');
  if (outputIndex !== -1 && outputIndex + 1 < args.length) {
    options.outputDir = args[outputIndex + 1];
  }

  // Check for theme option
  const themeIndex = args.findIndex(arg => arg === '--theme');
  if (themeIndex !== -1 && themeIndex + 1 < args.length) {
    options.theme = args[themeIndex + 1];
  }

  try {
    const generator = new SiteGenerator(options);
    await generator.generate();

    console.log('✅ Documentation site generation completed successfully!');
  } catch (error) {
    console.error('❌ Error generating documentation site:', error);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === \`file://\${process.argv[1]}\`) {
  main();
}

export { SiteGenerator, SiteOptions };
