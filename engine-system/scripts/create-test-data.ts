#!/usr/bin/env tsx

/**
 * Create test data for user deletion testing
 */

import prisma from '../src/db';

async function createTestData() {
  console.log('Creating test data for user deletion testing...');
  
  const testUserId = '123e4567-e89b-12d3-a456-426614174000';
  
  try {
    // Ensure we have required reference data
    let triggerType = await prisma.triggerType.findFirst();
    if (!triggerType) {
      triggerType = await prisma.triggerType.create({
        data: {
          name: 'test-type',
          description: 'Test trigger type'
        }
      });
    }
    
    let channelType = await prisma.channelType.findFirst();
    if (!channelType) {
      channelType = await prisma.channelType.create({
        data: {
          name: 'email',
          description: 'Email channel'
        }
      });
    }
    
    let contentType = await prisma.contentType.findFirst();
    if (!contentType) {
      contentType = await prisma.contentType.create({
        data: {
          name: 'html',
          description: 'HTML content'
        }
      });
    }

    // Create test trigger
    const trigger = await prisma.trigger.create({
      data: {
        name: `test-trigger-${Date.now()}`,
        description: 'Test trigger for user deletion',
        triggerTypeId: triggerType.id,
        createdBy: testUserId,
        updatedBy: testUserId,
      }
    });
    console.log(`✅ Created trigger: ${trigger.id}`);

    // Create test template
    const template = await prisma.template.create({
      data: {
        name: `test-template-${Date.now()}`,
        description: 'Test template for user deletion',
        body: 'Hello {{user.name}}, this is a test template.',
        channelId: channelType.id,
        contentTypeId: contentType.id,
        createdBy: testUserId,
        updatedBy: testUserId,
      }
    });
    console.log(`✅ Created template: ${template.id}`);

    // Create test audit log
    const auditLog = await prisma.templateAuditLog.create({
      data: {
        templateId: template.id,
        action: 'created',
        performedBy: testUserId,
      }
    });
    console.log(`✅ Created audit log: ${auditLog.id}`);

    console.log(`\n🎯 Test data created for user: ${testUserId}`);
    console.log('You can now test user deletion with this user ID.');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  createTestData();
}
