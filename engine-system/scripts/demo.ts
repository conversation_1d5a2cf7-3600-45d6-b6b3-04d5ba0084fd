#!/usr/bin/env tsx

import * as fs from 'fs';
import * as path from 'path';

/**
 * Demo script that shows the documentation system working end-to-end
 */
class DocumentationDemo {
  private rootDir: string;

  constructor() {
    this.rootDir = process.cwd();
  }

  async runDemo(): Promise<void> {
    console.log('🚀 Documentation System Demo');
    console.log('============================\n');

    try {
      // Step 1: Validate environment
      await this.validateEnvironment();

      // Step 2: Show configuration
      await this.showConfiguration();

      // Step 3: Generate documentation
      await this.generateDocumentation();

      // Step 4: Validate output
      await this.validateOutput();

      // Step 5: Show results
      await this.showResults();

      console.log('\n🎉 Demo completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Open docs-site/index.html in your browser');
      console.log('2. Review DOCUMENTATION.md for the merged content');
      console.log('3. Customize docs-config.json for your needs');
      console.log('4. Add your own documentation files');

    } catch (error) {
      console.error('\n❌ Demo failed:', error);
      process.exit(1);
    }
  }

  private async validateEnvironment(): Promise<void> {
    console.log('📋 Step 1: Validating environment...');

    // Check if we're in the right directory
    if (!fs.existsSync('docs-config.json')) {
      throw new Error('docs-config.json not found. Please run this from the engine-system directory.');
    }

    // Check if docs directory exists
    if (!fs.existsSync('docs')) {
      throw new Error('docs directory not found. Please ensure documentation files are present.');
    }

    // Check if scripts exist
    if (!fs.existsSync('scripts/docs.ts')) {
      throw new Error('Documentation generation script not found.');
    }

    console.log('✅ Environment validation passed');
  }

  private async showConfiguration(): Promise<void> {
    console.log('\n📄 Step 2: Showing configuration...');

    try {
      const config = JSON.parse(fs.readFileSync('docs-config.json', 'utf-8'));
      
      console.log(`📚 Title: ${config.title}`);
      console.log(`📝 Description: ${config.description}`);
      console.log(`🏷️  Version: ${config.version}`);
      console.log(`📁 Sections: ${config.structure.sections.length}`);

      let totalFiles = 0;
      for (const section of config.structure.sections) {
        totalFiles += section.files.length;
        console.log(`   ${section.order}. ${section.title} (${section.files.length} files)`);
      }

      console.log(`📄 Total files: ${totalFiles}`);
      console.log('✅ Configuration loaded successfully');

    } catch (error) {
      throw new Error(`Failed to read configuration: ${error}`);
    }
  }

  private async generateDocumentation(): Promise<void> {
    console.log('\n🔧 Step 3: Generating documentation...');

    // Import and use the documentation generator
    const { spawn } = require('child_process');

    return new Promise((resolve, reject) => {
      console.log('   📄 Generating merged documentation...');
      console.log('   🌐 Generating documentation site...');

      const process = spawn('npx', ['tsx', 'scripts/docs.ts', 'both'], {
        stdio: 'pipe',
        cwd: this.rootDir
      });

      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data: Buffer) => {
        output += data.toString();
      });

      process.stderr.on('data', (data: Buffer) => {
        errorOutput += data.toString();
      });

      process.on('close', (code: number) => {
        if (code === 0) {
          console.log('✅ Documentation generation completed');
          resolve();
        } else {
          console.error('Generation output:', output);
          console.error('Generation errors:', errorOutput);
          reject(new Error(`Documentation generation failed with code ${code}`));
        }
      });

      process.on('error', (error: Error) => {
        reject(new Error(`Failed to start generation process: ${error.message}`));
      });
    });
  }

  private async validateOutput(): Promise<void> {
    console.log('\n🔍 Step 4: Validating output...');

    // Check merged documentation
    if (!fs.existsSync('DOCUMENTATION.md')) {
      throw new Error('DOCUMENTATION.md was not generated');
    }

    const mergedStats = fs.statSync('DOCUMENTATION.md');
    console.log(`   📄 Merged doc: ${(mergedStats.size / 1024).toFixed(1)} KB`);

    if (mergedStats.size < 1000) {
      throw new Error('Generated merged documentation is too small (< 1KB)');
    }

    // Check documentation site
    if (!fs.existsSync('docs-site')) {
      throw new Error('docs-site directory was not generated');
    }

    if (!fs.existsSync('docs-site/index.html')) {
      throw new Error('docs-site/index.html was not generated');
    }

    if (!fs.existsSync('docs-site/assets/style.css')) {
      throw new Error('docs-site/assets/style.css was not generated');
    }

    // Count HTML files
    const htmlFiles = this.findFiles('docs-site', '.html');
    console.log(`   🌐 Site pages: ${htmlFiles.length} HTML files`);

    if (htmlFiles.length < 2) {
      throw new Error('Not enough HTML files generated');
    }

    // Check site size
    const siteSize = this.getDirectorySize('docs-site');
    console.log(`   📦 Site size: ${(siteSize / 1024).toFixed(1)} KB`);

    console.log('✅ Output validation passed');
  }

  private async showResults(): Promise<void> {
    console.log('\n📊 Step 5: Results summary...');

    // Merged documentation stats
    const mergedContent = fs.readFileSync('DOCUMENTATION.md', 'utf-8');
    const lines = mergedContent.split('\n').length;
    const words = mergedContent.split(/\s+/).length;
    const chars = mergedContent.length;

    console.log('\n📄 Merged Documentation:');
    console.log(`   Lines: ${lines.toLocaleString()}`);
    console.log(`   Words: ${words.toLocaleString()}`);
    console.log(`   Characters: ${chars.toLocaleString()}`);

    // Site stats
    const htmlFiles = this.findFiles('docs-site', '.html');
    const cssFiles = this.findFiles('docs-site', '.css');
    const totalFiles = this.findFiles('docs-site', '');

    console.log('\n🌐 Documentation Site:');
    console.log(`   HTML pages: ${htmlFiles.length}`);
    console.log(`   CSS files: ${cssFiles.length}`);
    console.log(`   Total files: ${totalFiles.length}`);

    // Show file structure
    console.log('\n📁 Generated structure:');
    console.log('   DOCUMENTATION.md');
    console.log('   docs-site/');
    console.log('   ├── index.html');
    console.log('   ├── assets/');
    console.log('   │   └── style.css');

    const config = JSON.parse(fs.readFileSync('docs-config.json', 'utf-8'));
    for (const section of config.structure.sections) {
      console.log(`   ├── ${section.id}/`);
      for (const file of section.files) {
        console.log(`   │   └── ${file.id}.html`);
      }
    }
  }

  private findFiles(dir: string, extension: string): string[] {
    const files: string[] = [];
    
    if (!fs.existsSync(dir)) {
      return files;
    }

    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.findFiles(fullPath, extension));
      } else if (extension === '' || item.endsWith(extension)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  private getDirectorySize(dir: string): number {
    let size = 0;
    
    if (!fs.existsSync(dir)) {
      return size;
    }

    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        size += this.getDirectorySize(fullPath);
      } else {
        size += stat.size;
      }
    }
    
    return size;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 Documentation System Demo

This demo script shows the documentation system working end-to-end.

Usage: tsx demo.ts

The demo will:
1. Validate the environment
2. Show the current configuration
3. Generate both merged docs and site
4. Validate the output
5. Show a summary of results

Make sure to run this from the engine-system directory.
`);
    return;
  }

  const demo = new DocumentationDemo();
  await demo.runDemo();
}

main().catch(error => {
  console.error('Demo failed:', error);
  process.exit(1);
});
