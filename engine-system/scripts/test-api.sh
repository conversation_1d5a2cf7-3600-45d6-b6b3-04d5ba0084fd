#!/bin/bash

# API Testing Script for User Deletion
# Make sure your server is running on localhost:3000

BASE_URL="http://localhost:3000"
USER_ID="123e4567-e89b-12d3-a456-426614174000"
ADMIN_ID="987fcdeb-51a2-43d1-b789-123456789abc"

echo "🚀 Testing User Deletion API"
echo "================================"

# Test 1: Check user constraints
echo "📋 Test 1: Checking user constraints..."
curl -s -X GET "$BASE_URL/users/$USER_ID/constraints" \
  -H "Content-Type: application/json" | jq '.'

echo -e "\n"

# Test 2: Delete user with reassignment
echo "🗑️ Test 2: Delete user with reassignment..."
curl -s -X DELETE "$BASE_URL/users" \
  -H "Content-Type: application/json" \
  -d "{
    \"userId\": \"$USER_ID\",
    \"adminUserId\": \"$ADMIN_ID\"
  }" | jq '.'

echo -e "\n"

# Test 3: Test validation error (invalid UUID)
echo "⚠️ Test 3: Testing validation error..."
curl -s -X DELETE "$BASE_URL/users" \
  -H "Content-Type: application/json" \
  -d "{
    \"userId\": \"invalid-uuid\",
    \"adminUserId\": \"$ADMIN_ID\"
  }" | jq '.'

echo -e "\n"

# Test 4: Test self-reassignment error
echo "🚫 Test 4: Testing self-reassignment prevention..."
curl -s -X DELETE "$BASE_URL/users" \
  -H "Content-Type: application/json" \
  -d "{
    \"userId\": \"$USER_ID\",
    \"adminUserId\": \"$USER_ID\"
  }" | jq '.'

echo -e "\n✨ API tests completed!"
