{"name": "engine-system", "version": "1.0.0", "description": "", "homepage": "https://bitbucket.org/thegrowthteam/notification-engine#readme", "bugs": {"url": "https://bitbucket.org/thegrowthteam/notification-engine/issues"}, "repository": {"type": "git", "url": "git+ssh://*****************/thegrowthteam/notification-engine.git"}, "license": "ISC", "author": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:user-deletion": "tsx scripts/test-user-deletion.ts", "build": "tsc -p tsconfig.json && copyfiles -u 1 src/**/*.hbs dist", "dev": "tsx  src/server.ts", "seed": "tsx prisma/seed.ts", "docs": "tsx scripts/docs.ts both", "docs:merge": "tsx scripts/docs.ts merge", "docs:site": "tsx scripts/docs.ts site", "docs:watch": "tsx scripts/docs.ts both", "docs:dev": "tsx scripts/docs.ts site", "docs:build": "tsx scripts/docs.ts both", "docs:clean": "rm -rf docs-site DOCUMENTATION.md", "docs:test": "tsx scripts/test-docs.ts", "docs:demo": "tsx scripts/demo.ts", "prebuild": "npm run docs:build"}, "dependencies": {"@aws-sdk/client-sqs": "^3.808.0", "@bull-board/api": "^6.9.2", "@bull-board/express": "^6.9.2", "@faker-js/faker": "^9.6.0", "@prisma/client": "^6.7.0", "@slack/web-api": "^7.9.1", "amqplib": "^0.10.5", "axios": "^1.8.4", "body-parser": "^2.2.0", "bullmq": "^5.44.4", "cors": "^2.8.5", "cron-validate": "^1.5.2", "dotenv": "^16.4.7", "express": "^4.21.2", "handlebars": "^4.7.8", "helmet": "^8.1.0", "ioredis": "^5.6.0", "nodemailer": "^6.10.0", "pino": "^9.6.0", "prisma": "^6.7.0", "zod": "^3.24.2"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^29.5.12", "@types/node": "^22.13.13", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "copyfiles": "^2.4.1", "jest": "^29.7.0", "prettier": "3.5.3", "supertest": "^7.0.0", "ts-jest": "^29.1.2", "tsx": "^4.19.3", "typescript": "^5.8.2"}}