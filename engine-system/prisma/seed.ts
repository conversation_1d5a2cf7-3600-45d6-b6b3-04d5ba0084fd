import prisma from '../src/db'
import { envConfig } from '../src/env.constant'

async function main() {
    const triggerTypes: { id: number; name: string; description: string }[] = [
        {
            id: 1,
            name: 'system',
            description: 'System defined trigger types',
        },
        {
            id: 2,
            name: 'custom',
            description: 'Custom defined trigger types',
        },
    ]

    const parameterTypes: { id: number; name: string; description: string }[] =
        [
            {
                id: 1,
                name: 'string',
                description: 'Text-based parameter',
            },
            {
                id: 2,
                name: 'number',
                description: 'Numeric parameter for calculations',
            },
            {
                id: 3,
                name: 'boolean',
                description: 'True/False flag',
            },
            {
                id: 4,
                name: 'date',
                description: 'Date parameter for time-based inputs',
            },
            {
                id: 5,
                name: 'object',
                description: 'Object parameter for complex data',
            },
            {
                id: 6,
                name: 'array',
                description: 'Array parameter of any type',
            },
        ]

    const contentTypes: { id: number; name: string; description: string }[] = [
        {
            id: 1,
            name: 'html',
            description: 'HTML formatted content for email templates',
        },
        {
            id: 2,
            name: 'text',
            description: 'Plain text content for SMS or email templates',
        },
        {
            id: 3,
            name: 'markdown',
            description: 'Markdown formatted content for rich text templates',
        },
        {
            id: 4,
            name: 'json',
            description: 'JSON formatted content for programmatic use',
        },
        {
            id: 5,
            name: 'block_kit',
            description: 'Block Kit content for Slack notifications',
        },
        {
            id: 6,
            name: 'hubspot_crm',
            description: 'HubSpot CRM formatted content for marketing emails',
        },
    ]

    const channelTypes: { id: number; name: string; description: string }[] = [
        {
            id: 1,
            name: 'email',
            description: 'Email notifications channel',
        },
        {
            id: 2,
            name: 'slack',
            description: 'Slack notifications channel',
        },
        {
            id: 3,
            name: 'sms',
            description: 'SMS notifications channel',
        },
        {
            id: 4,
            name: 'in_app',
            description: 'In-app notifications channel',
        },
        {
            id: 5,
            name: 'hubspot',
            description: 'HubSpot CRM notifications channel',
        },
        {
            id: 6,
            name: 'push',
            description: 'Push notifications channel',
        },
    ]

    // Seed trigger_types
    for (const triggerType of triggerTypes) {
        await prisma.triggerType.upsert({
            where: { name: triggerType.name },
            update: {},
            create: triggerType,
        })
    }

    // Seed parameter_types
    for (const parameterType of parameterTypes) {
        await prisma.parameterType.upsert({
            where: { name: parameterType.name },
            update: {},
            create: parameterType,
        })
    }

    // Seed content_types
    for (const contentType of contentTypes) {
        await prisma.contentType.upsert({
            where: { name: contentType.name },
            update: {},
            create: contentType,
        })
    }

    // Seed channel_types
    for (const channelType of channelTypes) {
        await prisma.channelType.upsert({
            where: { name: channelType.name },
            update: {},
            create: channelType,
        })
    }

    console.log('✅ Lookup tables seeded')

    const systemType = await prisma.triggerType.findUnique({
        where: { name: 'system' },
    })

    if (!systemType) {
        throw new Error('System trigger type not found')
    }

    const triggers: {
        name: string
        description: string
        fireOnce?: boolean
    }[] = [
        {
            name: 'credit-card',
            description: 'Trigger for credit card related events',
        },
        {
            name: 'snippet-share-docs',
            description: 'Trigger for snippet not installed - share docs',
        },
        {
            name: 'snippet-schedule-call',
            description: 'Trigger for snippet not installed - schedule call',
        },
        {
            name: 'session-share-docs',
            description: 'Trigger for session not tracking - share docs',
        },
        {
            name: 'session-schedule-call',

            description: 'Trigger for session not tracking - schedule call',
        },
        {
            name: 'revenue-share-docs',
            description: 'Trigger for revenue not tracking - share docs',
        },
        {
            name: 'revenue-schedule-call',
            description: 'Trigger for revenue not tracking - schedule call',
        },
        {
            name: 'weekly-summary-mid-trial',
            description: 'Weekly summary during trial',
            fireOnce: true,
        },
        {
            name: 'weekly-summary-end-trial',
            description: 'Weekly summary at end of trial',
            fireOnce: true,
        },
        {
            name: 'weekly-summary-recurring',
            description: 'Weekly summary for recurring users',
        },
        {
            name: 'login-case-study',
            description: 'Login inactive - share case study',
        },
        {
            name: 'login-book-demo',
            description: 'Login inactive - book demo',
        },
        {
            name: 'ip-blocking-not-implemented',
            description: 'IP blocking not implemented',
        },
        { name: 'team-not-invited', description: 'Teammates not invited' },
        { name: 'nps', description: 'Net Promoter Score (NPS) feedback' },
    ]

    for (const trigger of triggers) {
        const { name, description, fireOnce } = trigger

        await prisma.trigger.upsert({
            where: { name },
            update: {},
            create: {
                name,
                description,
                triggerTypeId: systemType.id,
                isConfigurable: false,
                createdBy: 'system',
                updatedBy: 'system',
                fireOnce: fireOnce ?? false, // fireOnce is optional, default to false if not provided
            },
        })
    }

    console.log('✅ Triggers seeded')
}

if (envConfig.runSeed) {
    main()
        .catch((e) => {
            console.error(e)
            process.exit(1)
        })
        .finally(async () => {
            await prisma.$disconnect()
        })
}
