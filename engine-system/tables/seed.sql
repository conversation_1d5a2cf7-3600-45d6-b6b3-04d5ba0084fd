INSERT INTO trigger_types (id, name, description)
VALUES (1, 'system', 'System defined trigger_types'),
       (2,'custom', 'custom defined trigger_types');

-- Parameter Types
INSERT INTO parameter_types (id,name, description)
VALUES
    (1,'string', 'Text-based parameter'),
       (2,'number', 'Numeric parameter for calculations'),
       (3,'boolean', 'True/False flag'),
    (4,'date', 'Date parameter for time-based inputs'),
       (5,'object', 'Object parameter for complex data'),
        (6,'array', 'Array parameter of any type');

-- Content Types
INSERT INTO content_types (id,name, description)
VALUES (1,'html', 'HTML formatted content for email templates'),
       (2,'text', 'Plain text content for SMS or email templates'),
       (3,'markdown', 'Markdown formatted content for rich text templates'),
       (4,'json', 'JSON formatted content for programmatic use'),
       (5,'block_kit', 'Block Kit content for Slack notifications'),
       (6,'hubspot_crm', 'HubSpot CRM formatted content for marketing emails');


-- Channel Types
INSERT INTO channel_types (id,name, description)
VALUES (1,'email', 'Email notifications channel'),
       (2,'slack', 'Slack notifications channel'),
       (3,'sms', 'SMS notifications channel'),
       (4,'in_app', 'In-app notifications channel'),
       (5,'hubspot', 'HubSpot CRM notifications channel'),
       (6,'push', 'Push notifications channel');



INSERT INTO triggers (name,
                      description,
                      trigger_type_id,
                      is_configurable,
                      created_by,
                      updated_by,
                      fire_once)
VALUES ('credit-card', 'Trigger for credit card related events',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system', false),
       ('snippet-share-docs', 'Trigger for snippet not installed - share docs',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system',
        false),
       ('snippet-schedule-call', 'Trigger for snippet not installed - schedule call',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system',
        'system', false),
       ('session-share-docs', 'Trigger for session not tracking - share docs',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system',
        false),
       ('session-schedule-call', 'Trigger for session not tracking - schedule call',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system',
        'system', false),
       ('revenue-share-docs', 'Trigger for revenue not tracking - share docs',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system',
        false),
       ('revenue-schedule-call', 'Trigger for revenue not tracking - schedule call',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system',
        'system', false),
       ('weekly-summary-mid-trial', 'Weekly summary during trial',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system', true),
       ('weekly-summary-end-trial', 'Weekly summary at end of trial',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system', true),
       ('weekly-summary-recurring', 'Weekly summary for recurring users',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system', false),
       ('login-case-study', 'Login inactive - share case study',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system', false),
       ('login-book-demo', 'Login inactive - book demo',
        (select id from trigger_types where trigger_types.name = 'system'),
        false, 'system', 'system', false),
       ('ip-blocking-not-implemented', 'IP blocking not implemented',
        (select id from trigger_types where trigger_types.name = 'system'), false, 'system', 'system', false),
       ('team-not-invited', 'Teammates not invited', (select id from trigger_types where trigger_types.name = 'system'),
        false, 'system', 'system', false),
       ('nps', 'Net Promoter Score (NPS) feedback', (select id from trigger_types where trigger_types.name = 'system'),
        false, 'system', 'system', false);
