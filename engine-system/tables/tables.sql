create table trigger_types
(
    id          tinyint unsigned not null auto_increment,
    name        varchar(64)      not null,
    description varchar(255),
    created_at  timestamp        not null default current_timestamp,
    updated_at  timestamp        not null default current_timestamp on update current_timestamp,

    primary key (id),
    unique key uq_trigger_types_name (name)
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

create table parameter_types
(
    id          tinyint unsigned not null auto_increment,
    name        varchar(64)      not null,
    description varchar(255),
    created_at  timestamp        not null default current_timestamp,
    updated_at  timestamp        not null default current_timestamp on update current_timestamp,

    primary key (id),
    unique key parameter_types_name (name)

) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

create table content_types
(
    id          tinyint unsigned not null auto_increment,
    name        varchar(64)      not null,
    description varchar(255),
    created_at  timestamp        not null default current_timestamp,
    updated_at  timestamp        not null default current_timestamp on update current_timestamp,

    primary key (id),
    unique key uq_content_types_name (name)

) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;


create table channel_types
(
    id          tinyint unsigned not null auto_increment,
    name        varchar(64),
    description varchar(255),
    created_at  timestamp        not null default current_timestamp,
    updated_at  timestamp        not null default current_timestamp on update current_timestamp,

    primary key (id),
    unique key uq_channel_types_name (name)
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

CREATE TABLE triggers
(
    id                 INT UNSIGNED     NOT NULL AUTO_INCREMENT,
    name               VARCHAR(255)     NOT NULL,
    description        TEXT,
    trigger_type_id    tinyint unsigned not null,
    metric_key         VARCHAR(255),
    is_configurable    BOOLEAN          NOT NULL DEFAULT FALSE,
    metadata           JSON,
    cooldown_seconds   INT,
    public_id          CHAR(36)         NOT NULL DEFAULT (UUID()),

    -- volume limits: Max number of times this type of trigger is allowed

    max_trigger_count  int unsigned,
    max_trigger_period enum ('minute', 'hour','day', 'week', 'month', 'year'),

    -- pacing: how far apart each firing of the same trigger must be
    min_interval_count int unsigned,
    min_interval_unit  enum ('minute', 'hour', 'day', 'week', 'month','year'),

    -- one-time flag
    fire_once          boolean          not null default false,

    delta_threshold    decimal(5, 2), -- range --100.00 to 100.00 (% change threshold)

    created_at         TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at         TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by         VARCHAR(128)     NOT NULL DEFAULT 'system',
    updated_by         VARCHAR(128)     NOT NULL DEFAULT 'system',
    deleted_at         timestamp,

    PRIMARY KEY (id),
    UNIQUE KEY uq_triggers_name (name),
    UNIQUE key uq_triggers_public_id (public_id),
    index idx_triggers_deleted_at (deleted_at),
    index idx_triggers_trigger_type (trigger_type_id),
    CONSTRAINT chk_trigger_name_slug CHECK (name REGEXP '^[a-z0-9]+(-[a-z0-9]+)*$'),
    constraint chk_delta_threshold_range check ( (delta_threshold is null) or
                                                 (delta_threshold between -100.00 and 100.00)),
    foreign key (trigger_type_id) references trigger_types (id) on delete restrict on update cascade
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

CREATE TABLE site_trigger_settings
(
    id                          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    id_site                     INT UNSIGNED NOT NULL,
    trigger_id                  INT UNSIGNED NOT NULL,
    is_enabled                  BOOLEAN      NOT NULL DEFAULT FALSE,

    condition_override          JSON,

    -- override pacing (per customer)
    min_interval_count_override int unsigned,
    min_interval_unit_override  enum ('minute', 'hour', 'day', 'week', 'month', 'year'),

    -- override volume cap (per customer)
    max_trigger_count_override  int unsigned,
    max_trigger_period_override enum ('minute', 'hour', 'day', 'week', 'month', 'year'),

    -- delta override
    delta_threshold_override    decimal(5, 2), -- range --100 to 100 (% change threshold)

    created_at                  timestamp    not null default current_timestamp,
    updated_at                  timestamp    not null default current_timestamp on update current_timestamp,
    deleted_at                  timestamp,

    PRIMARY KEY (id),
    UNIQUE KEY uq_site_trigger_settings_site_trigger (id_site, trigger_id),
    index idx_site_trigger_settings_deleted_at (deleted_at),
    FOREIGN KEY (trigger_id) REFERENCES triggers (id) ON UPDATE CASCADE ON DELETE restrict,
    constraint chk_delta_threshold_override_range check ( delta_threshold_override is null or
                                                          delta_threshold_override between -100.00 and 100.00)
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

CREATE TABLE site_notification_preferences
(
    id                 INT UNSIGNED     NOT NULL AUTO_INCREMENT,
    id_site            INT UNSIGNED     NOT NULL,
    channel_id         tinyint unsigned not null,
    trigger_id         int unsigned     null,
    is_enabled         BOOLEAN          NOT NULL DEFAULT FALSE,
    destination        JSON,
    created_at         TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at         TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at         timestamp,
    trigger_normalized int unsigned generated always as (ifnull(trigger_id, 0)),


    PRIMARY KEY (id),
    UNIQUE KEY uq_site_notification_preferences_site_channel_trigger (id_site, channel_id, trigger_normalized),
    index idx_site_notification_preferences_deleted_at (deleted_at),
    foreign key (channel_id) references channel_types (id) on update cascade on delete restrict
        #     constraint fk_site_notification_preferences_trigger foreign key (trigger_id) references triggers (id) on update cascade on delete restrict,

) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

alter table site_notification_preferences
    add foreign key (trigger_id) references triggers (id) on update cascade on delete restrict;
# alter table site_notification_preferences add constraint fk_site_notification_preferences_channel foreign key (channel_id) references channel_types (id) on update cascade on delete restrict


CREATE TABLE templates
(
    id              INT UNSIGNED                           NOT NULL AUTO_INCREMENT,
    trigger_id      INT UNSIGNED,
    name            VARCHAR(255)                           NOT NULL,
    description     TEXT,
    version         INT                                    NOT NULL DEFAULT 1,
    channel_id      tinyint unsigned                       not null,
    subject         VARCHAR(255),
    body            TEXT                                   NOT NULL,
    content_type_id tinyint unsigned                       not null,
    metadata        JSON,
    status          ENUM ('draft','archived', 'published') NOT NULL DEFAULT 'draft',
    public_id       CHAR(36)                               NOT NULL DEFAULT (UUID()),
    created_at      TIMESTAMP                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at      timestamp,
    created_by      VARCHAR(255),
    updated_by      VARCHAR(255),

    PRIMARY KEY (id),
    UNIQUE KEY uq_templates_trigger_channel (trigger_id, channel_id, version),
    UNIQUE KEY uq_templates_public_id (public_id),
    index idx_templates_deleted_at (deleted_at),
    index idx_templates_content_type_id (content_type_id),
    index idx_templates_channel_id (channel_id),
    FOREIGN KEY (trigger_id) REFERENCES triggers (id) on delete restrict on update cascade,
    foreign key (content_type_id) references content_types (id) on delete restrict on update cascade,
    foreign key (channel_id) references channel_types (id) on update cascade on delete restrict
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

CREATE TABLE parameters
(
    id            INT UNSIGNED     NOT NULL AUTO_INCREMENT,
    trigger_id    INT UNSIGNED,
    template_id   INT UNSIGNED,
    name          VARCHAR(255)     NOT NULL,
    description   TEXT,
    required      BOOLEAN          NOT NULL DEFAULT FALSE,
    param_type_id tinyint unsigned NOT NULL, -- eg. date,string,number,etc
    validations   JSON,
    default_value TEXT,
    example_value TEXT,
    created_at    TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY uq_parameter_name_per_scope (trigger_id, template_id, name),
    index idx_parameters_trigger_name (trigger_id, name),
    index idx_parameters_template_name (template_id, name),
    index idx_parameters_param_type_id (param_type_id),
    FOREIGN KEY (trigger_id) REFERENCES triggers (id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE ON UPDATE CASCADE,
    foreign key (param_type_id) references parameter_types (id) on delete restrict on update cascade
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

CREATE TABLE template_audit_logs
(
    id           BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    template_id  INT UNSIGNED    NOT NULL,
    action       ENUM ('created', 'rolled_back', 'published', 'archived', 'edited'),
    from_version INT,
    to_version   INT,
    performed_by VARCHAR(255),
    reason       TEXT,
    created_at   TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    INDEX idx_template_audit_logs_template_version (template_id, to_version),
    INDEX idx_template_audit_logs_performed_by (performed_by),
    FOREIGN KEY (template_id) REFERENCES templates (id) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

CREATE TABLE notification_logs
(
    id            BIGINT UNSIGNED                    NOT NULL AUTO_INCREMENT,
    trigger_id    INT UNSIGNED,
    template_id   INT UNSIGNED,
    id_site       INT UNSIGNED                       NOT NULL,
    channel_id    tinyint unsigned                   not null,
    #     context       varchar(50)                                                          DEFAULT NOT NULL,
    recipient     VARCHAR(512)                       NOT NULL, -- increased from 255
    status        ENUM ('pending', 'sent', 'failed') NOT NULL,
    trigger_value VARCHAR(64)                        NOT NULL,
    metadata      JSON,
    error_message TEXT,
    created_at    TIMESTAMP                          NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP                          NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_date  DATETIME GENERATED ALWAYS AS (DATE(created_at)) STORED,

    PRIMARY KEY (id),
    INDEX idx_notification_logs_trigger_id (trigger_id),
    INDEX idx_notification_logs_template_id (template_id),
    INDEX idx_notification_logs_site_trigger (id_site, trigger_id),
    INDEX idx_notification_logs_site_channel_trigger_value (id_site, channel_id, trigger_value),
    INDEX idx_notification_logs_site_created_date (id_site, created_date),
    INDEX idx_notification_logs_created_date (created_date),
    index idx_notification_logs_status (status),
    index idx_notification_logs_channel_id (channel_id),
    FOREIGN KEY (trigger_id) REFERENCES triggers (id) ON UPDATE CASCADE ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES templates (id) ON UPDATE CASCADE ON DELETE SET NULL,
    foreign key (channel_id) references channel_types (id) on update cascade on delete restrict
) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;


CREATE TABLE site_tokens
(
    id                     INT UNSIGNED AUTO_INCREMENT,
    id_site                INT UNSIGNED     NOT NULL,
    channel_id             TINYINT UNSIGNED NOT NULL,
    access_token_encrypted TEXT             NOT NULL,
    iv                     CHAR(32)         NOT NULL,
    tag                    CHAR(32)         NOT NULL,
    created_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at             timestamp,

    primary key (id),
    UNIQUE KEY uq_site_tokens_site_channel (id_site, channel_id),
    INDEX idx_site_tokens_created_at (created_at),
    foreign key (channel_id) references channel_types (id) on update cascade on delete restrict

) DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

