
# Dev

NODE_ENV=development|production
BUILD_TARGET=dev
PORT=8500

DATABASE_URL=

REDIS_HOST=redis|localhost
REDIS_PORT=6379

#RABBIT_MQ_HOST=amqp://guest:guest@localhost|rabbitmq

RABBITMQ_HOST=localhost|rabbitmq
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

HEATMAP_CONTEXT_BASE_URL=https://stage10.heatmapcore.com/backend/incontext

SMTP_HOST=
SMTP_PORT=
SMTP_SECURE=
SMTP_USERNAME=
SMTP_PASSWORD=
DEFAULT_EMAIL_FROM=

TRIAL_SITES_SCHEDULE="30 9 * * *" // run at 9:30
ACTIVE_SITES_SCHEDULE="0 10 * * 1-5" // run at 10:00
WEEKLY_METRICS_SCHEDULE="55 11 * * 1" // run at 11:55 am on mondays

EMAIL_ON=false
RUN_SEED=false
LOGGER_TOGGLE=false

SLACK_CLIENT_ID=
SLACK_CLIENT_SECRET=
SLACK_REDIRECT_BASE_URL=
ENCRYPTION_KEY=
OBSERVATION_URL=https://test-observation-server.onrender.com/save-incontext-observation

#prod
NODE_ENV=production
BUILD_TARGET=prod
PORT=

DATABASE_URL=

REDIS_HOST=redis|localhost
REDIS_PORT=6379

#RABBIT_MQ_HOST=amqp://guest:guest@localhost|rabbitmq

RABBITMQ_HOST=localhost|rabbitmq
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

HEATMAP_CONTEXT_BASE_URL=https:portal.heatmap.com/backend/incontext

SMTP_HOST=
SMTP_PORT=
SMTP_SECURE=
SMTP_USERNAME=
SMTP_PASSWORD=
DEFAULT_EMAIL_FROM=

TRIAL_SITES_SCHEDULE="30 9 * * *" // run at 9:30
ACTIVE_SITES_SCHEDULE="0 10 * * 1-5" // run at 10:00
WEEKLY_METRICS_SCHEDULE="55 11 * * 1" // run at 11:55 am on mondays

EMAIL_ON=true
RUN_SEED=false
LOGGER_TOGGLE=false

SLACK_CLIENT_ID=
SLACK_CLIENT_SECRET=
SLACK_REDIRECT_BASE_URL=
ENCRYPTION_KEY=
OBSERVATION_URL=

