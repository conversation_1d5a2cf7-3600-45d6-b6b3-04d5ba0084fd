# Notification Engine Documentation

Comprehensive documentation for the Notification Engine System

**Version:** 1.0.0

*Last updated: 2025-06-18*

---

## Table of Contents

1. [Overview](#overview)
   1.1 [Introduction](#introduction)

2. [API Reference](#api-reference)
   2.1 [REST API](#rest-api)

3. [Architecture & Design](#architecture-design)
   3.1 [Database Schema](#database-schema)
   3.2 [Queue System](#queue-system)
   3.3 [Template Engine](#template-engine)

4. [Development Guide](#development-guide)
   4.1 [Testing Guide](#testing-guide)
   4.2 [Deployment Guide](#deployment-guide)

5. [Troubleshooting](#troubleshooting)
   5.1 [Troubleshooting Guide](#troubleshooting-guide)

---

## 1. Overview

Introduction and getting started guide

### 1.1 Introduction

A comprehensive TypeScript-based notification engine designed to handle multi-channel notifications, trigger evaluations, and template management for SaaS applications.

#### Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Getting Started](#getting-started)
- [Database Schema](#database-schema)
- [Queue System](#queue-system)
- [Template Engine](#template-engine)
- [API Documentation](#api-documentation)
- [Environment Configuration](#environment-configuration)
- [Development Guide](#development-guide)
- [Deployment](#deployment)
- [Documentation](#documentation)

#### Overview

The Notification Engine System is a robust, scalable solution for managing notifications across multiple channels including email, Slack, and HubSpot. It features:

- **Multi-channel notifications** (Email, Slack, HubSpot CRM)
- **Template-based messaging** with Handlebars templating
- **Trigger-based automation** with configurable conditions
- **Queue-based processing** using BullMQ and RabbitMQ
- **Comprehensive audit logging** and notification tracking
- **Site-specific customization** and preferences
- **Real-time and scheduled notifications**

#### Architecture

#### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Queue System   │    │   Connectors    │
│                 │    │                 │    │                 │
│ • REST APIs     │───▶│ • BullMQ/Redis  │───▶│ • Email (SMTP)  │
│ • Webhooks      │    │ • RabbitMQ      │    │ • Slack API     │
│ • Admin Panel   │    │ • Job Queues    │    │ • HubSpot CRM   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │ Template Engine │    │   External      │
│                 │    │                 │    │   Services      │
│ • MySQL/Prisma  │    │ • Handlebars    │    │                 │
│ • Notifications │    │ • Email/Slack   │    │ • Site APIs     │
│ • Templates     │    │ • Validation    │    │ • Analytics     │
│ • Triggers      │    │ • Rendering     │    │ • Integrations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### Core Components

1. **API Layer** - Express.js REST API with middleware for authentication and validation
2. **Queue System** - Multi-queue architecture for job processing and message routing
3. **Template Engine** - Handlebars-based templating with layout support
4. **Database Layer** - Prisma ORM with MySQL for data persistence
5. **Connectors** - Channel-specific integrations for message delivery
6. **Job Processors** - Background workers for notification processing

#### Technology Stack

#### Core Technologies
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: MySQL with Prisma ORM
- **Queue System**: BullMQ (Redis) + RabbitMQ
- **Template Engine**: Handlebars.js
- **Validation**: Zod schemas

#### Key Dependencies
- **@bull-board/express** - Queue monitoring dashboard
- **@slack/web-api** - Slack integration
- **nodemailer** - Email sending
- **axios** - HTTP client for external APIs
- **helmet** - Security middleware
- **ioredis** - Redis client
- **amqplib** - RabbitMQ client

#### Development Tools
- **tsx** - TypeScript execution
- **copyfiles** - Build asset copying
- **pino** - Structured logging
- **dotenv** - Environment configuration

#### Getting Started

#### Prerequisites

- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- RabbitMQ 3.8+

#### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd notification-engine/engine-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   pnpm install
   ```

3. **Environment setup**
   ```bash
   cp env.sample .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   ```

5. **Start the application**
   ```bash
   npm run dev
   ```

#### Docker Setup

```bash
docker-compose up -d
```

#### Database Schema

The system uses a comprehensive database schema with the following core entities:

#### Core Models

##### Notifications & Logging
- **NotificationLog** - Tracks all notification attempts and statuses
- **TemplateAuditLog** - Maintains template change history

##### Templates & Content
- **Template** - Stores notification templates with versioning
- **ContentType** - Defines template content formats (HTML, JSON, Block Kit, etc.)
- **Parameter** - Template and trigger parameter definitions

##### Triggers & Automation
- **Trigger** - Defines automation rules and conditions
- **TriggerType** - Categorizes trigger types
- **SiteTriggerSetting** - Site-specific trigger configurations
- **SiteNotificationPreference** - User notification preferences

##### Channel Management
- **ChannelType** - Supported notification channels (email, slack, hubspot)
- **SiteToken** - Encrypted access tokens for external services

#### Key Relationships

```sql
NotificationLog ──▶ Trigger
NotificationLog ──▶ Template
NotificationLog ──▶ ChannelType

Template ──▶ Trigger
Template ──▶ ChannelType
Template ──▶ ContentType

Trigger ──▶ TriggerType
Trigger ──▶ Parameter[]

SiteTriggerSetting ──▶ Trigger
SiteNotificationPreference ──▶ ChannelType
SiteToken ──▶ ChannelType
```

#### Database Features
- **Soft deletes** with `deletedAt` timestamps
- **Comprehensive indexing** for performance
- **UUID public IDs** for external references
- **JSON metadata** columns for flexibility
- **Audit trails** for compliance and debugging

#### Queue System

The notification engine uses a dual-queue architecture combining BullMQ (Redis-based) and RabbitMQ for different use cases.

#### Queue Architecture

##### BullMQ Queues (Redis-based)
```typescript
// High-performance job queues with built-in retry logic
EvaluationQueue    // Site evaluation and trigger checking
TriggerQueue       // Trigger processing and condition evaluation
NotificationQueue  // Notification delivery processing
BotQueue          // Bot and automation tasks
ObservationQueue  // Analytics and monitoring
```

##### RabbitMQ Queues (Message broker)
```typescript
// Distributed messaging with exchange routing
TriggerExchange    // Routes trigger events
NotificationExchange // Routes notification jobs
```

#### Queue Configuration

Each queue supports:
- **Retry mechanisms** with exponential backoff
- **Dead letter queues** for failed jobs
- **Job prioritization** and scheduling
- **Concurrency control** and rate limiting
- **Monitoring and metrics** via Bull Board

#### Job Processing Flow

```
Site Event ──▶ EvaluationQueue ──▶ TriggerQueue ──▶ NotificationQueue ──▶ Delivery
     │               │                  │                   │              │
     ▼               ▼                  ▼                   ▼              ▼
  Analytics    Condition Check    Template Render    Channel Send    Audit Log
```

#### Template Engine

The template engine provides a powerful, flexible system for creating and managing notification templates across multiple channels.

#### Features

- **Handlebars templating** with custom helpers
- **Layout inheritance** for consistent branding
- **Multi-channel support** (Email HTML, Slack Block Kit, etc.)
- **Parameter validation** with Zod schemas
- **Template versioning** and rollback capabilities
- **Hot reloading** in development mode

#### Template Structure

```
templates/
├── layouts/           # Base layouts
│   ├── email.hbs     # Email HTML layout
│   └── slack.hbs     # Slack message layout
├── email/            # Email templates
│   ├── welcome.hbs
│   ├── reminder.hbs
│   └── summary.hbs
└── slack/            # Slack templates
    ├── alert.hbs
    └── notification.hbs
```

#### Template Registry

Templates are registered with validation schemas:

```typescript
export const templateRegistry = {
  'weekly-summary': {
    email: {
      schema: z.object({
        siteName: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        metrics: z.object({
          sessions: z.number(),
          revenue: z.number()
        })
      })
    },
    slack: {
      schema: z.object({
        // Slack-specific schema
      })
    }
  }
}
```

#### Template Examples

##### Email Template (Handlebars)
```handlebars
{{#> email-layout}}
  {{#content "header"}}
    <h1>Weekly Summary for {{siteName}}</h1>
  {{/content}}

  {{#content "body"}}
    <p>Here's your weekly summary from {{startDate}} to {{endDate}}:</p>
    <ul>
      <li>Sessions: {{metrics.sessions}}</li>
      <li>Revenue: ${{metrics.revenue}}</li>
    </ul>
  {{/content}}
{{/email-layout}}
```

##### Slack Template (Block Kit)
```handlebars
{
  "blocks": [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "Weekly Summary: {{siteName}}"
      }
    },
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*Sessions:* {{metrics.sessions}}\n*Revenue:* ${{metrics.revenue}}"
      }
    }
  ]
}
```

#### API Documentation

#### Core Endpoints

##### Trigger Management
```http
POST   /trigger/evaluation    # Trigger site evaluation
GET    /trigger/:id          # Get trigger details
PUT    /trigger/:id          # Update trigger configuration
```

##### Real-time Notifications
```http
POST   /real-time            # Send immediate notification
```

##### Slack Integration
```http
POST   /slack/auth           # OAuth authentication
POST   /slack/message        # Send Slack message
```

##### Admin & Monitoring
```http
GET    /up                   # Health check
GET    /admin/queues         # Queue monitoring dashboard
```

#### Request/Response Examples

##### Send Real-time Notification
```http
POST /real-time
Content-Type: application/json

{
  "idSite": 123,
  "triggerName": "login-reminder",
  "templateData": {
    "userName": "John Doe",
    "daysSinceLogin": 7
  },
  "channels": ["email", "slack"]
}
```

##### Trigger Site Evaluation
```http
POST /trigger/evaluation
Content-Type: application/json

{
  "sites": [123, 456, 789],
  "triggerTypes": ["trial", "engagement"],
  "immediate": true
}
```

#### Environment Configuration

#### Required Environment Variables

```bash
#### Application
NODE_ENV=development|production
PORT=3000
BUILD_TARGET=dev|prod

#### Database
DATABASE_URL=mysql://user:password@localhost:3306/notifications

#### Redis (BullMQ)
REDIS_HOST=localhost
REDIS_PORT=6379

#### RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

#### Email (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
DEFAULT_EMAIL_FROM=<EMAIL>

#### Slack Integration
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret
SLACK_REDIRECT_BASE_URL=https://yourapp.com

#### Scheduling (Cron expressions)
TRIAL_SITES_SCHEDULE=0 8 * * *      # Daily at 8 AM
ACTIVE_SITES_SCHEDULE=0 0 * * 1     # Weekly on Monday
WEEK_METRICS_SCHEDULE=0 6 * * 1     # Weekly on Monday at 6 AM

#### External Services
HEATMAP_CONTEXT_BASE_URL=https://api.yourservice.com
OBSERVATION_URL=https://observation.yourservice.com

#### Security
ENCRYPTION_KEY=your-32-character-encryption-key

#### Feature Flags
EMAIL_ON=true
LOGGER_TOGGLE=true
RUN_SEED=false
```

#### Environment Validation

The application includes comprehensive environment validation:

```typescript
// Validates required variables, cron expressions, and data types
export const envConfig = {
  nodeEnv: process.env.NODE_ENV,     // Validated against allowed values
  port: Number(process.env.PORT),    // Parsed and validated
  redis: {
    host: process.env.REDIS_HOST,    // Required
    port: Number(process.env.REDIS_PORT)
  },
  // ... other validated configs
}
```

#### Development Guide

#### Project Structure

```
src/
├── server.ts              # Application entry point
├── app.ts                 # Express app configuration
├── db.ts                  # Database connection
├── env.constant.ts        # Environment validation
├── template-engine.ts     # Template processing
├── template-registry.ts   # Template definitions
├── api/                   # REST API layer
│   ├── controllers/       # Request handlers
│   ├── routes/           # Route definitions
│   ├── middlewares.ts    # Express middleware
│   ├── schemas/          # Validation schemas
│   └── utils/            # API utilities
├── jobs/                 # Queue job definitions
│   ├── evaluation/       # Site evaluation jobs
│   ├── trigger/          # Trigger processing jobs
│   ├── notification/     # Notification jobs
│   ├── bot/             # Bot automation jobs
│   └── observation/     # Analytics jobs
├── messaging/           # Queue and messaging
│   ├── queue-service.ts # RabbitMQ service
│   ├── consumers/       # Message consumers
│   └── producers/       # Message producers
├── connectors/          # External service integrations
│   ├── smtp.ts         # Email connector
│   ├── slack.ts        # Slack connector
│   └── hubspot.ts      # HubSpot connector
├── services/           # Business logic services
├── types/              # TypeScript type definitions
└── utils/              # Shared utilities
```

#### Development Commands

```bash
#### Development
npm run dev                # Start with hot reload
npm run build             # Build for production
npm run seed              # Run database seed

#### Database
npx prisma generate       # Generate Prisma client
npx prisma db push        # Push schema changes
npx prisma studio        # Database GUI

#### Queue Monitoring
#### Visit http://localhost:3000/admin/queues
```

#### Adding New Templates

1. **Create template files**
   ```bash
   # Email template
   touch src/new-templates/my-template.hbs

   # Slack template
   touch src/slack-templates/my-template.hbs
   ```

2. **Register in template registry**
   ```typescript
   // src/template-registry.ts
   export const templateRegistry = {
     'my-template': {
       email: {
         schema: z.object({
           // Define validation schema
         })
       },
       slack: {
         schema: z.object({
           // Define validation schema
         })
       }
     }
   }
   ```

3. **Create database entries**
   ```sql
   -- Add to triggers table
   INSERT INTO triggers (name, description) VALUES ('my-template', 'My template description');

   -- Add templates for each channel
   INSERT INTO templates (trigger_id, channel_id, name, body, content_type_id)
   VALUES (trigger_id, 1, 'my-template-email', 'template-content', 1);
   ```

#### Adding New Job Types

1. **Create job definition**
   ```typescript
   // src/jobs/my-job/jobs.ts
   export interface MyJobData {
     siteId: number;
     // ... other properties
   }

   export async function processMyJob(job: Job<MyJobData>) {
     // Job processing logic
   }
   ```

2. **Add to queue**
   ```typescript
   // src/jobs/my-job/my-job-queue.ts
   export class MyJobQueue extends BaseQueue<MyJobData> {
     // Queue implementation
   }
   ```

3. **Register consumer**
   ```typescript
   // src/messaging/consumers/my-job.ts
   export async function startMyJobConsumer(queue: MyJobQueue) {
     // Consumer implementation
   }
   ```

#### Deployment

#### Docker Deployment

The application includes Docker configuration for containerized deployment:

```yaml
#### docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - mysql
      - redis
      - rabbitmq

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: notifications

  redis:
    image: redis:6-alpine

  rabbitmq:
    image: rabbitmq:3-management
```

#### Production Considerations

1. **Environment Variables**
   - Use secure secret management
   - Enable SSL/TLS for external connections
   - Configure proper CORS settings

2. **Database**
   - Set up read replicas for scaling
   - Configure regular backups
   - Monitor query performance

3. **Queue System**
   - Configure Redis persistence
   - Set up RabbitMQ clustering
   - Monitor queue depths and processing times

4. **Monitoring**
   - Set up application performance monitoring
   - Configure log aggregation
   - Set up alerts for critical failures

5. **Security**
   - Enable rate limiting
   - Configure proper authentication
   - Regular security updates

#### Health Checks

The application provides health check endpoints:

```http
GET /up                    # Basic health check
GET /admin/queues         # Queue status monitoring
```

#### Logging

Structured logging with multiple levels:

```typescript
// Log levels: trace, debug, info, warn, error, fatal
logger.info('Application started', { port: 3000 });
logger.error('Database connection failed', { error: err.message });
```

---

#### Documentation

#### Comprehensive Guides

The system includes detailed documentation for all aspects:

- **[Database Schema](docs/DATABASE.md)** - Complete database documentation with models, relationships, and optimization
- **[Queue System](docs/QUEUES.md)** - Dual-queue architecture with BullMQ and RabbitMQ implementation details
- **[Template Engine](docs/TEMPLATES.md)** - Handlebars templating system with custom helpers and multi-channel support
- **[API Reference](docs/API.md)** - Complete REST API documentation with examples and schemas
- **[Testing Guide](docs/TESTING.md)** - Comprehensive testing strategies including unit, integration, and load testing
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment instructions for Docker, AWS, and Kubernetes
- **[Troubleshooting Guide](docs/TROUBLESHOOTING.md)** - Common issues, debugging techniques, and FAQ

#### Quick Links

- **Setup & Installation**: See [Getting Started](#getting-started) section above
- **API Usage**: Check [API Documentation](docs/API.md) for endpoint details
- **Template Development**: Review [Template System](docs/TEMPLATES.md) guide
- **Queue Monitoring**: Use [Queue Dashboard](docs/QUEUES.md#monitoring) instructions
- **Performance Tuning**: Follow [Deployment Guide](docs/DEPLOYMENT.md#scaling-considerations)
- **Issue Resolution**: Consult [Troubleshooting Guide](docs/TROUBLESHOOTING.md)

---

#### Support

For questions, issues, or contributions:

1. Check the [Issues](repository-url/issues) section
2. Review existing documentation in the `docs/` folder
3. Consult the [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
4. Contact the development team

#### License

[License Type] - See LICENSE file for details

*Source: [README.md](README.md)*


---

## 2. API Reference

Complete API documentation with examples

### 2.1 REST API

#### Overview

The Notification Engine provides a RESTful API for managing notifications, triggers, templates, and integrations. The API is built with Express.js and includes comprehensive middleware for validation, authentication, and error handling.

#### Base Configuration

- **Base URL**: `http://localhost:3000` (development)
- **Content-Type**: `application/json`
- **Authentication**: Bearer token (when implemented)

#### Core Endpoints

#### Health Check

##### GET /up
Check the health status of the application.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-06-09T10:30:00.000Z",
  "uptime": 1800,
  "version": "1.0.0"
}
```

**Status Codes:**
- `200` - Service is healthy
- `503` - Service is unhealthy

---

#### Trigger Management

##### POST /trigger/evaluation
Trigger evaluation of sites for notification conditions.

**Request Body:**
```json
{
  "sites": [123, 456, 789],
  "triggerTypes": ["trial", "engagement", "revenue"],
  "immediate": true,
  "metadata": {
    "source": "manual",
    "requestedBy": "admin"
  }
}
```

**Parameters:**
- `sites` (required): Array of site IDs to evaluate
- `triggerTypes` (optional): Specific trigger types to check
- `immediate` (optional): Skip scheduling and process immediately
- `metadata` (optional): Additional context information

**Response:**
```json
{
  "success": true,
  "evaluationId": "eval_123456789",
  "sitesQueued": 3,
  "estimatedProcessingTime": "2-5 minutes"
}
```

**Status Codes:**
- `200` - Evaluation queued successfully
- `400` - Invalid request parameters
- `500` - Internal server error

---

#### Real-time Notifications

##### POST /real-time
Send immediate notifications without trigger evaluation.

**Request Body:**
```json
{
  "idSite": 123,
  "triggerName": "login-reminder",
  "templateData": {
    "userName": "John Doe",
    "daysSinceLogin": 7,
    "loginUrl": "https://app.example.com/login"
  },
  "channels": ["email", "slack"],
  "priority": "high",
  "scheduledFor": "2025-06-09T15:30:00.000Z"
}
```

**Parameters:**
- `idSite` (required): Site identifier
- `triggerName` (required): Name of trigger/template to use
- `templateData` (required): Data for template rendering
- `channels` (optional): Specific channels to send to (default: all enabled)
- `priority` (optional): Message priority (low, normal, high)
- `scheduledFor` (optional): Schedule for future delivery

**Response:**
```json
{
  "success": true,
  "notificationId": "notif_987654321",
  "status": "queued",
  "channels": {
    "email": {
      "status": "queued",
      "estimatedDelivery": "2025-06-09T10:35:00.000Z"
    },
    "slack": {
      "status": "queued",
      "estimatedDelivery": "2025-06-09T10:31:00.000Z"
    }
  }
}
```

**Status Codes:**
- `200` - Notification queued successfully
- `400` - Invalid request parameters
- `422` - Template validation failed
- `500` - Internal server error

---

#### Slack Integration

##### POST /slack/auth
Handle Slack OAuth authentication flow.

**Request Body:**
```json
{
  "code": "oauth_code_from_slack",
  "state": "random_state_string",
  "idSite": 123
}
```

**Response:**
```json
{
  "success": true,
  "teamName": "Example Team",
  "channelId": "C1234567890",
  "channelName": "#notifications",
  "accessToken": "encrypted_token_reference"
}
```

##### POST /slack/message
Send a direct Slack message (for testing/admin use).

**Request Body:**
```json
{
  "idSite": 123,
  "message": "Test notification message",
  "subject": "Test Subject",
  "channel": "#general"
}
```

---

#### Admin Endpoints

#### Queue Monitoring

##### GET /admin/queues
Access the Bull Board dashboard for queue monitoring.

**Response:** HTML dashboard interface

**Features:**
- Real-time queue statistics
- Job details and logs
- Manual job management
- Performance metrics

---

#### Error Handling

#### Standard Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Request validation failed",
    "details": {
      "field": "sites",
      "issue": "Array must contain at least one site ID"
    },
    "timestamp": "2025-06-09T10:30:00.000Z",
    "requestId": "req_123456789"
  }
}
```

#### Error Codes

| Code                        | Description                     | HTTP Status |
| --------------------------- | ------------------------------- | ----------- |
| `VALIDATION_ERROR`          | Request validation failed       | 400         |
| `TEMPLATE_NOT_FOUND`        | Template does not exist         | 404         |
| `TEMPLATE_VALIDATION_ERROR` | Template data validation failed | 422         |
| `SITE_NOT_FOUND`            | Site ID not found               | 404         |
| `RATE_LIMIT_EXCEEDED`       | Too many requests               | 429         |
| `QUEUE_UNAVAILABLE`         | Queue service unavailable       | 503         |
| `INTERNAL_ERROR`            | Unexpected server error         | 500         |

---

#### Request/Response Examples

#### Trigger Weekly Summary

```http
POST /trigger/evaluation
Content-Type: application/json

{
  "sites": [123],
  "triggerTypes": ["weekly-summary"],
  "immediate": true
}
```

```json
{
  "success": true,
  "evaluationId": "eval_202506091030_123",
  "sitesQueued": 1,
  "estimatedProcessingTime": "1-2 minutes"
}
```

#### Send Login Reminder

```http
POST /real-time
Content-Type: application/json

{
  "idSite": 123,
  "triggerName": "login-case-study",
  "templateData": {
    "userName": "Sarah Johnson",
    "siteName": "E-commerce Store",
    "daysSinceLogin": 14,
    "caseStudyUrl": "https://example.com/case-study",
    "loginUrl": "https://app.example.com/login"
  },
  "channels": ["email"]
}
```

```json
{
  "success": true,
  "notificationId": "notif_20250609103045_123",
  "status": "queued",
  "channels": {
    "email": {
      "status": "queued",
      "recipient": "<EMAIL>",
      "estimatedDelivery": "2025-06-09T10:32:00.000Z"
    }
  }
}
```

#### Send Team Invitation Reminder

```http
POST /real-time
Content-Type: application/json

{
  "idSite": 456,
  "triggerName": "team-not-invited",
  "templateData": {
    "userName": "Mike Chen",
    "siteName": "SaaS Dashboard",
    "inviteTeamUrl": "https://app.example.com/team/invite",
    "benefitsText": "Collaborating with your team can increase productivity by 40%"
  },
  "channels": ["email", "slack"]
}
```

---

#### Middleware

#### Request Validation

All endpoints include request validation middleware using Zod schemas:

```typescript
// Example validation schema
const triggerEvaluationSchema = z.object({
  sites: z.array(z.number().positive()).min(1),
  triggerTypes: z.array(z.string()).optional(),
  immediate: z.boolean().optional(),
  metadata: z.record(z.any()).optional()
});
```

#### Error Handling

Global error handling middleware catches and formats all errors:

```typescript
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  const errorResponse = {
    error: {
      code: error.name || 'INTERNAL_ERROR',
      message: error.message,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] || generateRequestId()
    }
  };

  if (error instanceof ValidationError) {
    res.status(400).json(errorResponse);
  } else if (error instanceof NotFoundError) {
    res.status(404).json(errorResponse);
  } else {
    res.status(500).json(errorResponse);
  }
});
```

#### Database Connection Management

Automatic Prisma connection management:

```typescript
export const disconnectPrismaV1 = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Skip for monitoring endpoints
  if (EXCLUDED_PATHS.some(path => req.originalUrl.startsWith(path))) {
    return next();
  }

  let isCleanUp = false;
  const cleanup = async (event: string) => {
    if (isCleanUp) return;

    try {
      isCleanUp = true;
      await prisma.$disconnect();
      console.log(`Prisma disconnect activated in ${event}`);
    } catch (err) {
      console.error(`Prisma disconnect failed in ${event}`);
    }
  };

  res.on('close', () => cleanup('close'));
  res.on('finish', () => cleanup('finish'));

  next();
};
```

---

#### Authentication & Security

#### Security Headers

```typescript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

#### CORS Configuration

```typescript
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://yourapp.com', 'https://dashboard.yourapp.com']
    : true,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}));
```

#### Rate Limiting (Recommended)

```typescript
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP'
    }
  }
});

app.use('/api/', limiter);
```

---

#### WebSocket Events (Future Enhancement)

#### Real-time Updates

For future implementation of real-time notifications:

```typescript
// WebSocket event types
interface NotificationEvent {
  type: 'notification.sent' | 'notification.failed' | 'notification.delivered';
  data: {
    notificationId: string;
    idSite: number;
    channel: string;
    status: string;
    timestamp: string;
  };
}

// Client subscription
ws.send(JSON.stringify({
  type: 'subscribe',
  channels: ['notifications', 'triggers'],
  siteId: 123
}));
```

---

#### API Client Examples

#### JavaScript/Node.js

```javascript
class NotificationAPI {
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async triggerEvaluation(sites, options = {}) {
    const response = await fetch(`${this.baseUrl}/trigger/evaluation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        sites,
        ...options
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return response.json();
  }

  async sendRealTimeNotification(notification) {
    const response = await fetch(`${this.baseUrl}/real-time`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify(notification)
    });

    return response.json();
  }
}

// Usage
const api = new NotificationAPI('http://localhost:3000', 'your-api-key');

await api.triggerEvaluation([123, 456], {
  triggerTypes: ['weekly-summary'],
  immediate: true
});

await api.sendRealTimeNotification({
  idSite: 123,
  triggerName: 'login-reminder',
  templateData: {
    userName: 'John Doe',
    daysSinceLogin: 7
  }
});
```

#### Python

```python
import requests
import json

class NotificationAPI:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

    def trigger_evaluation(self, sites, **options):
        data = {'sites': sites, **options}
        response = requests.post(
            f'{self.base_url}/trigger/evaluation',
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()

    def send_real_time_notification(self, notification):
        response = requests.post(
            f'{self.base_url}/real-time',
            headers=self.headers,
            json=notification
        )
        response.raise_for_status()
        return response.json()

#### Usage
api = NotificationAPI('http://localhost:3000', 'your-api-key')

api.trigger_evaluation([123, 456], trigger_types=['weekly-summary'])

api.send_real_time_notification({
    'idSite': 123,
    'triggerName': 'login-reminder',
    'templateData': {
        'userName': 'John Doe',
        'daysSinceLogin': 7
    }
})
```

---

#### Testing

#### Unit Tests

```typescript
describe('API Endpoints', () => {
  test('POST /trigger/evaluation', async () => {
    const response = await request(app)
      .post('/trigger/evaluation')
      .send({
        sites: [123],
        immediate: true
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.evaluationId).toBeDefined();
  });

  test('POST /real-time validation', async () => {
    const response = await request(app)
      .post('/real-time')
      .send({
        // Missing required fields
      })
      .expect(400);

    expect(response.body.error.code).toBe('VALIDATION_ERROR');
  });
});
```

#### Integration Tests

```typescript
describe('Notification Flow', () => {
  test('should send email notification', async () => {
    const mockEmailSend = jest.fn();
    jest.spyOn(emailConnector, 'send').mockImplementation(mockEmailSend);

    await request(app)
      .post('/real-time')
      .send({
        idSite: 123,
        triggerName: 'weekly-summary',
        templateData: { /* test data */ },
        channels: ['email']
      })
      .expect(200);

    // Verify queue processing
    await new Promise(resolve => setTimeout(resolve, 1000));

    expect(mockEmailSend).toHaveBeenCalledWith({
      recipient: '<EMAIL>',
      subject: expect.stringContaining('Weekly Summary'),
      content: expect.stringContaining('Test Site')
    });
  });
});
```

---

#### Performance Considerations

#### Response Time Targets

- **Health checks**: < 100ms
- **Trigger evaluation**: < 500ms (queueing)
- **Real-time notifications**: < 1s (queueing)
- **Slack auth**: < 2s

#### Caching Strategy

```typescript
// Redis caching for frequent lookups
const cache = new Redis(process.env.REDIS_URL);

app.use('/api/sites/:id', async (req, res, next) => {
  const cached = await cache.get(`site:${req.params.id}`);
  if (cached) {
    res.json(JSON.parse(cached));
    return;
  }
  next();
});
```

#### Monitoring

- Request/response time tracking
- Error rate monitoring
- Queue depth alerts
- Database connection pool monitoring

*Source: [docs/API.md](docs/API.md)*


---

## 3. Architecture & Design

System architecture and design documentation

### 3.1 Database Schema

#### Overview

The notification engine uses a comprehensive MySQL database schema managed through Prisma ORM. The schema is designed to support multi-channel notifications, template management, trigger automation, and comprehensive audit logging.

#### Core Models

#### NotificationLog
**Purpose**: Tracks all notification attempts, delivery status, and metadata

```prisma
model NotificationLog {
  id           BigInt             @id @default(autoincrement())
  triggerId    Int?               // Associated trigger
  templateId   Int?               // Template used
  idSite       Int                // Site identifier
  channelId    Int                // Delivery channel
  recipient    String             // Target recipient
  status       NotificationStatus // pending | sent | failed
  triggerValue String             // Trigger condition value
  metadata     Json?              // Additional data
  errorMessage String?            // Failure details
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @default(now())
  createdDate  DateTime?          // Indexed date field
}
```

**Key Features**:
- Comprehensive indexing for performance queries
- Status tracking for delivery confirmation
- JSON metadata for flexible data storage
- Error message logging for debugging

**Indexes**:
- `idx_notification_logs_site_channel_trigger_value`
- `idx_notification_logs_site_created_date`
- `idx_notification_logs_status`

#### Trigger
**Purpose**: Defines automation rules and conditions for notifications

```prisma
model Trigger {
  id                   Int                    @id @default(autoincrement())
  name                 String                 @unique
  description          String?
  triggerTypeId        Int                    // Type of trigger
  metricKey            String?               // Metric to evaluate
  isConfigurable       Boolean               @default(false)
  metadata             Json?                 // Additional configuration
  cooldownSeconds      Int?                  // Minimum time between triggers
  publicId             String                @unique @default(dbgenerated("(uuid())"))
  maxTriggerCount      Int?                  // Maximum times to trigger
  maxTriggerPeriod     TriggerIntervalPeriod?
  minIntervalCount     Int?                  // Minimum interval
  minIntervalUnit      TriggerIntervalPeriod?
  fireOnce             Boolean               @default(false)
  deltaThreshold       Decimal?              // Change threshold
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now())
  createdBy            String                @default("system")
  updatedBy            String                @default("system")
  deletedAt            DateTime?             // Soft delete
}
```

**Key Features**:
- Flexible trigger conditions with JSON metadata
- Cooldown periods to prevent spam
- Maximum trigger counts and intervals
- Soft delete capability
- UUID public IDs for external references

#### Template
**Purpose**: Stores notification templates with versioning support

```prisma
model Template {
  id            Int              @id @default(autoincrement())
  triggerId     Int?             // Associated trigger
  name          String
  description   String?
  version       Int              @default(1)
  channelId     Int              // Target channel
  subject       String?          // Email subject or notification title
  body          String           // Template content
  contentTypeId Int              // Content format
  metadata      Json?
  status        TemplateStatus   @default(draft) // draft | archived | published
  publicId      String           @unique @default(dbgenerated("(uuid())"))
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @default(now())
  deletedAt     DateTime?        // Soft delete
  createdBy     String?
  updatedBy     String?
}
```

**Key Features**:
- Template versioning for rollback capability
- Multi-channel support
- Status management (draft, published, archived)
- Audit trail with created/updated by tracking

#### Parameter
**Purpose**: Defines parameters for templates and triggers with validation

```prisma
model Parameter {
  id           Int           @id @default(autoincrement())
  triggerId    Int?          // For trigger parameters
  templateId   Int?          // For template parameters
  name         String
  description  String?
  required     Boolean       @default(false)
  paramTypeId  Int           // Data type
  validations  Json?         // Validation rules
  defaultValue String?
  exampleValue String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @default(now())
}
```

**Key Features**:
- Flexible parameter definition for templates and triggers
- Type validation with JSON validation rules
- Default and example values for documentation

#### Reference Tables

#### ChannelType
Defines supported notification channels:
- `email` - SMTP email notifications
- `slack` - Slack workspace notifications
- `hubspot` - HubSpot CRM notifications

#### ContentType
Defines template content formats:
- `html` - HTML formatted email content
- `text` - Plain text content
- `json` - JSON structured data
- `block_kit` - Slack Block Kit format
- `hubspot_crm` - HubSpot CRM format

#### ParameterType
Defines parameter data types:
- `string` - Text values
- `number` - Numeric values
- `boolean` - True/false values
- `date` - Date/time values
- `json` - JSON objects
- `array` - Array of values

#### TriggerType
Categorizes trigger types:
- `time_based` - Scheduled triggers
- `event_based` - Event-driven triggers
- `condition_based` - Conditional triggers

#### Site-Specific Configuration

#### SiteNotificationPreference
**Purpose**: User preferences for notification channels per site

```prisma
model SiteNotificationPreference {
  id                Int         @id @default(autoincrement())
  idSite            Int         // Site identifier
  channelId         Int         // Notification channel
  triggerId         Int?        // Specific trigger
  isEnabled         Boolean     @default(false)
  destination       Json?       // Channel-specific config
  triggerNormalized Int?        // Normalized trigger reference
}
```

#### SiteTriggerSetting
**Purpose**: Site-specific overrides for trigger configurations

```prisma
model SiteTriggerSetting {
  id                       Int                    @id @default(autoincrement())
  idSite                   Int
  triggerId                Int
  isEnabled                Boolean                @default(false)
  conditionOverride        Json?                  // Custom conditions
  minIntervalCountOverride Int?
  minIntervalUnitOverride  TriggerIntervalPeriod?
  maxTriggerCountOverride  Int?
  maxTriggerPeriodOverride TriggerIntervalPeriod?
  deltaThresholdOverride   Decimal?
}
```

#### SiteToken
**Purpose**: Encrypted storage of external service tokens

```prisma
model SiteToken {
  id                   Int         @id @default(autoincrement())
  idSite               Int
  channelId            Int
  accessTokenEncrypted String      // AES encrypted token
  iv                   String      // Initialization vector
  tag                  String      // Authentication tag
}
```

**Security Features**:
- AES-256-GCM encryption for tokens
- Unique initialization vectors per token
- Authentication tags for integrity verification

#### Audit and Logging

#### TemplateAuditLog
**Purpose**: Tracks all template changes for compliance and rollback

```prisma
model TemplateAuditLog {
  id          BigInt               @id @default(autoincrement())
  templateId  Int
  action      TemplateAuditAction? // created | edited | published | archived | rolled_back
  fromVersion Int?
  toVersion   Int?
  performedBy String?
  reason      String?
  createdAt   DateTime             @default(now())
}
```

#### Enums

#### NotificationStatus
```prisma
enum NotificationStatus {
  pending   // Queued for processing
  sent      // Successfully delivered
  failed    // Delivery failed
}
```

#### TemplateStatus
```prisma
enum TemplateStatus {
  draft     // Under development
  published // Active and available
  archived  // Deprecated but preserved
}
```

#### TriggerIntervalPeriod
```prisma
enum TriggerIntervalPeriod {
  minute
  hour
  day
  week
  month
  year
}
```

#### TemplateAuditAction
```prisma
enum TemplateAuditAction {
  created
  edited
  published
  archived
  rolled_back
}
```

#### Database Indexes

#### Performance Indexes
```sql
-- Notification logs for site queries
CREATE INDEX idx_notification_logs_site_created_date ON notification_logs(id_site, created_date);
CREATE INDEX idx_notification_logs_site_channel_trigger_value ON notification_logs(id_site, channel_id, trigger_value);

-- Template queries
CREATE INDEX idx_templates_channel_id ON templates(channel_id);
CREATE INDEX idx_templates_content_type_id ON templates(content_type_id);

-- Parameter lookups
CREATE INDEX idx_parameters_template_name ON parameters(template_id, name);
CREATE INDEX idx_parameters_trigger_name ON parameters(trigger_id, name);

-- Soft delete queries
CREATE INDEX idx_triggers_deleted_at ON triggers(deleted_at);
CREATE INDEX idx_templates_deleted_at ON templates(deleted_at);
```

#### Relationships Diagram

```
NotificationLog
├── belongs_to: Trigger
├── belongs_to: Template
└── belongs_to: ChannelType

Template
├── belongs_to: Trigger
├── belongs_to: ChannelType
├── belongs_to: ContentType
├── has_many: Parameter
├── has_many: NotificationLog
└── has_many: TemplateAuditLog

Trigger
├── belongs_to: TriggerType
├── has_many: Template
├── has_many: Parameter
├── has_many: NotificationLog
├── has_many: SiteTriggerSetting
└── has_many: SiteNotificationPreference

SiteNotificationPreference
├── belongs_to: ChannelType
└── belongs_to: Trigger

SiteTriggerSetting
└── belongs_to: Trigger

SiteToken
└── belongs_to: ChannelType
```

#### Migration Strategy

#### Adding New Columns
```sql
-- Use nullable columns for backward compatibility
ALTER TABLE triggers ADD COLUMN new_feature_flag BOOLEAN DEFAULT FALSE;

-- Update existing records if needed
UPDATE triggers SET new_feature_flag = TRUE WHERE condition;
```

#### Schema Evolution
1. **Add new columns as nullable**
2. **Update application code to handle both states**
3. **Migrate data in batches**
4. **Make columns non-nullable if required**
5. **Clean up old columns after full deployment**

#### Backup and Recovery

#### Backup Strategy
```bash
#### Full database backup
mysqldump -u user -p notifications > backup_$(date +%Y%m%d_%H%M%S).sql

#### Table-specific backup
mysqldump -u user -p notifications notification_logs > notification_logs_backup.sql
```

#### Point-in-Time Recovery
- Binary logging enabled for transaction recovery
- Regular full backups with incremental log backups
- Testing recovery procedures monthly

#### Performance Optimization

#### Query Optimization
- Use composite indexes for multi-column WHERE clauses
- Avoid SELECT * queries, specify columns
- Use LIMIT for pagination queries
- Consider read replicas for reporting queries

#### Table Maintenance
```sql
-- Analyze table statistics
ANALYZE TABLE notification_logs;

-- Optimize table structure
OPTIMIZE TABLE notification_logs;

-- Check table integrity
CHECK TABLE notification_logs;
```

#### Monitoring Queries
```sql
-- Find slow queries
SELECT * FROM information_schema.PROCESSLIST WHERE Time > 60;

-- Check index usage
SHOW INDEX FROM notification_logs;

-- Table size analysis
SELECT
  table_name,
  ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'notifications';
```

*Source: [docs/DATABASE.md](docs/DATABASE.md)*


---

### 3.2 Queue System

#### Overview

The notification engine implements a sophisticated dual-queue architecture combining BullMQ (Redis-based) and RabbitMQ to handle different types of workloads with optimal performance and reliability.

#### Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   BullMQ        │    │   RabbitMQ      │    │   Consumers     │
│   (Redis)       │    │   (AMQP)        │    │                 │
│                 │    │                 │    │                 │
│ • Job Queues    │    │ • Exchanges     │    │ • Site Eval     │
│ • Scheduling    │    │ • Routing       │    │ • Triggers      │
│ • Retry Logic   │    │ • Pub/Sub       │    │ • Notifications │
│ • Monitoring    │    │ • Distribution  │    │ • Observations  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### BullMQ Queues (Redis-based)

#### Queue Types

##### 1. EvaluationQueue
**Purpose**: Site evaluation and trigger condition checking

```typescript
interface SiteEvaluationJob {
  sites: number[];           // Site IDs to evaluate
  triggerTypes?: string[];   // Specific trigger types
  immediate?: boolean;       // Skip scheduling delay
}
```

**Features**:
- Batch processing of multiple sites
- Configurable evaluation intervals
- Priority-based processing
- Automatic retry with exponential backoff

**Configuration**:
```typescript
const evaluationQueue = new EvaluationQueue({
  connection: { host: 'redis-host', port: 6379 },
  defaultJobOptions: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 3,
    backoff: { type: 'exponential', delay: 2000 }
  }
});
```

##### 2. TriggerQueue
**Purpose**: Process trigger conditions and determine notification eligibility

```typescript
interface TriggerJob {
  idSite: number;
  triggerName: string;
  triggerData: Record<string, any>;
  evaluationId?: string;
  metadata?: Record<string, any>;
}
```

**Processing Flow**:
1. Receive trigger evaluation request
2. Check trigger conditions against site data
3. Validate cooldown periods and limits
4. Generate notification jobs if conditions met
5. Update trigger statistics and logs

##### 3. NotificationQueue
**Purpose**: Handle notification delivery across all channels

```typescript
interface NotificationJob {
  idSite: number;
  triggerName: string;
  templateName: string;
  channelType: 'email' | 'slack' | 'hubspot';
  recipient: string;
  templateData: Record<string, any>;
  priority?: number;
  scheduledFor?: Date;
}
```

**Features**:
- Multi-channel delivery support
- Template rendering and validation
- Delivery status tracking
- Error handling and retry logic

##### 4. BotQueue
**Purpose**: Automated tasks and maintenance operations

```typescript
interface BotJob {
  taskType: 'cleanup' | 'maintenance' | 'sync' | 'report';
  parameters: Record<string, any>;
  scheduledTime?: Date;
}
```

**Common Tasks**:
- Database cleanup operations
- Cache warming and maintenance
- External service synchronization
- Periodic reporting and analytics

##### 5. ObservationQueue
**Purpose**: Analytics, monitoring, and data collection

```typescript
interface ObservationJob {
  observationType: 'site_metrics' | 'user_behavior' | 'system_health';
  dataPoints: Record<string, any>;
  aggregationLevel: 'hourly' | 'daily' | 'weekly';
}
```

#### RabbitMQ Messaging (AMQP)

#### Exchange Configuration

##### TriggerExchange
**Type**: Topic Exchange
**Purpose**: Route trigger events to appropriate consumers

```typescript
// Exchange configuration
await queueService.assertExchange('trigger-exchange', 'topic', {
  durable: true,
  autoDelete: false
});

// Routing patterns
const routingKeys = {
  trialSites: 'trigger.trial.*',
  paidSites: 'trigger.paid.*',
  urgentTriggers: 'trigger.*.urgent'
};
```

##### NotificationExchange  
**Type**: Topic Exchange
**Purpose**: Distribute notification jobs to channel-specific consumers

```typescript
// Routing by channel
const notificationRouting = {
  email: 'notification.email.*',
  slack: 'notification.slack.*',
  hubspot: 'notification.hubspot.*'
};
```

#### Queue Binding Examples

```typescript
// Bind queues to exchanges with routing keys
await queueService.bindQueue('email-notifications', 'notification-exchange', 'notification.email.*');
await queueService.bindQueue('slack-notifications', 'notification-exchange', 'notification.slack.*');
await queueService.bindQueue('urgent-triggers', 'trigger-exchange', 'trigger.*.urgent');
```

#### Queue Service Implementation

#### Connection Management

```typescript
export class QueueService {
  private connection?: Connection;
  private channel?: Channel;
  private isInitialized = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  async initialize(): Promise<void> {
    try {
      this.connection = await amqp.connect({
        protocol: 'amqp',
        hostname: envConfig.rabbit.host,
        username: envConfig.rabbit.username,
        password: envConfig.rabbit.password,
        heartbeat: 60
      });

      this.channel = await this.connection.createChannel();
      this.setupEventListeners();
      this.isInitialized = true;

      logger.info('RabbitMQ connection established');
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ:', error);
      await this.handleReconnect();
    }
  }

  private setupEventListeners(): void {
    this.connection?.on('error', async (err) => {
      logger.error('RabbitMQ connection error:', err);
      this.isInitialized = false;
      await this.handleReconnect();
    });

    this.connection?.on('close', async () => {
      logger.warn('RabbitMQ connection closed');
      this.isInitialized = false;
      if (!this.isClosing) {
        await this.handleReconnect();
      }
    });
  }

  private async handleReconnect(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.pow(2, this.reconnectAttempts) * 1000;
    
    logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.initialize();
    }, delay);
  }
}
```

#### Message Publishing

```typescript
// Publish to exchange with routing
export async function publishToExchange<T>(
  exchange: string,
  routingKey: string,
  data: T,
  retries: number = 3
): Promise<void> {
  const queueService = QueueService.getInstance();
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await queueService.publishToExchange(
        exchange,
        routingKey,
        Buffer.from(JSON.stringify(data)),
        { persistent: true }
      );
      
      logger.info('Message published successfully', {
        exchange,
        routingKey,
        attempt
      });
      return;
    } catch (error) {
      logger.error(`Publish attempt ${attempt} failed:`, error);
      
      if (attempt === retries) {
        throw new Error(`Failed to publish after ${retries} attempts`);
      }
      
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, attempt) * 1000)
      );
    }
  }
}
```

#### Consumer Implementation

#### Site Evaluation Consumer

```typescript
export async function startSiteEvaluationConsumer(
  queue: EvaluationQueue
): Promise<void> {
  const queueService = QueueService.getInstance();
  
  await queueService.assertQueue('site-evaluation-queue', { durable: true });
  
  await queueService.consume('site-evaluation-queue', async (message) => {
    if (!message) return;
    
    try {
      const jobData: SiteEvaluationJob = JSON.parse(message.content.toString());
      
      logger.info('Processing site evaluation job', { 
        sites: jobData.sites.length,
        triggerTypes: jobData.triggerTypes 
      });
      
      // Process each site
      for (const siteId of jobData.sites) {
        await evaluateSiteConditions(siteId, jobData.triggerTypes);
      }
      
      // Acknowledge message
      queueService.ack(message);
      
    } catch (error) {
      logger.error('Site evaluation failed:', error);
      
      // Reject and requeue with limit
      queueService.nack(message, false, true);
    }
  });
}
```

#### Notification Consumer

```typescript
export async function startNotificationConsumer(
  queue: NotificationQueue
): Promise<void> {
  const queueService = QueueService.getInstance();
  
  await queueService.assertQueue('notification-queue', { durable: true });
  
  await queueService.consume('notification-queue', async (message) => {
    if (!message) return;
    
    try {
      const jobData: NotificationJobData = JSON.parse(message.content.toString());
      
      // Render template
      const renderedContent = await renderTemplate(
        jobData.templateName,
        jobData.channelType,
        jobData.templateData
      );
      
      // Send notification
      const connector = ConnectorFactory.getConnector(jobData.channelType);
      await connector.send({
        recipient: jobData.recipient,
        subject: renderedContent.subject,
        content: renderedContent.body,
        metadata: jobData.templateData
      });
      
      // Log success
      await logNotificationStatus(jobData, 'sent');
      
      queueService.ack(message);
      
    } catch (error) {
      logger.error('Notification delivery failed:', error);
      
      // Log failure
      await logNotificationStatus(jobData, 'failed', error.message);
      
      queueService.nack(message, false, false); // Don't requeue
    }
  });
}
```

#### Queue Monitoring

#### Bull Board Integration

```typescript
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';

const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath('/admin/queues');

export const setupBullBoard = (app: Express): void => {
  const { addQueue } = createBullBoard({
    queues: [
      new BullMQAdapter(evaluationQueue.getQueue()),
      new BullMQAdapter(triggerQueue.getQueue()),
      new BullMQAdapter(notificationQueue.getQueue()),
      new BullMQAdapter(botQueue.getQueue()),
      new BullMQAdapter(observationQueue.getQueue())
    ],
    serverAdapter: serverAdapter
  });

  app.use('/admin/queues', serverAdapter.getRouter());
};
```

**Dashboard Features**:
- Real-time queue status and metrics
- Job details and execution logs
- Retry and failure management
- Performance analytics
- Manual job triggering

#### Custom Metrics

```typescript
// Queue health monitoring
export class QueueMetrics {
  static async getQueueStats(queueName: string) {
    const queue = getQueueInstance(queueName);
    
    return {
      waiting: await queue.getWaiting(),
      active: await queue.getActive(),
      completed: await queue.getCompleted(),
      failed: await queue.getFailed(),
      delayed: await queue.getDelayed(),
      
      // Performance metrics
      throughput: await this.calculateThroughput(queue),
      averageProcessingTime: await this.getAverageProcessingTime(queue),
      errorRate: await this.calculateErrorRate(queue)
    };
  }
  
  static async calculateThroughput(queue: Queue): Promise<number> {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const completedJobs = await queue.getJobs(['completed'], oneHourAgo);
    return completedJobs.length;
  }
}
```

#### Error Handling and Recovery

#### Retry Strategies

```typescript
// Exponential backoff with jitter
const retryConfig = {
  attempts: 5,
  backoff: {
    type: 'exponential',
    delay: 2000,
    settings: {
      jitter: true  // Add randomness to prevent thundering herd
    }
  }
};

// Custom retry logic
const customRetry = {
  attempts: 3,
  backoff: (attemptsMade: number) => {
    return Math.min(Math.pow(2, attemptsMade) * 1000, 30000);
  }
};
```

#### Dead Letter Queues

```typescript
// Configure dead letter queue for failed messages
await queueService.assertQueue('notification-queue', {
  durable: true,
  arguments: {
    'x-dead-letter-exchange': 'dlx-exchange',
    'x-dead-letter-routing-key': 'failed-notifications',
    'x-message-ttl': 3600000  // 1 hour TTL
  }
});

// Dead letter exchange setup
await queueService.assertExchange('dlx-exchange', 'direct', { durable: true });
await queueService.assertQueue('failed-notifications', { durable: true });
await queueService.bindQueue('failed-notifications', 'dlx-exchange', 'failed-notifications');
```

#### Circuit Breaker Pattern

```typescript
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  
  constructor(
    private threshold = 5,
    private timeout = 60000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime < this.timeout) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'HALF_OPEN';
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }
}
```

#### Performance Optimization

#### Connection Pooling

```typescript
// Redis connection pool for BullMQ
const redisPool = {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxConnections: 20,
  connectionIdleTimeout: 30000
};

// RabbitMQ channel pooling
class ChannelPool {
  private channels: Channel[] = [];
  private maxChannels = 10;
  
  async getChannel(): Promise<Channel> {
    if (this.channels.length > 0) {
      return this.channels.pop()!;
    }
    
    if (this.channels.length < this.maxChannels) {
      return await this.connection.createChannel();
    }
    
    // Wait for available channel
    return new Promise((resolve) => {
      const checkForChannel = () => {
        if (this.channels.length > 0) {
          resolve(this.channels.pop()!);
        } else {
          setTimeout(checkForChannel, 10);
        }
      };
      checkForChannel();
    });
  }
  
  releaseChannel(channel: Channel): void {
    this.channels.push(channel);
  }
}
```

#### Batch Processing

```typescript
// Batch job processing for efficiency
export class BatchProcessor {
  private batch: any[] = [];
  private batchSize = 100;
  private flushInterval = 5000; // 5 seconds
  
  constructor(private processor: (items: any[]) => Promise<void>) {
    setInterval(() => this.flush(), this.flushInterval);
  }
  
  add(item: any): void {
    this.batch.push(item);
    
    if (this.batch.length >= this.batchSize) {
      this.flush();
    }
  }
  
  private async flush(): Promise<void> {
    if (this.batch.length === 0) return;
    
    const itemsToProcess = [...this.batch];
    this.batch = [];
    
    try {
      await this.processor(itemsToProcess);
    } catch (error) {
      logger.error('Batch processing failed:', error);
      // Could implement retry logic here
    }
  }
}
```

#### Configuration Best Practices

#### Environment-Specific Settings

```typescript
// Development
const devConfig = {
  redis: {
    maxRetriesPerRequest: 1,
    connectTimeout: 5000
  },
  queues: {
    removeOnComplete: 10,
    removeOnFail: 10
  }
};

// Production
const prodConfig = {
  redis: {
    maxRetriesPerRequest: 3,
    connectTimeout: 10000,
    enableAutoPipelining: true
  },
  queues: {
    removeOnComplete: 100,
    removeOnFail: 50,
    attempts: 5
  }
};
```

#### Monitoring and Alerting

```typescript
// Queue health checks
export async function checkQueueHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: Record<string, any>;
}> {
  const queues = ['evaluation', 'trigger', 'notification', 'bot', 'observation'];
  const results = {};
  let overallStatus = 'healthy';
  
  for (const queueName of queues) {
    const stats = await QueueMetrics.getQueueStats(queueName);
    
    // Check for concerning metrics
    if (stats.failed > 100 || stats.waiting > 1000) {
      overallStatus = 'degraded';
    }
    
    if (stats.errorRate > 0.1) { // 10% error rate
      overallStatus = 'unhealthy';
    }
    
    results[queueName] = stats;
  }
  
  return { status: overallStatus, details: results };
}
```

*Source: [docs/QUEUES.md](docs/QUEUES.md)*


---

### 3.3 Template Engine

#### Overview

The notification engine's template system provides a powerful, flexible framework for creating and managing multi-channel notification templates using Handlebars.js with custom layouts and helpers.

#### Architecture

```
Template Engine
├── Template Registry      # Schema definitions and validation
├── Template Loader       # File system template loading
├── Layout System        # Base layouts for consistent branding
├── Custom Helpers       # Handlebars helper functions
├── Validation Layer     # Zod schema validation
└── Rendering Engine     # Template compilation and rendering
```

#### Template Registry

The template registry defines available templates, their schemas, and validation rules for each channel.

#### Registry Structure

```typescript
// src/template-registry.ts
export const templateRegistry = {
  'template-name': {
    email: {
      schema: z.object({
        // Email-specific validation schema
        subject: z.string().min(1),
        recipientName: z.string(),
        // ... other properties
      })
    },
    slack: {
      schema: z.object({
        // Slack-specific validation schema
        channelId: z.string(),
        blocks: z.array(z.object({
          type: z.string(),
          // ... block kit schema
        }))
      })
    },
    hubspot: {
      schema: z.object({
        // HubSpot CRM schema
        contactId: z.string(),
        properties: z.record(z.any())
      })
    }
  }
}
```

#### Template Examples

##### Weekly Summary Template
```typescript
'weekly-summary-recurring': {
  email: {
    schema: z.object({
      siteName: z.string(),
      startDate: z.string(),
      endDate: z.string(),
      metrics: z.object({
        sessions: z.number(),
        revenue: z.number().optional(),
        pageviews: z.number(),
        conversionRate: z.number().optional()
      }),
      topPages: z.array(z.object({
        url: z.string(),
        views: z.number(),
        conversions: z.number().optional()
      })),
      userName: z.string()
    })
  },
  slack: {
    schema: z.object({
      siteName: z.string(),
      metrics: z.object({
        sessions: z.number(),
        revenue: z.number().optional()
      }),
      channelId: z.string()
    })
  }
}
```

#### Template Engine Implementation

#### Core Engine Class

```typescript
export class TemplateEngine {
  registry: typeof templateRegistry;
  templates: Record<string, string> = {};
  layouts: Record<string, string> = {};

  constructor(
    private readonly templatesPath: string,
    private readonly layoutsPath?: string,
    registry = templateRegistry
  ) {
    this.registry = registry;
  }

  async init(): Promise<void> {
    // Register custom helpers for layout functionality
    this.registerLayoutHelpers();

    // Load layout templates first
    if (this.layoutsPath) {
      await this.loadLayouts();
    }

    // Load regular templates
    await this.loadTemplates();
    
    // Validate registry completeness
    await this.checkTemplateRegistryCompleteness();
  }

  /**
   * Render a template with provided data
   */
  render(templateName: string, channel: string, data: any): string {
    const template = this.getCompiledTemplate(templateName, channel);
    return template(data);
  }

  /**
   * Validate data against template schema
   */
  validateData(templateName: string, channel: string, data: any): boolean {
    const schema = this.getSchema(templateName, channel);
    const result = schema.safeParse(data);
    
    if (!result.success) {
      throw new Error(`Template validation failed: ${result.error.message}`);
    }
    
    return true;
  }
}
```

#### Layout System

The template engine supports layout inheritance using custom Handlebars helpers that mimic `handlebars-layouts` functionality.

##### Layout Helper Implementation

```typescript
private registerLayoutHelpers(): void {
  const blocks: Record<string, Record<string, string>> = {};

  // Helper to define content blocks
  Handlebars.registerHelper('content', function(this: any, name, options) {
    const data = Handlebars.createFrame(options.data);
    const blockName = name.trim();

    if (!blocks[data.layoutName]) {
      blocks[data.layoutName] = {};
    }

    // Store the block content
    blocks[data.layoutName][blockName] = options.fn(this);
    return null;
  });

  // Helper to retrieve content blocks
  Handlebars.registerHelper('block', function(this: any, name, options) {
    const data = options.data;
    const layoutName = data.layoutName;
    const blockName = name.trim();

    // Get block content or use default
    let content = '';
    if (blocks[layoutName] && blocks[layoutName][blockName]) {
      content = blocks[layoutName][blockName];
    } else if (options.fn) {
      content = options.fn(this);
    }

    return new Handlebars.SafeString(content);
  });

  // Helper to extend layouts
  Handlebars.registerHelper('extend', function(this: any, layoutName, options) {
    const data = Handlebars.createFrame(options.data);
    data.layoutName = layoutName;

    // Execute the child template to populate blocks
    options.fn(this, { data: data });

    // Render the layout with populated blocks
    const layout = this.layouts[layoutName];
    if (!layout) {
      throw new Error(`Layout '${layoutName}' not found`);
    }

    const template = Handlebars.compile(layout);
    return template(this, { data: data });
  });
}
```

#### Template Structure

#### Directory Organization

```
templates/
├── layouts/                    # Base layouts
│   ├── email.hbs              # Email HTML layout
│   ├── email-plain.hbs        # Plain text email layout
│   └── slack.hbs              # Slack message layout
├── new-templates/             # Current email templates
│   ├── credit-card.hbs
│   ├── weekly-summary-recurring.hbs
│   ├── login-case-study.hbs
│   └── ...
├── slack-templates/           # Slack-specific templates
│   ├── credit-card.hbs
│   ├── weekly-summary-recurring.hbs
│   └── ...
└── templating/               # Legacy templates
    └── ...
```

#### Template File Examples

##### Email Layout (layouts/email.hbs)
```handlebars
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{#block "title"}}Notification{{/block}}</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            border-bottom: 3px solid #007bff;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .footer {
            text-align: center;
            color: #666;
            font-size: 12px;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        {{#block "header"}}
            <h1>{{siteName}} Notification</h1>
        {{/block}}
    </div>
    
    <div class="content">
        {{#block "content"}}
            <p>Default content</p>
        {{/block}}
    </div>
    
    <div class="footer">
        {{#block "footer"}}
            <p>© 2025 Your Company. All rights reserved.</p>
            <p><a href="{{unsubscribeUrl}}">Unsubscribe</a></p>
        {{/block}}
    </div>
</body>
</html>
```

##### Email Template (new-templates/weekly-summary-recurring.hbs)
```handlebars
{{#extend "email"}}
  {{#content "title"}}Weekly Summary - {{siteName}}{{/content}}
  
  {{#content "header"}}
    <h1>📊 Weekly Summary for {{siteName}}</h1>
    <p>{{startDate}} - {{endDate}}</p>
  {{/content}}
  
  {{#content "content"}}
    <h2>Hey {{userName}}! 👋</h2>
    
    <p>Here's how {{siteName}} performed this week:</p>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>📈 Key Metrics</h3>
      <ul style="list-style: none; padding: 0;">
        <li><strong>Sessions:</strong> {{formatNumber metrics.sessions}}</li>
        <li><strong>Page Views:</strong> {{formatNumber metrics.pageviews}}</li>
        {{#if metrics.revenue}}
        <li><strong>Revenue:</strong> ${{formatCurrency metrics.revenue}}</li>
        {{/if}}
        {{#if metrics.conversionRate}}
        <li><strong>Conversion Rate:</strong> {{formatPercent metrics.conversionRate}}</li>
        {{/if}}
      </ul>
    </div>
    
    {{#if topPages}}
    <h3>🔥 Top Performing Pages</h3>
    <table style="width: 100%; border-collapse: collapse;">
      <thead>
        <tr style="background: #f8f9fa;">
          <th style="padding: 10px; text-align: left;">Page</th>
          <th style="padding: 10px; text-align: right;">Views</th>
          {{#if (hasProperty (first topPages) 'conversions')}}
          <th style="padding: 10px; text-align: right;">Conversions</th>
          {{/if}}
        </tr>
      </thead>
      <tbody>
        {{#each topPages}}
        <tr>
          <td style="padding: 10px; border-bottom: 1px solid #eee;">
            <a href="{{url}}" style="color: #007bff;">{{truncate url 50}}</a>
          </td>
          <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">
            {{formatNumber views}}
          </td>
          {{#if conversions}}
          <td style="padding: 10px; text-align: right; border-bottom: 1px solid #eee;">
            {{formatNumber conversions}}
          </td>
          {{/if}}
        </tr>
        {{/each}}
      </tbody>
    </table>
    {{/if}}
    
    <div style="margin: 30px 0; text-align: center;">
      <a href="{{dashboardUrl}}" class="btn">View Full Dashboard</a>
    </div>
    
    <p>Keep up the great work! 🚀</p>
  {{/content}}
{{/extend}}
```

##### Slack Template (slack-templates/weekly-summary-recurring.hbs)
```handlebars
{
  "blocks": [
    {
      "type": "header",
      "text": {
        "type": "plain_text",
        "text": "📊 Weekly Summary: {{siteName}}"
      }
    },
    {
      "type": "context",
      "elements": [
        {
          "type": "mrkdwn",
          "text": "{{startDate}} - {{endDate}}"
        }
      ]
    },
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "Hey {{userName}}! Here's how *{{siteName}}* performed this week:"
      }
    },
    {
      "type": "section",
      "fields": [
        {
          "type": "mrkdwn",
          "text": "*Sessions:*\n{{formatNumber metrics.sessions}}"
        },
        {
          "type": "mrkdwn",
          "text": "*Page Views:*\n{{formatNumber metrics.pageviews}}"
        }
        {{#if metrics.revenue}},
        {
          "type": "mrkdwn",
          "text": "*Revenue:*\n${{formatCurrency metrics.revenue}}"
        }
        {{/if}}
        {{#if metrics.conversionRate}},
        {
          "type": "mrkdwn",
          "text": "*Conversion Rate:*\n{{formatPercent metrics.conversionRate}}"
        }
        {{/if}}
      ]
    },
    {{#if topPages}}
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*🔥 Top Pages:*\n{{#each topPages}}• <{{url}}|{{truncate url 40}}> ({{formatNumber views}} views)\n{{/each}}"
      }
    },
    {{/if}}
    {
      "type": "actions",
      "elements": [
        {
          "type": "button",
          "text": {
            "type": "plain_text",
            "text": "View Dashboard"
          },
          "url": "{{dashboardUrl}}",
          "style": "primary"
        }
      ]
    }
  ]
}
```

#### Custom Handlebars Helpers

#### Formatting Helpers

```typescript
// Number formatting
Handlebars.registerHelper('formatNumber', function(value: number): string {
  if (typeof value !== 'number') return '0';
  return value.toLocaleString('en-US');
});

// Currency formatting
Handlebars.registerHelper('formatCurrency', function(value: number): string {
  if (typeof value !== 'number') return '0.00';
  return value.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
});

// Percentage formatting
Handlebars.registerHelper('formatPercent', function(value: number): string {
  if (typeof value !== 'number') return '0%';
  return (value * 100).toFixed(1) + '%';
});

// Date formatting
Handlebars.registerHelper('formatDate', function(date: string, format?: string): string {
  const d = new Date(date);
  if (format === 'short') {
    return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
  return d.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
});
```

#### Utility Helpers

```typescript
// String truncation
Handlebars.registerHelper('truncate', function(str: string, length: number): string {
  if (!str || str.length <= length) return str;
  return str.substring(0, length) + '...';
});

// Conditional helpers
Handlebars.registerHelper('gt', function(a: number, b: number): boolean {
  return a > b;
});

Handlebars.registerHelper('lt', function(a: number, b: number): boolean {
  return a < b;
});

Handlebars.registerHelper('eq', function(a: any, b: any): boolean {
  return a === b;
});

// Array helpers
Handlebars.registerHelper('first', function(array: any[]): any {
  return array && array.length > 0 ? array[0] : null;
});

Handlebars.registerHelper('last', function(array: any[]): any {
  return array && array.length > 0 ? array[array.length - 1] : null;
});

Handlebars.registerHelper('hasProperty', function(obj: any, property: string): boolean {
  return obj && obj.hasOwnProperty(property);
});
```

#### Business Logic Helpers

```typescript
// Site-specific helpers
Handlebars.registerHelper('getDashboardUrl', function(siteId: number): string {
  return `https://dashboard.yourapp.com/sites/${siteId}`;
});

Handlebars.registerHelper('getUnsubscribeUrl', function(siteId: number, email: string): string {
  const token = generateUnsubscribeToken(siteId, email);
  return `https://yourapp.com/unsubscribe?token=${token}`;
});

// Metric analysis helpers
Handlebars.registerHelper('getTrend', function(current: number, previous: number): string {
  if (!previous) return 'new';
  const change = ((current - previous) / previous) * 100;
  
  if (change > 10) return 'up-significant';
  if (change > 0) return 'up';
  if (change < -10) return 'down-significant';
  if (change < 0) return 'down';
  return 'stable';
});

Handlebars.registerHelper('getTrendIcon', function(trend: string): string {
  const icons = {
    'up-significant': '📈',
    'up': '⬆️',
    'down-significant': '📉',
    'down': '⬇️',
    'stable': '➡️',
    'new': '✨'
  };
  return icons[trend] || '➡️';
});
```

#### Template Validation

#### Schema Validation

```typescript
export function validateTemplateData(
  templateName: string,
  channel: string,
  data: any
): { valid: boolean; errors?: string[] } {
  const templateConfig = templateRegistry[templateName];
  
  if (!templateConfig) {
    return { valid: false, errors: [`Template '${templateName}' not found`] };
  }
  
  const channelConfig = templateConfig[channel];
  if (!channelConfig) {
    return { valid: false, errors: [`Channel '${channel}' not supported for template '${templateName}'`] };
  }
  
  const result = channelConfig.schema.safeParse(data);
  
  if (!result.success) {
    return {
      valid: false,
      errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
    };
  }
  
  return { valid: true };
}
```

#### Template Completeness Check

```typescript
async checkTemplateRegistryCompleteness(): Promise<void> {
  const registryTemplates = Object.keys(this.registry);
  const missingTemplates: string[] = [];
  
  for (const templateName of registryTemplates) {
    const templateConfig = this.registry[templateName];
    
    for (const channel of Object.keys(templateConfig)) {
      const expectedFileName = `${templateName}.hbs`;
      const channelPath = channel === 'email' ? this.templatesPath : 
                         channel === 'slack' ? this.templatesPath.replace('new-templates', 'slack-templates') :
                         this.templatesPath;
      
      const templatePath = path.join(channelPath, expectedFileName);
      
      try {
        await fs.access(templatePath);
      } catch (error) {
        missingTemplates.push(`${templateName}/${channel} (${templatePath})`);
      }
    }
  }
  
  if (missingTemplates.length > 0) {
    console.warn('Missing template files:', missingTemplates);
  } else {
    console.log('✅ All registry templates have corresponding files');
  }
}
```

#### Template Rendering Pipeline

#### Rendering Process

```typescript
export async function renderTemplate(
  templateName: string,
  channel: string,
  data: any
): Promise<{ subject?: string; body: string }> {
  // 1. Validate input data
  const validation = validateTemplateData(templateName, channel, data);
  if (!validation.valid) {
    throw new Error(`Template validation failed: ${validation.errors?.join(', ')}`);
  }
  
  // 2. Get template engine instance
  const engine = await getTemplateEngine();
  
  // 3. Prepare template data with helpers
  const templateData = {
    ...data,
    // Add global helpers/data
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    // Add any computed values
    ...computeAdditionalData(data)
  };
  
  // 4. Render template
  try {
    const rendered = engine.render(templateName, channel, templateData);
    
    // 5. Parse rendered content for subject/body
    if (channel === 'email') {
      return parseEmailTemplate(rendered);
    } else {
      return { body: rendered };
    }
  } catch (error) {
    logger.error('Template rendering failed', {
      templateName,
      channel,
      error: error.message
    });
    throw error;
  }
}

function parseEmailTemplate(rendered: string): { subject?: string; body: string } {
  // Extract subject from title tag if present
  const titleMatch = rendered.match(/<title>(.*?)<\/title>/);
  const subject = titleMatch ? titleMatch[1] : undefined;
  
  return { subject, body: rendered };
}
```

#### Template Caching

```typescript
class TemplateCache {
  private cache = new Map<string, HandlebarsTemplateDelegate>();
  private maxAge = 5 * 60 * 1000; // 5 minutes
  private timestamps = new Map<string, number>();
  
  get(key: string): HandlebarsTemplateDelegate | null {
    const template = this.cache.get(key);
    const timestamp = this.timestamps.get(key);
    
    if (!template || !timestamp) return null;
    
    // Check if cache entry is expired
    if (Date.now() - timestamp > this.maxAge) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      return null;
    }
    
    return template;
  }
  
  set(key: string, template: HandlebarsTemplateDelegate): void {
    this.cache.set(key, template);
    this.timestamps.set(key, Date.now());
  }
  
  clear(): void {
    this.cache.clear();
    this.timestamps.clear();
  }
}
```

#### Template Development Workflow

#### Development Setup

1. **Create template files**
   ```bash
   # Email template
   touch src/new-templates/my-new-template.hbs
   
   # Slack template
   touch src/slack-templates/my-new-template.hbs
   ```

2. **Register in template registry**
   ```typescript
   // Add to src/template-registry.ts
   'my-new-template': {
     email: {
       schema: z.object({
         userName: z.string(),
         // ... other properties
       })
     },
     slack: {
       schema: z.object({
         // ... slack properties
       })
     }
   }
   ```

3. **Test template rendering**
   ```typescript
   // Development testing
   const testData = {
     userName: 'John Doe',
     // ... test data
   };
   
   const rendered = await renderTemplate('my-new-template', 'email', testData);
   console.log(rendered);
   ```

#### Template Testing

```typescript
// Template test suite
describe('Template Rendering', () => {
  test('should render weekly summary email', async () => {
    const testData = {
      siteName: 'Test Site',
      userName: 'John Doe',
      startDate: '2025-06-01',
      endDate: '2025-06-08',
      metrics: {
        sessions: 1234,
        pageviews: 5678,
        revenue: 1234.56
      },
      topPages: [
        { url: '/home', views: 100, conversions: 5 },
        { url: '/product', views: 80, conversions: 8 }
      ]
    };
    
    const result = await renderTemplate('weekly-summary-recurring', 'email', testData);
    
    expect(result.body).toContain('Test Site');
    expect(result.body).toContain('John Doe');
    expect(result.body).toContain('1,234');
    expect(result.subject).toContain('Weekly Summary');
  });
  
  test('should validate template data', () => {
    const invalidData = { /* missing required fields */ };
    
    const validation = validateTemplateData('weekly-summary-recurring', 'email', invalidData);
    
    expect(validation.valid).toBe(false);
    expect(validation.errors).toBeDefined();
  });
});
```

#### Performance Considerations

#### Template Compilation

- Templates are compiled once and cached in memory
- Hot reloading in development mode
- Precompilation for production deployments

#### Memory Management

- Template cache with TTL to prevent memory leaks
- Lazy loading of templates
- Cleanup of unused templates

#### Optimization Tips

1. **Minimize template complexity** - Keep logic simple in templates
2. **Use helpers** - Move complex logic to Handlebars helpers
3. **Cache compiled templates** - Avoid recompilation
4. **Optimize images** - Use CDN for template assets
5. **Minify output** - Remove unnecessary whitespace in production

*Source: [docs/TEMPLATES.md](docs/TEMPLATES.md)*


---

## 4. Development Guide

Development, testing, and deployment guides

### 4.1 Testing Guide

This document provides comprehensive guidance for testing the Notification Engine System, including unit tests, integration tests, and end-to-end testing strategies.

#### Table of Contents

- [Testing Overview](#testing-overview)
- [Test Setup](#test-setup)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [End-to-End Testing](#end-to-end-testing)
- [Load Testing](#load-testing)
- [Testing Best Practices](#testing-best-practices)
- [Continuous Integration](#continuous-integration)

#### Testing Overview

The Notification Engine System uses a comprehensive testing approach with multiple testing layers:

- **Unit Tests**: Test individual functions, classes, and components in isolation
- **Integration Tests**: Test interactions between different components and external services
- **End-to-End Tests**: Test complete notification workflows from trigger to delivery
- **Load Tests**: Test system performance under various load conditions

#### Testing Stack

- **Jest**: Primary testing framework
- **Supertest**: HTTP integration testing
- **Testcontainers**: Docker-based integration testing
- **Mock Libraries**: Service mocking and stubbing
- **Factory Libraries**: Test data generation

#### Test Setup

#### Installation

```bash
#### Install testing dependencies
npm install --save-dev jest @types/jest supertest @types/supertest
npm install --save-dev testcontainers mysql2
npm install --save-dev jest-mock-extended factory.ts
```

#### Configuration

```javascript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/generated/**/*'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 30000
};
```

#### Test Environment Setup

```typescript
// tests/setup.ts
import { GenericContainer } from 'testcontainers';
import { PrismaClient } from '@prisma/client';

let prisma: PrismaClient;
let mysqlContainer: any;
let redisContainer: any;

beforeAll(async () => {
  // Start test databases
  mysqlContainer = await new GenericContainer('mysql:8.0')
    .withEnvironment({
      MYSQL_ROOT_PASSWORD: 'testpass',
      MYSQL_DATABASE: 'test_notifications'
    })
    .withExposedPorts(3306)
    .start();

  redisContainer = await new GenericContainer('redis:7')
    .withExposedPorts(6379)
    .start();

  // Setup Prisma client
  const DATABASE_URL = `mysql://root:testpass@localhost:${mysqlContainer.getMappedPort(3306)}/test_notifications`;
  process.env.DATABASE_URL = DATABASE_URL;
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = redisContainer.getMappedPort(6379).toString();

  prisma = new PrismaClient();
  await prisma.$connect();
});

afterAll(async () => {
  await prisma.$disconnect();
  await mysqlContainer.stop();
  await redisContainer.stop();
});
```

#### Unit Testing

#### Testing Services

```typescript
// tests/unit/services/trigger-evaluator.test.ts
import { TriggerEvaluator } from '../../../src/services/trigger-evaluator';
import { SiteState } from '../../../src/types/site';

describe('TriggerEvaluator', () => {
  let evaluator: TriggerEvaluator;

  beforeEach(() => {
    evaluator = new TriggerEvaluator();
  });

  describe('evaluateCondition', () => {
    test('should evaluate simple numeric conditions', () => {
      const condition = {
        field: 'sessions',
        operator: 'greater_than',
        value: 100
      };
      
      const siteState: SiteState = {
        idSite: 123,
        metrics: { sessions: 150 }
      };

      const result = evaluator.evaluateCondition(condition, siteState);
      expect(result).toBe(true);
    });

    test('should evaluate date-based conditions', () => {
      const condition = {
        field: 'last_login_date',
        operator: 'days_since',
        value: 7
      };
      
      const siteState: SiteState = {
        idSite: 123,
        lastLoginDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)
      };

      const result = evaluator.evaluateCondition(condition, siteState);
      expect(result).toBe(true);
    });

    test('should handle missing data gracefully', () => {
      const condition = {
        field: 'sessions',
        operator: 'greater_than',
        value: 100
      };
      
      const siteState: SiteState = {
        idSite: 123,
        metrics: {}
      };

      const result = evaluator.evaluateCondition(condition, siteState);
      expect(result).toBe(false);
    });
  });
});
```

#### Testing Template Engine

```typescript
// tests/unit/template-engine.test.ts
import { TemplateEngine } from '../../src/template-engine';
import { mockFs } from 'jest-mock-extended';

jest.mock('fs', () => mockFs);

describe('TemplateEngine', () => {
  let templateEngine: TemplateEngine;

  beforeEach(() => {
    templateEngine = new TemplateEngine();
  });

  test('should render email template with data', async () => {
    const templateContent = `
      <h1>Hello {{userName}}</h1>
      <p>Your site {{siteName}} has {{sessions}} sessions</p>
    `;
    
    mockFs.readFileSync.mockReturnValue(templateContent);

    const result = await templateEngine.render('weekly-summary', 'email', {
      userName: 'John Doe',
      siteName: 'Test Site',
      sessions: 1234
    });

    expect(result.body).toContain('Hello John Doe');
    expect(result.body).toContain('Test Site has 1,234 sessions');
  });

  test('should use custom helpers', async () => {
    const templateContent = `
      <p>Revenue: {{formatCurrency revenue}}</p>
      <p>Date: {{formatDate date}}</p>
    `;
    
    mockFs.readFileSync.mockReturnValue(templateContent);

    const result = await templateEngine.render('template', 'email', {
      revenue: 1234.56,
      date: new Date('2023-01-01')
    });

    expect(result.body).toContain('Revenue: $1,234.56');
    expect(result.body).toContain('Date: Jan 1, 2023');
  });
});
```

#### Testing Queue Jobs

```typescript
// tests/unit/jobs/notification-job.test.ts
import { NotificationJob } from '../../../src/jobs/notification/notification-job';
import { createMockContext } from '../../helpers/mock-context';

describe('NotificationJob', () => {
  let notificationJob: NotificationJob;
  let mockContext: any;

  beforeEach(() => {
    mockContext = createMockContext();
    notificationJob = new NotificationJob();
  });

  test('should process email notification', async () => {
    const jobData = {
      notificationId: 'test-123',
      channel: 'email',
      recipient: '<EMAIL>',
      templateId: 'weekly-summary',
      templateData: {
        siteName: 'Test Site',
        userName: 'John Doe'
      }
    };

    await notificationJob.process(jobData, mockContext);

    expect(mockContext.emailConnector.send).toHaveBeenCalledWith({
      to: '<EMAIL>',
      subject: expect.stringContaining('Weekly Summary'),
      html: expect.stringContaining('Test Site')
    });
  });

  test('should handle job failures gracefully', async () => {
    const jobData = {
      notificationId: 'test-123',
      channel: 'email',
      recipient: 'invalid-email',
      templateId: 'weekly-summary',
      templateData: {}
    };

    mockContext.emailConnector.send.mockRejectedValue(new Error('Invalid email'));

    await expect(notificationJob.process(jobData, mockContext)).rejects.toThrow('Invalid email');
    
    // Verify error was logged
    expect(mockContext.logger.error).toHaveBeenCalled();
  });
});
```

#### Integration Testing

#### API Integration Tests

```typescript
// tests/integration/api/trigger.test.ts
import request from 'supertest';
import app from '../../../src/app';
import { PrismaClient } from '@prisma/client';

describe('Trigger API', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up test data
    await prisma.notification.deleteMany();
    await prisma.trigger.deleteMany();
  });

  describe('POST /trigger/evaluation', () => {
    test('should trigger evaluation for specified sites', async () => {
      const response = await request(app)
        .post('/trigger/evaluation')
        .send({
          sites: [123, 456],
          immediate: true
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.evaluationId).toBeDefined();
      expect(response.body.sitesQueued).toBe(2);
    });

    test('should validate request body', async () => {
      const response = await request(app)
        .post('/trigger/evaluation')
        .send({
          // Missing required fields
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.details).toContain('sites');
    });
  });

  describe('POST /real-time', () => {
    test('should process real-time notification', async () => {
      // Create test trigger
      const trigger = await prisma.trigger.create({
        data: {
          name: 'test-trigger',
          templateId: 'weekly-summary',
          channels: ['email'],
          conditions: [],
          isActive: true
        }
      });

      const response = await request(app)
        .post('/real-time')
        .send({
          idSite: 123,
          triggerName: 'test-trigger',
          templateData: {
            siteName: 'Test Site',
            userName: 'John Doe'
          },
          channels: ['email']
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.notificationId).toBeDefined();
    });
  });
});
```

#### Database Integration Tests

```typescript
// tests/integration/database/models.test.ts
import { PrismaClient } from '@prisma/client';
import { createTestNotification, createTestTrigger } from '../../helpers/factories';

describe('Database Models', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('Notification Model', () => {
    test('should create notification with all relationships', async () => {
      const trigger = await createTestTrigger(prisma);
      const notification = await createTestNotification(prisma, {
        triggerId: trigger.id
      });

      const retrieved = await prisma.notification.findUnique({
        where: { id: notification.id },
        include: {
          trigger: true,
          attempts: true,
          auditLogs: true
        }
      });

      expect(retrieved).toBeDefined();
      expect(retrieved?.trigger.id).toBe(trigger.id);
      expect(retrieved?.status).toBe('PENDING');
    });

    test('should handle soft deletes', async () => {
      const notification = await createTestNotification(prisma);
      
      await prisma.notification.update({
        where: { id: notification.id },
        data: { deletedAt: new Date() }
      });

      const activeNotifications = await prisma.notification.findMany({
        where: { deletedAt: null }
      });

      expect(activeNotifications).not.toContainEqual(
        expect.objectContaining({ id: notification.id })
      );
    });
  });
});
```

#### End-to-End Testing

#### Complete Notification Flow

```typescript
// tests/e2e/notification-flow.test.ts
import { startTestServer } from '../helpers/test-server';
import { EmailTestHelper } from '../helpers/email-test-helper';
import { QueueTestHelper } from '../helpers/queue-test-helper';

describe('Complete Notification Flow', () => {
  let server: any;
  let emailHelper: EmailTestHelper;
  let queueHelper: QueueTestHelper;

  beforeAll(async () => {
    server = await startTestServer();
    emailHelper = new EmailTestHelper();
    queueHelper = new QueueTestHelper();
  });

  afterAll(async () => {
    await server.close();
  });

  test('should send weekly summary email', async () => {
    // Setup test data
    const siteId = 123;
    const userEmail = '<EMAIL>';
    
    // Trigger evaluation
    const response = await fetch(`${server.url}/trigger/evaluation`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sites: [siteId],
        immediate: true,
        triggerName: 'weekly-summary'
      })
    });

    expect(response.ok).toBe(true);

    // Wait for queue processing
    await queueHelper.waitForJobCompletion('evaluation-queue');
    await queueHelper.waitForJobCompletion('notification-queue');

    // Verify email was sent
    const sentEmails = await emailHelper.getSentEmails();
    const weeklyEmail = sentEmails.find(email => 
      email.to === userEmail && 
      email.subject.includes('Weekly Summary')
    );

    expect(weeklyEmail).toBeDefined();
    expect(weeklyEmail?.html).toContain('Test Site');
    expect(weeklyEmail?.html).toContain('metrics');
  });

  test('should handle Slack notifications', async () => {
    const slackHelper = new SlackTestHelper();
    
    // Trigger Slack notification
    const response = await fetch(`${server.url}/real-time`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        idSite: 123,
        triggerName: 'installation-complete',
        channels: ['slack'],
        templateData: {
          siteName: 'Test Site',
          userName: 'John Doe'
        }
      })
    });

    expect(response.ok).toBe(true);

    // Wait for processing
    await queueHelper.waitForJobCompletion('notification-queue');

    // Verify Slack message
    const slackMessages = await slackHelper.getPostedMessages();
    expect(slackMessages).toHaveLength(1);
    expect(slackMessages[0].text).toContain('Installation Complete');
  });
});
```

#### Load Testing

#### Queue Performance Tests

```typescript
// tests/load/queue-performance.test.ts
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { EvaluationQueue } from '../../src/jobs/evaluation/evaluation-queue';

describe('Queue Load Testing', () => {
  let evaluationQueue: EvaluationQueue;

  beforeAll(async () => {
    evaluationQueue = EvaluationQueue.getInstance();
  });

  test('should handle high volume of jobs', async () => {
    const jobCount = 1000;
    const jobs = [];

    // Add many jobs simultaneously
    for (let i = 0; i < jobCount; i++) {
      jobs.push(evaluationQueue.add(`test-job-${i}`, {
        siteId: i,
        triggerName: 'test-trigger'
      }));
    }

    const startTime = Date.now();
    await Promise.all(jobs);
    const addTime = Date.now() - startTime;

    // Wait for processing
    const processStartTime = Date.now();
    await evaluationQueue.whenCurrentJobsFinished();
    const processTime = Date.now() - processStartTime;

    // Performance assertions
    expect(addTime).toBeLessThan(5000); // Should add 1000 jobs in under 5 seconds
    expect(processTime).toBeLessThan(30000); // Should process in under 30 seconds

    console.log(`Added ${jobCount} jobs in ${addTime}ms`);
    console.log(`Processed ${jobCount} jobs in ${processTime}ms`);
  });
});
```

#### API Load Tests

```typescript
// tests/load/api-performance.test.ts
import autocannon from 'autocannon';
import { startTestServer } from '../helpers/test-server';

describe('API Load Testing', () => {
  let server: any;

  beforeAll(async () => {
    server = await startTestServer();
  });

  afterAll(async () => {
    await server.close();
  });

  test('should handle concurrent trigger evaluations', async () => {
    const result = await autocannon({
      url: `${server.url}/trigger/evaluation`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        sites: [123],
        immediate: true
      }),
      connections: 10,
      duration: 30 // 30 seconds
    });

    // Performance assertions
    expect(result.errors).toBe(0);
    expect(result.timeouts).toBe(0);
    expect(result.non2xx).toBe(0);
    expect(result.latency.average).toBeLessThan(1000); // Average response under 1 second

    console.log(`Requests per second: ${result.requests.average}`);
    console.log(`Average latency: ${result.latency.average}ms`);
  });
});
```

#### Testing Best Practices

#### Test Organization

```typescript
// tests/helpers/factories.ts
import { PrismaClient } from '@prisma/client';

export const createTestTrigger = async (prisma: PrismaClient, overrides = {}) => {
  return prisma.trigger.create({
    data: {
      name: 'test-trigger',
      templateId: 'weekly-summary',
      channels: ['email'],
      conditions: [],
      isActive: true,
      ...overrides
    }
  });
};

export const createTestNotification = async (prisma: PrismaClient, overrides = {}) => {
  return prisma.notification.create({
    data: {
      idSite: 123,
      templateId: 'weekly-summary',
      channel: 'email',
      recipient: '<EMAIL>',
      status: 'PENDING',
      templateData: {},
      ...overrides
    }
  });
};
```

#### Mock Helpers

```typescript
// tests/helpers/mock-context.ts
export const createMockContext = () => ({
  emailConnector: {
    send: jest.fn().mockResolvedValue({ messageId: 'test-123' })
  },
  slackConnector: {
    postMessage: jest.fn().mockResolvedValue({ ok: true })
  },
  templateEngine: {
    render: jest.fn().mockResolvedValue({
      subject: 'Test Subject',
      body: 'Test Body'
    })
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
});
```

#### Test Utilities

```typescript
// tests/helpers/test-server.ts
import app from '../../src/app';
import { Server } from 'http';

export const startTestServer = async (port = 0): Promise<{
  server: Server;
  url: string;
  close: () => Promise<void>;
}> => {
  return new Promise((resolve) => {
    const server = app.listen(port, () => {
      const address = server.address();
      const actualPort = typeof address === 'object' ? address?.port : port;
      
      resolve({
        server,
        url: `http://localhost:${actualPort}`,
        close: () => new Promise(resolve => server.close(resolve))
      });
    });
  });
};
```

#### Continuous Integration

#### GitHub Actions Configuration

```yaml
#### .github/workflows/test.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test_notifications
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      
      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Generate Prisma Client
      run: npx prisma generate
    
    - name: Run database migrations
      run: npx prisma migrate deploy
      env:
        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications
    
    - name: Run unit tests
      run: npm run test:unit
      env:
        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications
        REDIS_HOST: localhost
        REDIS_PORT: 6379
    
    - name: Run integration tests
      run: npm run test:integration
      env:
        DATABASE_URL: mysql://root:password@localhost:3306/test_notifications
        REDIS_HOST: localhost
        REDIS_PORT: 6379
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

#### Test Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest tests/unit",
    "test:integration": "jest tests/integration",
    "test:e2e": "jest tests/e2e",
    "test:load": "jest tests/load",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

#### Coverage Requirements

```javascript
// jest.config.js
module.exports = {
  // ... other configuration
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/services/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  }
};
```

This comprehensive testing guide provides the foundation for maintaining high code quality and system reliability in the Notification Engine System. Follow these patterns and adapt them to your specific testing needs.

*Source: [docs/TESTING.md](docs/TESTING.md)*


---

### 4.2 Deployment Guide

This document provides comprehensive deployment instructions for the Notification Engine System across different environments and platforms.

#### Table of Contents

- [Deployment Overview](#deployment-overview)
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Docker Deployment](#docker-deployment)
- [Production Deployment](#production-deployment)
- [AWS Deployment](#aws-deployment)
- [Monitoring and Health Checks](#monitoring-and-health-checks)
- [Scaling Considerations](#scaling-considerations)
- [Troubleshooting](#troubleshooting)

#### Deployment Overview

The Notification Engine System can be deployed in several ways:

- **Local Development**: Using Docker Compose for development and testing
- **Staging Environment**: Containerized deployment with external databases
- **Production**: Multi-instance deployment with load balancing and monitoring
- **Cloud Deployment**: AWS, Google Cloud, or Azure with managed services

#### Architecture Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   App Instances │    │   Databases     │
│                 │    │                 │    │                 │
│ • Nginx/ALB     │───▶│ • Node.js Apps  │───▶│ • MySQL RDS     │
│ • SSL/TLS       │    │ • Docker        │    │ • Redis Cluster │
│ • Health Checks │    │ • Auto Scaling  │    │ • RabbitMQ      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### Prerequisites

#### System Requirements

- **Node.js**: Version 18+ LTS
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **MySQL**: Version 8.0+
- **Redis**: Version 6.0+
- **RabbitMQ**: Version 3.8+

#### Minimum Hardware

**Development:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 10GB

**Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 50GB+ (with monitoring)

#### Environment Setup

#### Environment Variables

Create environment files for each deployment stage:

```bash
#### .env.development
NODE_ENV=development
PORT=3000
BUILD_TARGET=dev
LOGGER_TOGGLE=true

#### Database
DATABASE_URL=mysql://user:password@localhost:3306/notifications_dev

#### Redis
REDIS_HOST=localhost
REDIS_PORT=6379

#### RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

#### Email
EMAIL_ON=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
```

```bash
#### .env.staging
NODE_ENV=staging
PORT=3000
BUILD_TARGET=prod
LOGGER_TOGGLE=true

#### Database (use staging database)
DATABASE_URL=mysql://user:password@staging-db:3306/notifications_staging

#### External Redis/RabbitMQ
REDIS_HOST=staging-redis.cache.amazonaws.com
RABBITMQ_HOST=staging-rabbitmq.domain.com

#### Email (use test SMTP)
EMAIL_ON=true
SMTP_HOST=smtp.mailtrap.io
```

```bash
#### .env.production
NODE_ENV=production
PORT=3000
BUILD_TARGET=prod
LOGGER_TOGGLE=false

#### Production databases
DATABASE_URL=mysql://user:password@prod-db-cluster:3306/notifications
REDIS_HOST=prod-redis-cluster.cache.amazonaws.com
RABBITMQ_HOST=prod-rabbitmq-cluster.domain.com

#### Production email
EMAIL_ON=true
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=${SENDGRID_API_KEY}

#### Security
ENCRYPTION_KEY=${ENCRYPTION_KEY}
JWT_SECRET=${JWT_SECRET}
```

#### Secrets Management

```bash
#### Use AWS Secrets Manager, Azure Key Vault, or similar
aws secretsmanager create-secret \
  --name "notification-engine/production" \
  --description "Production secrets for Notification Engine" \
  --secret-string '{
    "DATABASE_PASSWORD": "secure-password",
    "SMTP_PASSWORD": "sendgrid-api-key",
    "ENCRYPTION_KEY": "32-char-encryption-key",
    "SLACK_CLIENT_SECRET": "slack-client-secret"
  }'
```

#### Docker Deployment

#### Single Container Deployment

```dockerfile
#### Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npx prisma generate
RUN npm run build

FROM node:18-alpine as production

WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/package*.json ./

#### Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

EXPOSE 3000
CMD ["node", "dist/server.js"]
```

#### Docker Compose Development

```yaml
#### docker-compose.dev.yml
version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mysql
      - redis
      - rabbitmq
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: notifications
      MYSQL_USER: notif_user
      MYSQL_PASSWORD: notif_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
```

#### Docker Compose Production

```yaml
#### docker-compose.prod.yml
version: '3.8'

services:
  app:
    image: notification-engine:latest
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        max_attempts: 3
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
    ports:
      - "3000-3002:3000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - app
    restart: unless-stopped
```

#### Production Deployment

#### Build Process

```bash
#!/bin/bash
#### build.sh

set -e

echo "Building Notification Engine..."

#### Install dependencies
npm ci --only=production

#### Generate Prisma client
npx prisma generate

#### Build TypeScript
npm run build

#### Create production image
docker build -t notification-engine:${BUILD_VERSION} .
docker tag notification-engine:${BUILD_VERSION} notification-engine:latest

echo "Build completed successfully"
```

#### Database Migration

```bash
#!/bin/bash
#### migrate.sh

set -e

echo "Running database migrations..."

#### Backup database
mysqldump -h ${DB_HOST} -u ${DB_USER} -p${DB_PASSWORD} notifications > backup_$(date +%Y%m%d_%H%M%S).sql

#### Run migrations
npx prisma migrate deploy

#### Verify migration
npx prisma migrate status

echo "Migrations completed successfully"
```

#### Deployment Script

```bash
#!/bin/bash
#### deploy.sh

set -e

BUILD_VERSION=${1:-latest}
ENVIRONMENT=${2:-production}

echo "Deploying Notification Engine v${BUILD_VERSION} to ${ENVIRONMENT}..."

#### Pull latest code
git pull origin main

#### Build application
./scripts/build.sh

#### Run database migrations
./scripts/migrate.sh

#### Stop existing containers
docker-compose -f docker-compose.${ENVIRONMENT}.yml down

#### Start new deployment
docker-compose -f docker-compose.${ENVIRONMENT}.yml up -d

#### Health check
sleep 30
./scripts/health-check.sh

echo "Deployment completed successfully"
```

#### Health Check Script

```bash
#!/bin/bash
#### health-check.sh

set -e

BASE_URL=${1:-http://localhost:3000}
MAX_ATTEMPTS=10
ATTEMPT=1

echo "Performing health checks..."

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
  echo "Attempt $ATTEMPT/$MAX_ATTEMPTS"
  
  # Basic health check
  if curl -f -s "${BASE_URL}/up" > /dev/null; then
    echo "✓ Basic health check passed"
  else
    echo "✗ Basic health check failed"
    exit 1
  fi
  
  # Queue health check
  if curl -f -s "${BASE_URL}/admin/queues" > /dev/null; then
    echo "✓ Queue health check passed"
  else
    echo "✗ Queue health check failed"
    exit 1
  fi
  
  # Database connectivity
  if curl -f -s "${BASE_URL}/health/database" > /dev/null; then
    echo "✓ Database connectivity check passed"
    break
  else
    echo "✗ Database connectivity check failed"
    if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
      exit 1
    fi
  fi
  
  ATTEMPT=$((ATTEMPT + 1))
  sleep 10
done

echo "All health checks passed!"
```

#### AWS Deployment

#### ECS Deployment

```json
// task-definition.json
{
  "family": "notification-engine",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/notificationEngineTaskRole",
  "containerDefinitions": [
    {
      "name": "notification-engine",
      "image": "ACCOUNT.dkr.ecr.REGION.amazonaws.com/notification-engine:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "PORT",
          "value": "3000"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:REGION:ACCOUNT:secret:notification-engine/production:DATABASE_URL::"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/notification-engine",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3000/up || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

#### CloudFormation Template

```yaml
#### infrastructure.yml
AWSTemplateFormatVersion: '2010-09-09'
Description: 'Notification Engine Infrastructure'

Parameters:
  Environment:
    Type: String
    Default: production
    AllowedValues: [development, staging, production]

Resources:
  # VPC and Networking
  VPC:
    Type: AWS::EC2::VPC
    Properties:
      CidrBlock: 10.0.0.0/16
      EnableDnsHostnames: true
      EnableDnsSupport: true
      Tags:
        - Key: Name
          Value: !Sub ${Environment}-notification-engine-vpc

  # RDS MySQL Instance
  DatabaseSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupDescription: Subnet group for RDS database
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2

  Database:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceIdentifier: !Sub ${Environment}-notification-engine-db
      DBInstanceClass: db.t3.medium
      Engine: mysql
      EngineVersion: '8.0'
      AllocatedStorage: 100
      StorageType: gp2
      DBName: notifications
      MasterUsername: admin
      MasterUserPassword: !Ref DatabasePassword
      VPCSecurityGroups:
        - !Ref DatabaseSecurityGroup
      DBSubnetGroupName: !Ref DatabaseSubnetGroup
      BackupRetentionPeriod: 7
      MultiAZ: !If [IsProduction, true, false]
      StorageEncrypted: true

  # ElastiCache Redis
  RedisSubnetGroup:
    Type: AWS::ElastiCache::SubnetGroup
    Properties:
      Description: Subnet group for Redis cluster
      SubnetIds:
        - !Ref PrivateSubnet1
        - !Ref PrivateSubnet2

  RedisCluster:
    Type: AWS::ElastiCache::ReplicationGroup
    Properties:
      ReplicationGroupId: !Sub ${Environment}-notification-engine-redis
      Description: Redis cluster for notification engine
      NumCacheClusters: 2
      Engine: redis
      CacheNodeType: cache.t3.medium
      CacheSubnetGroupName: !Ref RedisSubnetGroup
      SecurityGroupIds:
        - !Ref RedisSecurityGroup
      AtRestEncryptionEnabled: true
      TransitEncryptionEnabled: true

  # ECS Cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Sub ${Environment}-notification-engine

  # Application Load Balancer
  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub ${Environment}-notification-engine-alb
      Scheme: internet-facing
      SecurityGroups:
        - !Ref LoadBalancerSecurityGroup
      Subnets:
        - !Ref PublicSubnet1
        - !Ref PublicSubnet2

Conditions:
  IsProduction: !Equals [!Ref Environment, production]

Outputs:
  DatabaseEndpoint:
    Description: RDS instance endpoint
    Value: !GetAtt Database.Endpoint.Address
    Export:
      Name: !Sub ${Environment}-db-endpoint

  RedisEndpoint:
    Description: Redis cluster endpoint
    Value: !GetAtt RedisCluster.RedisEndpoint.Address
    Export:
      Name: !Sub ${Environment}-redis-endpoint
```

#### Terraform Configuration

```hcl
#### main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

#### VPC
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "${var.environment}-notification-engine"
  cidr = "10.0.0.0/16"

  azs             = ["${var.aws_region}a", "${var.aws_region}b"]
  private_subnets = ["********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24"]

  enable_nat_gateway = true
  enable_vpn_gateway = true

  tags = {
    Environment = var.environment
    Project     = "notification-engine"
  }
}

#### RDS MySQL
resource "aws_db_instance" "mysql" {
  identifier     = "${var.environment}-notification-engine"
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = var.db_instance_class
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_encrypted     = true
  
  db_name  = "notifications"
  username = var.db_username
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.default.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = var.environment != "production"
  
  tags = {
    Environment = var.environment
    Project     = "notification-engine"
  }
}

#### ElastiCache Redis
resource "aws_elasticache_replication_group" "redis" {
  replication_group_id       = "${var.environment}-notification-engine"
  description                = "Redis cluster for notification engine"
  
  port               = 6379
  parameter_group_name = "default.redis7"
  node_type          = var.redis_node_type
  num_cache_clusters = 2
  
  subnet_group_name  = aws_elasticache_subnet_group.default.name
  security_group_ids = [aws_security_group.redis.id]
  
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  
  tags = {
    Environment = var.environment
    Project     = "notification-engine"
  }
}

#### ECS Service
resource "aws_ecs_service" "notification_engine" {
  name            = "notification-engine"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.notification_engine.arn
  desired_count   = var.app_count

  launch_type = "FARGATE"

  network_configuration {
    security_groups  = [aws_security_group.ecs_tasks.id]
    subnets          = module.vpc.private_subnets
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.arn
    container_name   = "notification-engine"
    container_port   = 3000
  }

  depends_on = [aws_lb_listener.app]
}
```

#### Monitoring and Health Checks

#### Application Health Endpoints

```typescript
// src/routes/health.ts
import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import RedisClient from '../services/redis';
import { QueueService } from '../messaging/queue-service';

const router = Router();
const prisma = new PrismaClient();

// Basic health check
router.get('/up', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Detailed health check
router.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      database: 'unknown',
      redis: 'unknown',
      rabbitmq: 'unknown'
    }
  };

  try {
    // Database check
    await prisma.$queryRaw`SELECT 1`;
    health.services.database = 'ok';
  } catch (error) {
    health.services.database = 'error';
    health.status = 'error';
  }

  try {
    // Redis check
    const redis = RedisClient.getInstance();
    await redis.ping();
    health.services.redis = 'ok';
  } catch (error) {
    health.services.redis = 'error';
    health.status = 'error';
  }

  try {
    // RabbitMQ check
    const queueService = QueueService.getInstance();
    await queueService.checkConnection();
    health.services.rabbitmq = 'ok';
  } catch (error) {
    health.services.rabbitmq = 'error';
    health.status = 'error';
  }

  const statusCode = health.status === 'ok' ? 200 : 503;
  res.status(statusCode).json(health);
});

// Queue metrics
router.get('/metrics/queues', async (req, res) => {
  try {
    const queueService = QueueService.getInstance();
    const metrics = await queueService.getMetrics();
    
    res.json({
      timestamp: new Date().toISOString(),
      queues: metrics
    });
  } catch (error) {
    res.status(500).json({ error: 'Failed to get queue metrics' });
  }
});

export default router;
```

#### Prometheus Metrics

```typescript
// src/middleware/metrics.ts
import { Request, Response, NextFunction } from 'express';
import client from 'prom-client';

// Create metrics
const httpRequestDuration = new client.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const httpRequestTotal = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const queueJobsProcessed = new client.Counter({
  name: 'queue_jobs_processed_total',
  help: 'Total number of queue jobs processed',
  labelNames: ['queue_name', 'status']
});

// Middleware
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode.toString())
      .observe(duration);
    
    httpRequestTotal
      .labels(req.method, route, res.statusCode.toString())
      .inc();
  });
  
  next();
};

// Metrics endpoint
export const metricsHandler = async (req: Request, res: Response) => {
  res.set('Content-Type', client.register.contentType);
  res.end(await client.register.metrics());
};

export { queueJobsProcessed };
```

#### Monitoring Stack

```yaml
#### monitoring/docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning

  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml

volumes:
  prometheus_data:
  grafana_data:
```

#### Scaling Considerations

#### Horizontal Scaling

```yaml
#### kubernetes/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-engine
  labels:
    app: notification-engine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: notification-engine
  template:
    metadata:
      labels:
        app: notification-engine
    spec:
      containers:
      - name: notification-engine
        image: notification-engine:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        readinessProbe:
          httpGet:
            path: /up
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: notification-engine-service
spec:
  selector:
    app: notification-engine
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: notification-engine-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: notification-engine
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

#### Database Scaling

```sql
-- Read replicas setup
CREATE USER 'readonly'@'%' IDENTIFIED BY 'secure_password';
GRANT SELECT ON notifications.* TO 'readonly'@'%';

-- Connection pooling configuration
-- In application code:
const readOnlyPool = mysql.createPool({
  host: 'read-replica-endpoint',
  user: 'readonly',
  password: 'secure_password',
  database: 'notifications',
  connectionLimit: 10
});
```

#### Troubleshooting

#### Common Issues

**1. Database Connection Issues**
```bash
#### Check database connectivity
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD -e "SELECT 1;"

#### Check connection pool
curl http://localhost:3000/health/database
```

**2. Queue Processing Issues**
```bash
#### Check queue status
curl http://localhost:3000/admin/queues

#### Check Redis connectivity
redis-cli -h $REDIS_HOST ping

#### Check RabbitMQ
rabbitmqctl status
```

**3. Memory Issues**
```bash
#### Monitor memory usage
docker stats notification-engine

#### Check for memory leaks
curl http://localhost:3000/metrics | grep nodejs_heap
```

#### Log Analysis

```bash
#### Application logs
docker logs notification-engine --follow

#### Structured log queries
docker logs notification-engine 2>&1 | jq '.level == "error"'

#### Performance logs
docker logs notification-engine 2>&1 | jq '.responseTime > 1000'
```

#### Emergency Procedures

**1. Immediate Scaling**
```bash
#### Scale up immediately
docker-compose up --scale app=5

#### Kubernetes scaling
kubectl scale deployment notification-engine --replicas=10
```

**2. Circuit Breaker**
```bash
#### Disable email sending
curl -X POST http://localhost:3000/admin/toggle-email -d '{"enabled": false}'

#### Pause queue processing
curl -X POST http://localhost:3000/admin/pause-queues
```

This deployment guide provides comprehensive instructions for deploying the Notification Engine System across various environments with proper monitoring, scaling, and troubleshooting procedures.

*Source: [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)*


---

## 5. Troubleshooting

Common issues and solutions

### 5.1 Troubleshooting Guide

This document provides solutions to common issues, debugging techniques, and frequently asked questions for the Notification Engine System.

#### Table of Contents

- [Quick Diagnostics](#quick-diagnostics)
- [Common Issues](#common-issues)
- [Database Issues](#database-issues)
- [Queue System Issues](#queue-system-issues)
- [Template Issues](#template-issues)
- [API Issues](#api-issues)
- [Performance Issues](#performance-issues)
- [Security Issues](#security-issues)
- [Monitoring & Debugging](#monitoring--debugging)
- [FAQ](#frequently-asked-questions)

#### Quick Diagnostics

#### Health Check Commands

```bash
#### Basic application health
curl http://localhost:3000/up

#### Detailed health check
curl http://localhost:3000/health

#### Queue status
curl http://localhost:3000/admin/queues

#### Database connectivity
curl http://localhost:3000/health/database
```

#### Log Analysis

```bash
#### Real-time application logs
docker logs notification-engine --follow

#### Filter error logs
docker logs notification-engine 2>&1 | grep "ERROR"

#### Structured log queries with jq
docker logs notification-engine 2>&1 | jq '.level == "error"'

#### Performance monitoring
docker logs notification-engine 2>&1 | jq '.responseTime > 1000'
```

#### Resource Monitoring

```bash
#### Container resource usage
docker stats notification-engine

#### Memory usage breakdown
docker exec notification-engine node -e "console.log(process.memoryUsage())"

#### CPU and memory limits
docker inspect notification-engine | jq '.[0].HostConfig'
```

#### Common Issues

#### 1. Application Won't Start

**Symptoms:**
- Container exits immediately
- "Connection refused" errors
- Environment validation failures

**Diagnostics:**
```bash
#### Check container logs
docker logs notification-engine

#### Verify environment variables
docker exec notification-engine env | grep -E "(DATABASE_URL|REDIS_HOST|RABBITMQ_HOST)"

#### Test database connection
docker exec notification-engine npx prisma db push --dry-run
```

**Solutions:**

```bash
#### Fix environment variables
cp env.sample .env
#### Edit .env with proper values

#### Test database connectivity
mysql -h localhost -u root -p -e "SELECT 1;"

#### Regenerate Prisma client
docker exec notification-engine npx prisma generate

#### Reset database if needed
docker exec notification-engine npx prisma migrate reset
```

#### 2. High Memory Usage

**Symptoms:**
- Container getting killed (OOMKilled)
- Slow response times
- Memory warnings in logs

**Diagnostics:**
```bash
#### Check memory usage patterns
docker stats notification-engine --no-stream

#### Analyze heap dump (if available)
docker exec notification-engine node --inspect-brk=0.0.0.0:9229 dist/server.js

#### Memory leak detection
curl http://localhost:3000/metrics | grep nodejs_heap
```

**Solutions:**

```javascript
// Add to server.ts - Memory monitoring
setInterval(() => {
  const usage = process.memoryUsage();
  if (usage.heapUsed > 500 * 1024 * 1024) { // 500MB threshold
    console.warn('High memory usage:', usage);
  }
}, 30000);

// Force garbage collection periodically
if (global.gc) {
  setInterval(() => {
    global.gc();
  }, 60000);
}
```

```yaml
#### Docker memory limits
services:
  app:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

#### 3. Slow API Response Times

**Symptoms:**
- API calls taking > 5 seconds
- Timeout errors
- Queue processing delays

**Diagnostics:**
```bash
#### API response time testing
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3000/trigger/evaluation

#### Database query analysis
docker exec mysql mysql -u root -p -e "SHOW PROCESSLIST;"
docker exec mysql mysql -u root -p -e "SHOW ENGINE INNODB STATUS\G"

#### Queue processing times
curl http://localhost:3000/admin/queues | jq '.queues[].processing_time'
```

**Solutions:**

```sql
-- Add database indexes
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_triggers_active ON triggers(is_active);

-- Optimize queries
ANALYZE TABLE notifications;
OPTIMIZE TABLE notifications;
```

```typescript
// Add query optimization
const notifications = await prisma.notification.findMany({
  where: { status: 'PENDING' },
  select: { id: true, recipient: true }, // Only select needed fields
  take: 100, // Limit results
  orderBy: { createdAt: 'asc' }
});
```

#### Database Issues

#### Connection Pool Exhaustion

**Symptoms:**
- "Too many connections" errors
- Slow database queries
- Connection timeout errors

**Diagnostics:**
```sql
-- Check current connections
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- Check connection pool settings
SHOW VARIABLES LIKE 'max_connections';
```

**Solutions:**

```typescript
// Optimize Prisma connection pool
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  log: ['query', 'info', 'warn', 'error'],
});

// Connection pool configuration
DATABASE_URL="mysql://user:password@localhost:3306/db?connection_limit=10&pool_timeout=20"
```

```sql
-- Increase MySQL connection limit
SET GLOBAL max_connections = 500;

-- Add to my.cnf
[mysqld]
max_connections = 500
innodb_buffer_pool_size = 1G
```

#### Migration Issues

**Symptoms:**
- Schema drift warnings
- Migration conflicts
- Data loss during migrations

**Diagnostics:**
```bash
#### Check migration status
npx prisma migrate status

#### View pending migrations
npx prisma migrate diff --from-schema-datamodel prisma/schema.prisma

#### Database schema inspection
npx prisma db pull
```

**Solutions:**

```bash
#### Resolve migration conflicts
npx prisma migrate resolve --applied "migration-name"

#### Reset database (development only)
npx prisma migrate reset

#### Create and apply migration
npx prisma migrate dev --name "fix-schema"

#### Deploy to production
npx prisma migrate deploy
```

#### Data Consistency Issues

**Symptoms:**
- Duplicate notifications
- Missing foreign key relationships
- Orphaned records

**Diagnostics:**
```sql
-- Find orphaned notifications
SELECT n.id, n.trigger_id
FROM notifications n
LEFT JOIN triggers t ON n.trigger_id = t.id
WHERE t.id IS NULL;

-- Check for duplicates
SELECT recipient, template_id, COUNT(*)
FROM notifications
GROUP BY recipient, template_id
HAVING COUNT(*) > 1;
```

**Solutions:**

```sql
-- Clean up orphaned records
DELETE FROM notifications
WHERE trigger_id NOT IN (SELECT id FROM triggers);

-- Add constraints
ALTER TABLE notifications
ADD CONSTRAINT fk_notification_trigger
FOREIGN KEY (trigger_id) REFERENCES triggers(id) ON DELETE CASCADE;

-- Fix duplicate prevention
CREATE UNIQUE INDEX idx_unique_notification
ON notifications(recipient, template_id, site_id, created_at);
```

#### Queue System Issues

#### Redis Connection Issues

**Symptoms:**
- Queue jobs not processing
- Redis connection errors
- BullMQ dashboard not loading

**Diagnostics:**
```bash
#### Test Redis connectivity
redis-cli -h localhost -p 6379 ping

#### Check Redis memory usage
redis-cli info memory

#### Monitor Redis commands
redis-cli monitor
```

**Solutions:**

```bash
#### Redis configuration tuning
#### In redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
timeout 300

#### Restart Redis
docker restart redis

#### Clear Redis cache if needed
redis-cli flushall
```

```typescript
// Redis connection with retry logic
const redis = new Redis({
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  connectTimeout: 10000,
  commandTimeout: 5000
});
```

#### RabbitMQ Connection Issues

**Symptoms:**
- Messages not being consumed
- Connection timeout errors
- Queue backing up

**Diagnostics:**
```bash
#### Check RabbitMQ status
rabbitmqctl status

#### List queues and their status
rabbitmqctl list_queues name messages consumers

#### Check connections
rabbitmqctl list_connections
```

**Solutions:**

```bash
#### Restart RabbitMQ
docker restart rabbitmq

#### Purge stuck queues
rabbitmqctl purge_queue evaluation_queue

#### Reset RabbitMQ (development only)
rabbitmqctl reset
```

```typescript
// RabbitMQ connection with retry
const connection = await amqp.connect(RABBITMQ_URL, {
  heartbeat: 60,
  connection_timeout: 10000,
  clientProperties: {
    connection_name: 'notification-engine'
  }
});

connection.on('error', (err) => {
  console.error('RabbitMQ connection error:', err);
  // Implement reconnection logic
});
```

#### Queue Job Failures

**Symptoms:**
- Jobs stuck in "failed" state
- High retry counts
- Processing timeouts

**Diagnostics:**
```bash
#### Check failed jobs
curl http://localhost:3000/admin/queues | jq '.queues[].failed'

#### Get job details
curl http://localhost:3000/admin/queues/evaluation/failed
```

**Solutions:**

```typescript
// Improve job error handling
export class NotificationJob {
  async process(data: any) {
    try {
      await this.sendNotification(data);
    } catch (error) {
      if (error.code === 'RATE_LIMIT') {
        // Retry after delay
        throw new Error('Rate limited, retry later');
      } else if (error.code === 'INVALID_EMAIL') {
        // Don't retry for permanent failures
        throw new Error(`Permanent failure: ${error.message}`);
      }
      throw error;
    }
  }
}

// Configure job options
const jobOptions = {
  attempts: 3,
  backoff: {
    type: 'exponential',
    delay: 5000,
  },
  removeOnComplete: 10,
  removeOnFail: 5
};
```

#### Template Issues

#### Template Rendering Errors

**Symptoms:**
- Email/Slack notifications not rendering
- Handlebars compilation errors
- Missing template data

**Diagnostics:**
```bash
#### Test template rendering
curl -X POST http://localhost:3000/admin/test-template \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "weekly-summary",
    "channel": "email",
    "data": {"siteName": "Test"}
  }'
```

**Solutions:**

```typescript
// Add template validation
export const validateTemplateData = (templateId: string, data: any) => {
  const requiredFields = templateRegistry[templateId]?.requiredFields || [];
  const missing = requiredFields.filter(field => !data[field]);

  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
};

// Safe template rendering
export const renderTemplate = async (templateId: string, data: any) => {
  try {
    validateTemplateData(templateId, data);
    return await templateEngine.render(templateId, data);
  } catch (error) {
    console.error(`Template rendering failed for ${templateId}:`, error);
    throw new Error(`Template rendering failed: ${error.message}`);
  }
};
```

#### Missing Template Files

**Symptoms:**
- "Template not found" errors
- 404 errors for template endpoints
- Blank notification content

**Diagnostics:**
```bash
#### Check template files
ls -la src/new-templates/
ls -la src/slack-templates/

#### Verify template registry
node -e "console.log(require('./dist/template-registry').templateRegistry)"
```

**Solutions:**

```bash
#### Copy missing templates
cp -r templates/* src/new-templates/
cp -r slack-templates/* src/slack-templates/

#### Rebuild with templates
npm run build
```

```typescript
// Add template existence check
const checkTemplateExists = (templateId: string, channel: string) => {
  const templatePath = path.join(
    __dirname,
    `../templates/${channel}`,
    `${templateId}.hbs`
  );

  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template not found: ${templateId} for ${channel}`);
  }
};
```

#### API Issues

#### Authentication Problems

**Symptoms:**
- 401 Unauthorized errors
- API key validation failures
- Session timeout issues

**Diagnostics:**
```bash
#### Test API authentication
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/triggers

#### Check token validity
curl -X POST http://localhost:3000/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Solutions:**

```typescript
// Improve auth middleware
export const authenticateRequest = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Missing authorization token' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Auth error:', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};
```

#### Rate Limiting Issues

**Symptoms:**
- 429 Too Many Requests errors
- API calls being blocked
- Legitimate traffic getting limited

**Diagnostics:**
```bash
#### Check rate limit headers
curl -I http://localhost:3000/api/triggers

#### Monitor rate limit metrics
curl http://localhost:3000/metrics | grep rate_limit
```

**Solutions:**

```typescript
// Configurable rate limiting
import rateLimit from 'express-rate-limit';

const createRateLimit = (windowMs: number, max: number) =>
  rateLimit({
    windowMs,
    max,
    message: 'Too many requests, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  });

// Different limits for different endpoints
app.use('/api/triggers', createRateLimit(15 * 60 * 1000, 100)); // 100 requests per 15 minutes
app.use('/real-time', createRateLimit(60 * 1000, 10)); // 10 requests per minute
```

#### Performance Issues

#### High CPU Usage

**Symptoms:**
- Container using > 80% CPU
- Slow API responses
- Queue processing delays

**Diagnostics:**
```bash
#### Monitor CPU usage
docker stats notification-engine --no-stream

#### Profile Node.js application
docker exec notification-engine node --prof dist/server.js

#### Analyze CPU usage patterns
top -p $(docker inspect -f '{{.State.Pid}}' notification-engine)
```

**Solutions:**

```typescript
// Add CPU monitoring
const os = require('os');

setInterval(() => {
  const load = os.loadavg();
  if (load[0] > os.cpus().length) {
    console.warn('High CPU load detected:', load);
  }
}, 30000);

// Optimize heavy operations
const processInBatches = async (items: any[], batchSize = 10) => {
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    await Promise.all(batch.map(processItem));

    // Allow event loop to process other tasks
    await new Promise(resolve => setImmediate(resolve));
  }
};
```

#### Memory Leaks

**Symptoms:**
- Steadily increasing memory usage
- Out of memory errors
- Container restarts

**Diagnostics:**
```bash
#### Track memory over time
while true; do
  docker stats notification-engine --no-stream | grep notification-engine
  sleep 60
done

#### Generate heap snapshot
kill -USR2 $(docker inspect -f '{{.State.Pid}}' notification-engine)
```

**Solutions:**

```typescript
// Add memory monitoring
const monitorMemory = () => {
  const usage = process.memoryUsage();
  const threshold = 800 * 1024 * 1024; // 800MB

  if (usage.heapUsed > threshold) {
    console.warn('Memory usage high:', usage);

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }
};

setInterval(monitorMemory, 60000);

// Proper event listener cleanup
const cleanup = () => {
  // Remove event listeners
  process.removeAllListeners();

  // Close database connections
  prisma.$disconnect();

  // Close Redis connections
  redis.disconnect();
};

process.on('SIGTERM', cleanup);
process.on('SIGINT', cleanup);
```

#### Security Issues

#### API Security

**Symptoms:**
- Unauthorized access attempts
- SQL injection attempts
- Brute force attacks

**Diagnostics:**
```bash
#### Check access logs
docker logs notification-engine | grep -E "(401|403|429)"

#### Monitor for suspicious activity
docker logs notification-engine | grep -E "DROP TABLE|UNION SELECT|<script"
```

**Solutions:**

```typescript
// Security middleware stack
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import validator from 'validator';

app.use(helmet());
app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100
}));

// Input validation
const validateInput = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize strings
  for (const [key, value] of Object.entries(req.body)) {
    if (typeof value === 'string') {
      req.body[key] = validator.escape(value);
    }
  }
  next();
};

app.use(validateInput);
```

#### Environment Security

**Symptoms:**
- Exposed sensitive information
- Weak authentication
- Unencrypted connections

**Solutions:**

```bash
#### Use proper secrets management
export DATABASE_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/db/password --query SecretString --output text)

#### Enable SSL/TLS
export DATABASE_URL="mysql://user:password@host:3306/db?ssl=true&sslcert=/path/to/cert.pem"

#### Rotate secrets regularly
aws secretsmanager rotate-secret --secret-id prod/api/keys
```

#### Monitoring & Debugging

#### Application Monitoring

```typescript
// Advanced logging setup
import winston from 'winston';

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info({
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
  });

  next();
});
```

#### Performance Monitoring

```typescript
// Performance tracking
const performanceTracker = {
  track: (operation: string, fn: Function) => {
    return async (...args: any[]) => {
      const start = process.hrtime.bigint();

      try {
        const result = await fn(...args);
        const end = process.hrtime.bigint();
        const duration = Number(end - start) / 1000000; // Convert to milliseconds

        logger.info({
          operation,
          duration,
          status: 'success'
        });

        return result;
      } catch (error) {
        const end = process.hrtime.bigint();
        const duration = Number(end - start) / 1000000;

        logger.error({
          operation,
          duration,
          status: 'error',
          error: error.message
        });

        throw error;
      }
    };
  }
};

// Usage
const sendEmail = performanceTracker.track('sendEmail', async (data) => {
  // Email sending logic
});
```

#### Frequently Asked Questions

#### Q: Why are notifications not being sent?

**A:** Check the following in order:
1. Verify `EMAIL_ON=true` in environment variables
2. Check SMTP configuration and credentials
3. Verify queue processing is running
4. Check for failed jobs in the queue dashboard
5. Verify template exists and renders correctly

#### Q: How do I add a new notification template?

**A:** Follow these steps:
1. Create template files in `src/new-templates/` (email) and `src/slack-templates/` (Slack)
2. Add template definition to `template-registry.ts`
3. Define validation schema in the registry
4. Test template rendering
5. Deploy and verify

#### Q: Why is the queue processing slowly?

**A:** Common causes:
1. Redis memory exhaustion - check with `redis-cli info memory`
2. Database connection pool exhaustion
3. Rate limiting from external services (SMTP, Slack)
4. Insufficient worker processes
5. Resource constraints (CPU/memory)

#### Q: How do I scale the application?

**A:** Scaling options:
1. **Horizontal scaling**: Deploy multiple instances behind a load balancer
2. **Database scaling**: Use read replicas for read-heavy operations
3. **Queue scaling**: Use Redis Cluster and multiple RabbitMQ nodes
4. **Caching**: Implement Redis caching for frequently accessed data

#### Q: How do I backup and restore data?

**A:** Backup procedures:
```bash
#### Database backup
mysqldump -h $DB_HOST -u $DB_USER -p$DB_PASSWORD notifications > backup.sql

#### Redis backup
redis-cli --rdb backup.rdb

#### Restore database
mysql -h $DB_HOST -u $DB_USER -p$DB_PASSWORD notifications < backup.sql
```

#### Q: How do I monitor application health?

**A:** Use these endpoints and tools:
- `/up` - Basic health check
- `/health` - Detailed health with service status
- `/admin/queues` - Queue monitoring
- `/metrics` - Prometheus metrics
- Docker logs and stats for resource monitoring

#### Q: What's the disaster recovery procedure?

**A:** Recovery steps:
1. **Assess the situation**: Check logs and monitoring alerts
2. **Scale up resources**: Increase container count if needed
3. **Database recovery**: Restore from backup if corrupted
4. **Queue recovery**: Purge stuck queues and restart processing
5. **Verify functionality**: Run health checks and test notifications
6. **Monitor closely**: Watch for recurring issues

#### Q: How do I update to a new version?

**A:** Update procedure:
1. **Test in staging**: Deploy to staging environment first
2. **Backup data**: Create database and Redis backups
3. **Run migrations**: Apply any database schema changes
4. **Deploy gradually**: Use blue-green or rolling deployment
5. **Monitor health**: Watch logs and metrics during deployment
6. **Rollback if needed**: Have rollback plan ready

This troubleshooting guide should help you diagnose and resolve most issues with the Notification Engine System. For complex issues, consider enabling debug logging and consulting the application logs for more detailed information.

*Source: [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)*


---

---

*Generated by Documentation Generator v1.0.0*
*Generated on: 2025-06-18T10:50:16.040Z*
