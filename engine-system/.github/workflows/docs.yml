name: Documentation Generation

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'engine-system/docs/**'
      - 'engine-system/README.md'
      - 'engine-system/docs-config.json'
      - 'engine-system/scripts/docs-*.ts'
      - '.github/workflows/docs.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'engine-system/docs/**'
      - 'engine-system/README.md'
      - 'engine-system/docs-config.json'
      - 'engine-system/scripts/docs-*.ts'
  workflow_dispatch:
    inputs:
      deploy_to_pages:
        description: 'Deploy to GitHub Pages'
        required: false
        default: false
        type: boolean

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  generate-docs:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: engine-system/package-lock.json
        
    - name: Install dependencies
      working-directory: engine-system
      run: npm ci
      
    - name: Generate documentation
      working-directory: engine-system
      run: |
        echo "📚 Generating documentation..."
        npm run docs:build
        
    - name: Validate generated files
      working-directory: engine-system
      run: |
        echo "🔍 Validating generated documentation..."
        
        # Check if merged documentation was generated
        if [ ! -f "DOCUMENTATION.md" ]; then
          echo "❌ DOCUMENTATION.md was not generated"
          exit 1
        fi
        
        # Check if site was generated
        if [ ! -d "docs-site" ]; then
          echo "❌ docs-site directory was not generated"
          exit 1
        fi
        
        # Check if index.html exists
        if [ ! -f "docs-site/index.html" ]; then
          echo "❌ docs-site/index.html was not generated"
          exit 1
        fi
        
        # Check if assets were generated
        if [ ! -d "docs-site/assets" ]; then
          echo "❌ docs-site/assets directory was not generated"
          exit 1
        fi
        
        echo "✅ All documentation files generated successfully"
        
        # Show file sizes
        echo "📊 Generated file sizes:"
        du -sh DOCUMENTATION.md docs-site/
        
        # Count generated pages
        PAGE_COUNT=$(find docs-site -name "*.html" | wc -l)
        echo "📄 Generated $PAGE_COUNT HTML pages"
        
    - name: Upload merged documentation artifact
      uses: actions/upload-artifact@v4
      with:
        name: merged-documentation
        path: engine-system/DOCUMENTATION.md
        retention-days: 30
        
    - name: Upload documentation site artifact
      uses: actions/upload-artifact@v4
      with:
        name: documentation-site
        path: engine-system/docs-site/
        retention-days: 30

  deploy-to-pages:
    if: github.ref == 'refs/heads/main' && (github.event_name == 'push' || github.event.inputs.deploy_to_pages == 'true')
    needs: generate-docs
    runs-on: ubuntu-latest
    
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
      
    steps:
    - name: Download documentation site
      uses: actions/download-artifact@v4
      with:
        name: documentation-site
        path: ./docs-site
        
    - name: Setup Pages
      uses: actions/configure-pages@v4
      
    - name: Upload to GitHub Pages
      uses: actions/upload-pages-artifact@v3
      with:
        path: ./docs-site
        
    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4

  check-links:
    needs: generate-docs
    runs-on: ubuntu-latest
    
    steps:
    - name: Download documentation site
      uses: actions/download-artifact@v4
      with:
        name: documentation-site
        path: ./docs-site
        
    - name: Check internal links
      run: |
        echo "🔗 Checking internal links in documentation..."
        
        # Simple link checker for HTML files
        find ./docs-site -name "*.html" -exec grep -l "href=" {} \; | while read file; do
          echo "Checking links in: $file"
          
          # Extract relative links and check if target files exist
          grep -o 'href="[^"]*"' "$file" | grep -v '^href="http' | grep -v '^href="#' | while read link; do
            # Remove href=" and trailing "
            target=$(echo "$link" | sed 's/href="//g' | sed 's/"//g')
            
            # Convert relative path to absolute path
            dir=$(dirname "$file")
            target_path="$dir/$target"
            
            if [ ! -f "$target_path" ]; then
              echo "⚠️  Broken link in $file: $target (target: $target_path)"
            fi
          done
        done
        
        echo "✅ Link check completed"

  notify-completion:
    if: always()
    needs: [generate-docs, deploy-to-pages, check-links]
    runs-on: ubuntu-latest
    
    steps:
    - name: Notify completion
      run: |
        if [ "${{ needs.generate-docs.result }}" == "success" ]; then
          echo "✅ Documentation generation completed successfully"
        else
          echo "❌ Documentation generation failed"
        fi
        
        if [ "${{ needs.deploy-to-pages.result }}" == "success" ]; then
          echo "🌐 Documentation deployed to GitHub Pages"
        elif [ "${{ needs.deploy-to-pages.result }}" == "skipped" ]; then
          echo "⏭️  GitHub Pages deployment skipped"
        else
          echo "❌ GitHub Pages deployment failed"
        fi
        
        if [ "${{ needs.check-links.result }}" == "success" ]; then
          echo "🔗 Link validation passed"
        else
          echo "⚠️  Link validation had issues"
        fi
